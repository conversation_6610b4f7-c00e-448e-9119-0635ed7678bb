prefix=${pcfiledir}/../..
# Copyright (C) 2010, 2011
# <PERSON> <<EMAIL>>
#
# Copying and distribution of this file, with or without modification, are
# permitted in any medium without royalty provided the copyright notice and this
# notice are preserved. This file is offered as-is, without any warranty.

exec_prefix=${prefix}
libdir=${exec_prefix}/lib
includedir=${prefix}/include

Name: gta
Description: Library to read and write Generic Tagged Arrays (GTAs)
URL: http://gta.nongnu.org/
Version: 1.0.8
Libs: "-L${libdir}" -lgta
Libs.private:  -loptimized "-l${prefix}/lib/lzma.lib" -ldebug "-l${prefix}/debug/lib/lzma.lib" -loptimized "-l${prefix}/lib/bz2.lib" -ldebug "-l${prefix}/debug/lib/bz2d.lib" -loptimized "-l${prefix}/lib/zlib.lib" -ldebug "-l${prefix}/debug/lib/zlibd.lib"  
Cflags: "-I${includedir}"

