/**********************************************************************
 *
 * GEOS - Geometry Engine Open Source
 * http://geos.osgeo.org
 *
 * Copyright (C) 2020 Sandro Santilli <<EMAIL>>
 *
 * This is free software; you can redistribute and/or modify it under
 * the terms of the GNU Lesser General Public Licence as published
 * by the Free Software Foundation.
 * See the COPYING file for more information.
 *
 *
 **********************************************************************
 *
 * Last port: ORIGINAL WORK
 *
 **********************************************************************/

#pragma once

namespace geos {
namespace geom { // geos::geom
namespace prep { // geos::geom::prep

class PreparedLineString;

class PreparedLineStringDistance {
public:

    static double distance(const PreparedLineString& prep, const geom::Geometry* geom)
    {
        PreparedLineStringDistance op(prep);
        return op.distance(geom);
    }

    PreparedLineStringDistance(const PreparedLineString& prep)
        : prepLine(prep)
    { }

    double distance(const geom::Geometry* g) const;

    bool isWithinDistance(const geom::Geometry* g, double d) const;

protected:

    const PreparedLineString& prepLine;

    // Declare type as noncopyable
    PreparedLineStringDistance(const PreparedLineStringDistance& other) = delete;
    PreparedLineStringDistance& operator=(const PreparedLineStringDistance& rhs) = delete;
};

} // namespace geos::geom::prep
} // namespace geos::geom
} // namespace geos

