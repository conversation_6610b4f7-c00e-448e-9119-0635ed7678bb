/* osgEarth
* Copyright 2025 Pelican Mapping
* MIT License
*/
#ifndef OSGEARTH_TIME_CONTROL_H
#define OSGEARTH_TIME_CONTROL_H 1

#include <osgEarth/Common>
#include <osg/FrameStamp>
#include <vector>
#include <string>

namespace osgEarth
{
    /**
     * Information about one of the frames in a sequence.
     */
    struct SequenceFrameInfo
    {
        std::string timeIdentifier;
    };


    /**
     * Interface to an object that is subject to time controls.
     * Pure virtual.
     */
    class SequenceControl
    {
    public:
        /** Whether the implementation supports these methods */
        virtual bool supportsSequenceControl() const =0;

        /** Starts playback */
        virtual void playSequence() =0;

        /** Stops playback */
        virtual void pauseSequence() =0;

        /** Seek to a specific frame */
        virtual void seekToSequenceFrame(unsigned frame) =0;

        /** Whether the object is in playback mode */
        virtual bool isSequencePlaying() const =0;

        /** Gets data about the current frame in the sequence */
        virtual const std::vector<SequenceFrameInfo>& getSequenceFrameInfo() const =0;

        /** Gets the index of the current frame. */
        virtual int getCurrentSequenceFrameIndex( const osg::FrameStamp* ) const =0;
    };
}

#endif // OSGEARTH_TIME_CONTROL_H
