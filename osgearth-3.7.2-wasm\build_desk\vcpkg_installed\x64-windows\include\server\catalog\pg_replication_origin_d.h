/*-------------------------------------------------------------------------
 *
 * pg_replication_origin_d.h
 *    Macro definitions for pg_replication_origin
 *
 * Portions Copyright (c) 1996-2023, PostgreSQL Global Development Group
 * Portions Copyright (c) 1994, Regents of the University of California
 *
 * NOTES
 *  ******************************
 *  *** DO NOT EDIT THIS FILE! ***
 *  ******************************
 *
 *  It has been GENERATED by src/backend/catalog/genbki.pl
 *
 *-------------------------------------------------------------------------
 */
#ifndef PG_REPLICATION_ORIGIN_D_H
#define PG_REPLICATION_ORIGIN_D_H

#define ReplicationOriginRelationId 6000
#define PgReplicationOriginToastTable 4181
#define PgReplicationOriginToastIndex 4182
#define ReplicationOriginIdentIndex 6001
#define ReplicationOriginNameIndex 6002

#define Anum_pg_replication_origin_roident 1
#define Anum_pg_replication_origin_roname 2

#define Natts_pg_replication_origin 2


#endif							/* PG_REPLICATION_ORIGIN_D_H */
