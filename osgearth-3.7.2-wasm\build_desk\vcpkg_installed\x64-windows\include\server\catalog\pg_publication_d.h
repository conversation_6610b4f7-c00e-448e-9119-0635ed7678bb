/*-------------------------------------------------------------------------
 *
 * pg_publication_d.h
 *    Macro definitions for pg_publication
 *
 * Portions Copyright (c) 1996-2023, PostgreSQL Global Development Group
 * Portions Copyright (c) 1994, Regents of the University of California
 *
 * NOTES
 *  ******************************
 *  *** DO NOT EDIT THIS FILE! ***
 *  ******************************
 *
 *  It has been GENERATED by src/backend/catalog/genbki.pl
 *
 *-------------------------------------------------------------------------
 */
#ifndef PG_PUBLICATION_D_H
#define PG_PUBLICATION_D_H

#define PublicationRelationId 6104
#define PublicationObjectIndexId 6110
#define PublicationNameIndexId 6111

#define Anum_pg_publication_oid 1
#define Anum_pg_publication_pubname 2
#define Anum_pg_publication_pubowner 3
#define Anum_pg_publication_puballtables 4
#define Anum_pg_publication_pubinsert 5
#define Anum_pg_publication_pubupdate 6
#define Anum_pg_publication_pubdelete 7
#define Anum_pg_publication_pubtruncate 8
#define Anum_pg_publication_pubviaroot 9

#define Natts_pg_publication 9


#endif							/* PG_PUBLICATION_D_H */
