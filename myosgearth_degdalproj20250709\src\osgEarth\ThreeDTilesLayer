/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */

#ifndef OSGEARTH_THREED_TILES_LAYER_H
#define OSGEARTH_THREED_TILES_LAYER_H 1

#include <osgEarth/Common>
#include <osgEarth/VisibleLayer>
#include <osgEarth/ShaderUtils>
#include <osgEarth/TDTiles>
#include <osgEarth/URI>
#include <osgEarth/Threading>

namespace osgEarth { namespace Contrib
{    
    /**
     * Layer that contains a 3D tiles tileset
     */
    class OSGEARTH_EXPORT ThreeDTilesLayer : public VisibleLayer
    {
    public: // serialization
        class OSGEARTH_EXPORT Options : public VisibleLayer::Options {
        public:        
            META_LayerOptions(osgEarth, Options, VisibleLayer::Options);
            OE_OPTION(URI, url);
            OE_OPTION(float, maximumScreenSpaceError);
            virtual Config getConfig() const;
        private:
            void fromConfig( const Config& conf );
        };

    public:
        META_Layer(osgEarth, ThreeDTilesLayer, Options, VisibleLayer, ThreeDTiles);

    public:
        //! URL from which to load the tileset from        
        void setURL(const URI& url);
        const URI& getURL() const;

        //! The maximum screen space error for this layer
        float getMaximumScreenSpaceError() const;
        void setMaximumScreenSpaceError(float maximumScreenSpaceError);

        osgEarth::Contrib::ThreeDTiles::ThreeDTilesetNode* getTilesetNode() {
            return _tilesetNode.get();
        }

    public: // Layer

        //! Open the layer and return its status
        virtual Status openImplementation();

        //! Node created by this model layer
        virtual osg::Node* getNode() const;

    protected: // Layer

        //! post-ctor initialization
        virtual void init();

    protected:

        virtual ~ThreeDTilesLayer();

        osg::ref_ptr<osgEarth::Contrib::ThreeDTiles::ThreeDTilesetNode> _tilesetNode;
    };
} }

OSGEARTH_SPECIALIZE_CONFIG(osgEarth::Contrib::ThreeDTilesLayer::Options);

#endif // OSGEARTH_THREED_TILES_LAYER_H
