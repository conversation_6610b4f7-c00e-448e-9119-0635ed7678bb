/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */

#include <osgEarth/SpatialReference>
#include <osgEarth/Registry>
#include <osgEarth/Cube>
#include <osgEarth/LocalTangentPlane>
#include <osgEarth/Math>
#include <osgEarth/StringUtils>
#include "GeographicLibAdapter.h"
#include <osg/Vec3d>
#include <iostream>
#include <cmath>
#include <algorithm>
#include <unordered_map>

#define LC "[SpatialReference] "

using namespace osgEarth;

// 自定义错误代码，替代GDAL错误代码
enum CustomErrorCode
{
    CUSTOM_ERR_NONE = 0,
    CUSTOM_ERR_INVALID_HANDLE = 1,
    CUSTOM_ERR_INVALID_INPUT = 2,
    CUSTOM_ERR_TRANSFORMATION_FAILED = 3
};

// 自定义空间参考句柄结构
struct CustomSpatialRefHandle
{
    std::string name;
    std::string proj4String;
    std::string wktString;
    std::string datum;
    double semiMajorAxis;
    double semiMinorAxis;
    double linearUnits;
    bool isGeographic;
    bool isProjected;
    std::string projection;
    std::unordered_map<std::string, std::string> attributes;

    CustomSpatialRefHandle() : semiMajorAxis(6378137.0),        // WGS84默认值
                               semiMinorAxis(6356752.31424518), // WGS84默认值
                               linearUnits(1.0),
                               isGeographic(false),
                               isProjected(false)
    {
    }
};

// 坐标变换句柄结构
struct CustomTransformHandle
{
    std::shared_ptr<CustomSpatialRefHandle> fromSRS;
    std::shared_ptr<CustomSpatialRefHandle> toSRS;
    bool isValid;

    CustomTransformHandle() : isValid(false) {}
};

namespace
{
    // 全局错误消息
    std::string g_lastErrorMsg = "";

    // 存储所有创建的空间参考句柄
    std::unordered_map<void *, std::shared_ptr<CustomSpatialRefHandle>> g_spatialRefHandles;
    std::unordered_map<void *, std::shared_ptr<CustomTransformHandle>> g_transformHandles;

    // 句柄计数器
    static size_t g_handleCounter = 1000;

    // 创建新的句柄
    void *createNewHandle()
    {
        return reinterpret_cast<void *>(++g_handleCounter);
    }

    // 解析PROJ4字符串中的参数
    std::unordered_map<std::string, std::string> parseProj4(const std::string &proj4String)
    {
        std::unordered_map<std::string, std::string> params;

        // 简单的PROJ4字符串解析
        std::string cleanString = proj4String;
        std::replace(cleanString.begin(), cleanString.end(), '+', ' ');

        std::stringstream ss(cleanString);
        std::string token;

        while (ss >> token)
        {
            if (token.empty())
                continue;

            size_t pos = token.find('=');
            if (pos != std::string::npos)
            {
                std::string key = token.substr(0, pos);
                std::string value = token.substr(pos + 1);
                params[key] = value;
            }
            else
            {
                params[token] = "true";
            }
        }

        return params;
    }

    // 预定义的常见坐标系统
    std::shared_ptr<CustomSpatialRefHandle> createWGS84Geographic()
    {
        auto handle = std::make_shared<CustomSpatialRefHandle>();
        handle->name = "WGS84";
        handle->proj4String = "+proj=longlat +ellps=WGS84 +datum=WGS84 +no_defs";
        handle->wktString = "GEOGCS[\"WGS 84\",DATUM[\"WGS_1984\",SPHEROID[\"WGS 84\",6378137,298.257223563]],PRIMEM[\"Greenwich\",0],UNIT[\"degree\",0.0174532925199433]]";
        handle->datum = "WGS84";
        handle->semiMajorAxis = 6378137.0;
        handle->semiMinorAxis = 6356752.31424518;
        handle->linearUnits = 1.0;
        handle->isGeographic = true;
        handle->isProjected = false;
        handle->attributes["GEOGCS"] = "WGS 84";
        handle->attributes["DATUM"] = "WGS_1984";
        handle->attributes["SPHEROID"] = "WGS 84";
        handle->attributes["UNIT"] = "degree";
        return handle;
    }

    std::shared_ptr<CustomSpatialRefHandle> createSphericalMercator()
    {
        auto handle = std::make_shared<CustomSpatialRefHandle>();
        handle->name = "Spherical Mercator";
        handle->proj4String = "+proj=webmerc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +towgs84=0,0,0,0,0,0,0 +wktext +no_defs";
        handle->datum = "WGS84";
        handle->semiMajorAxis = 6378137.0;
        handle->semiMinorAxis = 6378137.0; // 球形墨卡托
        handle->linearUnits = 1.0;
        handle->isGeographic = false;
        handle->isProjected = true;
        handle->projection = "mercator";
        handle->attributes["PROJCS"] = "Spherical Mercator";
        handle->attributes["PROJECTION"] = "Mercator";
        handle->attributes["UNIT"] = "metre";
        return handle;
    }

    // 获取属性值的实现
    std::string
    getOGRAttrValue(void *_handle, const std::string &name, int child_num = 0, bool lowercase = false)
    {
        if (!_handle)
            return "";

        auto it = g_spatialRefHandles.find(_handle);
        if (it == g_spatialRefHandles.end())
            return "";

        auto handle = it->second;
        auto attrIt = handle->attributes.find(name);
        if (attrIt != handle->attributes.end())
        {
            return lowercase ? toLower(attrIt->second) : attrIt->second;
        }

        return "";
    }

    // 将CustomSpatialRefHandle转换为GeographicLibAdapter::ProjectionParams
    GeographicLibAdapter::ProjectionParams toProjectionParams(const std::shared_ptr<CustomSpatialRefHandle> &handle)
    {
        GeographicLibAdapter::ProjectionParams params;

        if (handle->isGeographic)
        {
            params.type = GeographicLibAdapter::GEOGRAPHIC;
        }
        else if (handle->isProjected)
        {
            if (handle->projection == "mercator")
            {
                params.type = GeographicLibAdapter::WEB_MERCATOR;
            }
            else if (handle->projection == "utm")
            {
                params.type = GeographicLibAdapter::UTM;
                // 从PROJ4字符串中解析UTM参数
                auto proj4Params = parseProj4(handle->proj4String);
                auto zoneIt = proj4Params.find("zone");
                if (zoneIt != proj4Params.end())
                {
                    params.utmZone = std::stoi(zoneIt->second);
                }
                params.isNorthern = proj4Params.find("south") == proj4Params.end();
            }
            else if (handle->projection == "tmerc" || handle->projection == "transverse_mercator")
            {
                params.type = GeographicLibAdapter::TRANSVERSE_MERCATOR;
                // 从PROJ4字符串中解析横轴墨卡托参数
                auto proj4Params = parseProj4(handle->proj4String);
                auto lon0It = proj4Params.find("lon_0");
                if (lon0It != proj4Params.end())
                {
                    params.centralMeridian = std::stod(lon0It->second);
                }
                auto x0It = proj4Params.find("x_0");
                if (x0It != proj4Params.end())
                {
                    params.falseEasting = std::stod(x0It->second);
                }
                auto y0It = proj4Params.find("y_0");
                if (y0It != proj4Params.end())
                {
                    params.falseNorthing = std::stod(y0It->second);
                }
                auto kIt = proj4Params.find("k");
                if (kIt != proj4Params.end())
                {
                    params.scaleFactor = std::stod(kIt->second);
                }
            }
            else
            {
                params.type = GeographicLibAdapter::UNKNOWN;
            }
        }
        else
        {
            params.type = GeographicLibAdapter::UNKNOWN;
        }

        return params;
    }

    // 基础的坐标变换实现，使用GeographicLib适配器
    bool performBasicTransform(const std::shared_ptr<CustomSpatialRefHandle> &fromSRS,
                               const std::shared_ptr<CustomSpatialRefHandle> &toSRS,
                               double &x, double &y, double &z)
    {
        // 如果源和目标相同，不需要变换
        if (fromSRS == toSRS)
            return true;

        // 转换为GeographicLib参数
        auto fromParams = toProjectionParams(fromSRS);
        auto toParams = toProjectionParams(toSRS);

        // 使用GeographicLib适配器进行转换
        auto result = GeographicLibAdapter::transform(x, y, z, fromParams, toParams);

        if (result.success)
        {
            x = result.x;
            y = result.y;
            z = result.z;
            return true;
        }

        return false;
    }

    void geodeticToGeocentric(std::vector<osg::Vec3d> &points, const Ellipsoid &em)
    {
        for (unsigned i = 0; i < points.size(); ++i)
        {
            points[i] = em.geodeticToGeocentric(points[i]);
        }
    }

    void geocentricToGeodetic(std::vector<osg::Vec3d> &points, const Ellipsoid &em)
    {
        for (unsigned i = 0; i < points.size(); ++i)
        {
            points[i] = em.geocentricToGeodetic(points[i]);
        }
    }

    // Make a MatrixTransform suitable for use with a Locator object based on the given extents.
    // Calling Locator::setTransformAsExtents doesn't work with OSG 2.6 due to the fact that the
    // _inverse member isn't updated properly.  Calling Locator::setTransform works correctly.
    osg::Matrixd
    getTransformFromExtents(double minX, double minY, double maxX, double maxY)
    {
        osg::Matrixd transform;
        transform.set(
            maxX - minX, 0.0, 0.0, 0.0,
            0.0, maxY - minY, 0.0, 0.0,
            0.0, 0.0, 1.0, 0.0,
            minX, minY, 0.0, 1.0);
        return transform;
    }
}

//------------------------------------------------------------------------

SpatialReference::ThreadLocal::ThreadLocal() : _handle(nullptr),
                                               _workspace(nullptr),
                                               _workspaceSize(0u),
                                               _threadId(std::this_thread::get_id())
{
    // nop
}

SpatialReference::ThreadLocal::~ThreadLocal()
{
    if (_workspace)
        delete[] _workspace;

    //    // Causing a crash under GDAL3/PROJ6 - comment out until further notice
    //    // This only happens on program exist anyway
    // #if GDAL_VERSION_MAJOR < 3
    //    for(auto& xformEntry : _xformCache)
    //    {
    //        optional<TransformInfo>& ti = xformEntry.second;
    //        if (ti.isSet() && ti->_handle != nullptr)
    //            OCTDestroyCoordinateTransformation(ti->_handle);
    //    }
    // #endif
    //
    //    if (_handle)
    //    {
    //        OSRDestroySpatialReference(static_cast<OGRSpatialReferenceH>(_handle));
    //    }
}

SpatialReference *
SpatialReference::create(const std::string &horiz, const std::string &vert)
{
    osg::ref_ptr<SpatialReference> srs = Registry::instance()->getOrCreateSRS(Key(horiz, vert));
    return srs.release();
}

SpatialReference *
SpatialReference::createFromKey(const SpatialReference::Key &key)
{
    osg::ref_ptr<SpatialReference> srs;

    if (key.horizLower == "unified-cube")
        srs = new Contrib::CubeSpatialReference(key);
    else
        srs = new SpatialReference(key);

    return (srs.valid() && srs->valid()) ? srs.release() : nullptr;
}

SpatialReference::SpatialReference(void *handle) : _valid(true),
                                                   _initialized(false),
                                                   _domain(GEOGRAPHIC),
                                                   _is_mercator(false),
                                                   _is_north_polar(false),
                                                   _is_south_polar(false),
                                                   _is_cube(false),
                                                   _is_user_defined(false),
                                                   _is_ltp(false),
                                                   _is_spherical_mercator(false),
                                                   _ellipsoidId(0u)
{
    _setup.srcHandle = handle;

    // force handle creation
    init();
}

SpatialReference::SpatialReference(const Key &key) : _valid(true),
                                                     _initialized(false),
                                                     _domain(GEOGRAPHIC),
                                                     _is_mercator(false),
                                                     _is_north_polar(false),
                                                     _is_south_polar(false),
                                                     _is_cube(false),
                                                     _is_user_defined(false),
                                                     _is_ltp(false),
                                                     _is_spherical_mercator(false),
                                                     _ellipsoidId(0u)
{
    // shortcut for spherical-mercator:
    // https://wiki.openstreetmap.org/wiki/EPSG:3857
    if (key.horizLower == "spherical-mercator" ||
        key.horizLower == "global-mercator" ||
        key.horizLower == "web-mercator" ||
        key.horizLower == "epsg:3857" ||
        key.horizLower == "epsg:900913" ||
        key.horizLower == "epsg:102100" ||
        key.horizLower == "epsg:102113" ||
        key.horizLower == "epsg:3785" ||
        key.horizLower == "epsg:3587" ||
        key.horizLower == "osgeo:41001")
    {
        // note the use of nadgrids=@null (see http://proj.maptools.org/faq.html)
        _setup.name = "Spherical Mercator";
        _setup.type = INIT_PROJ;
#if (GDAL_VERSION_MAJOR >= 3)
        _setup.horiz = "+proj=webmerc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +towgs84=0,0,0,0,0,0,0 +wktext +no_defs";
#else
        _setup.horiz = "+proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +towgs84=0,0,0,0,0,0,0 +wktext +no_defs";
#endif
        _setup.vert = key.vertLower;
    }

    // true ellipsoidal ("world") mercator:
    // https://epsg.io/3395
    // https://gis.stackexchange.com/questions/259121/transformation-functions-for-epsg3395-projection-vs-epsg3857
    else if (
        key.horizLower == "world-mercator" ||
        key.horizLower == "epsg:3395" ||
        key.horizLower == "epsg:54004" ||
        key.horizLower == "epsg:9804" ||
        key.horizLower == "epsg:3832")
    {
        _setup.name = "World Mercator (WGS84)";
        _setup.type = INIT_PROJ;
        _setup.horiz = "+proj=merc +lon_0=0 +k=1 +x_0=0 +y_0=0 +ellps=WGS84 +datum=WGS84 +units=m +no_defs";
        _setup.vert = key.vertLower;
    }

    // common WGS84:
    else if (
        key.horizLower == "epsg:4326" ||
        key.horizLower == "wgs84" ||
        key.horizLower == "wgs_1984")
    {
        _setup.name = "WGS84";
        _setup.type = INIT_PROJ;
        _setup.horiz = "+proj=longlat +ellps=WGS84 +datum=WGS84 +no_defs";
        _setup.vert = key.vertLower;
    }

    // WGS84 Plate Carre:
    else if (
        key.horizLower == "plate-carre" ||
        key.horizLower == "plate-carree")
    {
        // https://proj4.org/operations/projections/eqc.html
        _setup.name = "Plate Carree";
        _setup.type = INIT_PROJ;
        _setup.horiz = "+proj=eqc +lat_ts=0 +lat_0=0 +lon_0=0 +x_0=0 +y_0=0 +units=m +ellps=WGS84 +datum=WGS84 +no_defs";
        _setup.vert = key.vertLower;
    }

    else if (
        key.horizLower == "moon" ||
        key.horizLower == "esri104903")
    {
        _setup.name = "Moon";
        _setup.type = INIT_PROJ;
        _setup.horiz = "+proj=longlat +R=1737400 +no_defs +units=m +type=crs";
        _setup.vert = key.vertLower;
    }

    else if (
        key.horizLower.find("+proj=longlat") == 0 &&
        key.horizLower.find("+R=1737400") != std::string::npos)
    {
        _setup.name = "Moon";
        _setup.type = INIT_PROJ;
        _setup.horiz = key.horiz;
        _setup.vert = key.vertLower;
    }

    // custom srs for the unified cube
    else if (
        key.horizLower == "unified-cube")
    {
        _setup.name = "Unified Cube";
        _setup.type = INIT_USER;
        _setup.horiz = "+proj=longlat +ellps=WGS84 +datum=WGS84 +no_defs";
        _is_cube = true;
    }

    else if (
        key.horizLower.find('+') == 0)
    {
        //_setup.name = key.horiz;
        _setup.type = INIT_PROJ;
        _setup.horiz = key.horiz;
    }
    else if (
        key.horizLower.find("epsg:") == 0 ||
        key.horizLower.find("osgeo:") == 0)
    {
        _setup.name = key.horiz;
        _setup.type = INIT_PROJ;
        _setup.horiz = std::string("+init=") + key.horiz;
    }
    else if (
        key.horizLower.find("projcs") == 0 ||
        key.horizLower.find("geogcs") == 0 ||
        key.horizLower.find("geoccs") == 0)
    {
        //_setup.name = key.horiz;
        _setup.type = INIT_WKT;
        _setup.horiz = key.horiz;
    }
    else
    {
        // Try to set it from the user input.  This will handle things like CRS:84
        // createFromUserInput will actually handle all valid inputs from GDAL,
        // so we might be able to simplify this function and just call createFromUserInput.
        //_setup.name = key.horiz;
        _setup.type = INIT_USER;
        _setup.horiz = key.horiz;
    }

    // next, resolve the vertical SRS:
    if (!key.vert.empty() && !ciEquals(key.vert, "geodetic"))
    {
        _vdatum = VerticalDatum::get(key.vert);
        if (!_vdatum.valid())
        {
            OE_WARN << LC << "Failed to locate vertical datum \"" << key.vert << "\"" << std::endl;
        }
    }

    _key = key;

    // create a handle for this thread and establish validity.
    init();
}

SpatialReference::ThreadLocal &
SpatialReference::getLocal() const
{
    ThreadLocal &local = _local.get();

    if (local._handle == nullptr)
    {
        local._threadId = std::this_thread::get_id();

        if (_setup.srcHandle != nullptr)
        {
            // 克隆现有句柄
            auto sourceIt = g_spatialRefHandles.find(_setup.srcHandle);
            if (sourceIt != g_spatialRefHandles.end())
            {
                void *newHandle = createNewHandle();
                auto clonedHandle = std::make_shared<CustomSpatialRefHandle>(*sourceIt->second);
                g_spatialRefHandles[newHandle] = clonedHandle;
                local._handle = newHandle;
            }
            else
            {
                OE_WARN << LC << "Failed to clone an existing handle" << std::endl;
                _valid = false;
            }
        }
        else
        {
            // 创建新的空间参考句柄
            void *newHandle = createNewHandle();
            auto customHandle = std::make_shared<CustomSpatialRefHandle>();
            CustomErrorCode error = CUSTOM_ERR_INVALID_HANDLE;

            if (_setup.type == INIT_PROJ)
            {
                // 解析PROJ4字符串
                auto proj4Params = parseProj4(_setup.horiz);
                customHandle->proj4String = _setup.horiz;

                // 设置基本属性
                if (proj4Params.find("proj") != proj4Params.end())
                {
                    std::string proj = proj4Params["proj"];
                    if (proj == "longlat")
                    {
                        customHandle->isGeographic = true;
                        customHandle->isProjected = false;
                    }
                    else if (proj == "merc" || proj == "webmerc")
                    {
                        customHandle->isGeographic = false;
                        customHandle->isProjected = true;
                        customHandle->projection = "mercator";
                    }
                    error = CUSTOM_ERR_NONE;
                }

                // 设置椭球参数
                if (proj4Params.find("a") != proj4Params.end())
                {
                    customHandle->semiMajorAxis = std::stod(proj4Params["a"]);
                }
                if (proj4Params.find("b") != proj4Params.end())
                {
                    customHandle->semiMinorAxis = std::stod(proj4Params["b"]);
                }

                // 设置基准
                if (proj4Params.find("datum") != proj4Params.end())
                {
                    customHandle->datum = proj4Params["datum"];
                }
            }
            else if (_setup.type == INIT_WKT)
            {
                // 简单的WKT解析
                customHandle->wktString = _setup.horiz;
                std::string wkt_lower = toLower(_setup.horiz);

                if (wkt_lower.find("geogcs") != std::string::npos ||
                    wkt_lower.find("geogcrs") != std::string::npos)
                {
                    customHandle->isGeographic = true;
                    customHandle->isProjected = false;
                    error = CUSTOM_ERR_NONE;
                }
                else if (wkt_lower.find("projcs") != std::string::npos ||
                         wkt_lower.find("projcrs") != std::string::npos)
                {
                    customHandle->isGeographic = false;
                    customHandle->isProjected = true;
                    error = CUSTOM_ERR_NONE;
                }
                else if (wkt_lower.find("geoccs") != std::string::npos)
                {
                    customHandle->isGeographic = false;
                    customHandle->isProjected = false;
                    customHandle->projection = "geocentric";
                    error = CUSTOM_ERR_NONE;
                }

                // 尝试解析椭球体信息
                size_t spheroid_pos = wkt_lower.find("spheroid[");
                if (spheroid_pos != std::string::npos)
                {
                    size_t end_pos = _setup.horiz.find(']', spheroid_pos);
                    if (end_pos != std::string::npos)
                    {
                        std::string spheroid_str = _setup.horiz.substr(spheroid_pos + 9, end_pos - (spheroid_pos + 9));
                        StringTokenizer tokenizer;
                        tokenizer.delim(",");
                        auto tokens = tokenizer(spheroid_str);
                        if (tokens.size() >= 3)
                        {
                            try
                            {
                                double semiMajor = as<double>(tokens[1], 0.0);
                                double invFlattening = as<double>(tokens[2], 0.0);
                                if (semiMajor > 0 && invFlattening > 1.0) // 基本验证
                                {
                                    customHandle->semiMajorAxis = semiMajor;
                                    customHandle->semiMinorAxis = semiMajor * (1.0 - 1.0 / invFlattening);
                                }
                            }
                            catch (...)
                            { /* 忽略解析错误 */
                            }
                        }
                    }
                }
            }
            else
            {
                // 从用户输入创建（处理已知的常见情况）
                std::string input = toLower(_setup.horiz);

                if (input.empty() || input == "wgs84" || input == "epsg:4326")
                {
                    *customHandle = *createWGS84Geographic();
                    error = CUSTOM_ERR_NONE;
                }
                else if (input.find("spherical-mercator") != std::string::npos ||
                         input.find("web-mercator") != std::string::npos ||
                         input.find("epsg:3857") != std::string::npos ||
                         input.find("epsg:900913") != std::string::npos)
                {
                    *customHandle = *createSphericalMercator();
                    error = CUSTOM_ERR_NONE;
                }
                else
                {
                    // 尝试作为PROJ4字符串处理
                    if (_setup.horiz.find("+proj=") != std::string::npos)
                    {
                        auto proj4Params = parseProj4(_setup.horiz);
                        customHandle->proj4String = _setup.horiz;

                        // 确保至少有投影类型
                        if (proj4Params.count("proj"))
                        {
                            error = CUSTOM_ERR_NONE;
                        }
                        else
                        {
                            OE_WARN << LC << "Invalid PROJ4 string (missing +proj): " << _setup.horiz << std::endl;
                        }
                    }
                    else
                    {
                        OE_WARN << LC << "Unrecognized SRS input string: " << _setup.horiz << std::endl;
                    }
                }
            }

            if (error != CUSTOM_ERR_NONE)
            {
                OE_WARN << LC << "Failed to create SRS from \"" << _setup.horiz << "\"" << std::endl;
                _valid = false;
            }
            else
            {
                g_spatialRefHandles[newHandle] = customHandle;
                local._handle = newHandle;
            }
        }
    }

    if (local._threadId != std::this_thread::get_id())
    {
        OE_WARN << LC << "Thread Safety Violation at line " OE_STRINGIFY(__LINE__) << std::endl;
    }

    return local;
}

bool SpatialReference::getBounds(Bounds &output) const
{
    output = _bounds;
    return _bounds.valid();
}

void *
SpatialReference::getHandle() const
{
    return getLocal()._handle;
}

SpatialReference *
SpatialReference::createFromHandle(void *ogrHandle)
{
    OE_SOFT_ASSERT_AND_RETURN(ogrHandle != nullptr, nullptr);

    return new SpatialReference(ogrHandle);
}

/****************************************************************************/

SpatialReference::~SpatialReference()
{
    // nop
}

bool SpatialReference::isGeographic() const
{
    return _domain == GEOGRAPHIC;
}

bool SpatialReference::isGeodetic() const
{
    return isGeographic() && !_vdatum.valid();
}

bool SpatialReference::isProjected() const
{
    return _domain == PROJECTED;
}

bool SpatialReference::isGeocentric() const
{
    return _domain == GEOCENTRIC;
}

const std::string &
SpatialReference::getName() const
{
    return _name;
}

const Ellipsoid &
SpatialReference::getEllipsoid() const
{
    return _ellipsoid;
}

const std::string &
SpatialReference::getDatumName() const
{
    return _datum;
}

const UnitsType &
SpatialReference::getUnits() const
{
    return _units;
}

double
SpatialReference::getReportedLinearUnits() const
{
    return _reportedLinearUnits;
}

const std::string &
SpatialReference::getWKT() const
{
    return _wkt;
}

const VerticalDatum *
SpatialReference::getVerticalDatum() const
{
    return _vdatum.get();
}

const SpatialReference::Key &
SpatialReference::getKey() const
{
    return _key;
}

const std::string &
SpatialReference::getHorizInitString() const
{
    return _key.horiz;
}

const std::string &
SpatialReference::getVertInitString() const
{
    return _key.vert;
}

bool SpatialReference::isEquivalentTo(const SpatialReference *rhs) const
{
    return _isEquivalentTo(rhs, true);
}

bool SpatialReference::isHorizEquivalentTo(const SpatialReference *rhs) const
{
    return _isEquivalentTo(rhs, false);
}

bool SpatialReference::isVertEquivalentTo(const SpatialReference *rhs) const
{
    // vertical equivalence means the same vertical datum and the same
    // reference ellipsoid.
    return _vdatum.get() == rhs->_vdatum.get() &&
           _ellipsoidId == rhs->_ellipsoidId;
}

bool SpatialReference::_isEquivalentTo(const SpatialReference *rhs, bool considerVDatum) const
{
    if (this == rhs)
        return true;

    if (!valid())
        return false;

    if (rhs == nullptr || !rhs->valid())
        return false;

    if (isGeographic() != rhs->isGeographic() ||
        isMercator() != rhs->isMercator() ||
        isGeocentric() != rhs->isGeocentric() ||
        isSphericalMercator() != rhs->isSphericalMercator() ||
        isNorthPolar() != rhs->isNorthPolar() ||
        isSouthPolar() != rhs->isSouthPolar() ||
        isUserDefined() != rhs->isUserDefined() ||
        isCube() != rhs->isCube() ||
        isLTP() != rhs->isLTP())
    {
        return false;
    }

    if (isGeocentric() && rhs->isGeocentric())
        return true;

    if (considerVDatum && (_vdatum.get() != rhs->_vdatum.get()))
        return false;

    if (_key.horizLower == rhs->_key.horizLower &&
        (!considerVDatum || (_key.vertLower == rhs->_key.vertLower)))
    {
        return true;
    }

    if (_proj4 == rhs->_proj4)
        return true;

    if (_wkt == rhs->_wkt)
        return true;

    if (this->isGeographic() && rhs->isGeographic())
    {
        return osg::equivalent(getEllipsoid().getRadiusEquator(), rhs->getEllipsoid().getRadiusEquator()) &&
               osg::equivalent(getEllipsoid().getRadiusPolar(), rhs->getEllipsoid().getRadiusPolar());
    }

    // last resort, since it requires the lock
    void *myHandle = getHandle();
    void *rhsHandle = rhs->getHandle();

    if (!myHandle || !rhsHandle)
        return false;

    // 自定义的句柄比较实现
    auto myIt = g_spatialRefHandles.find(myHandle);
    auto rhsIt = g_spatialRefHandles.find(rhsHandle);

    if (myIt == g_spatialRefHandles.end() || rhsIt == g_spatialRefHandles.end())
        return false;

    auto myCustomHandle = myIt->second;
    auto rhsCustomHandle = rhsIt->second;

    // 比较关键属性
    return (myCustomHandle->isGeographic == rhsCustomHandle->isGeographic &&
            myCustomHandle->isProjected == rhsCustomHandle->isProjected &&
            myCustomHandle->datum == rhsCustomHandle->datum &&
            std::abs(myCustomHandle->semiMajorAxis - rhsCustomHandle->semiMajorAxis) < 1e-6 &&
            std::abs(myCustomHandle->semiMinorAxis - rhsCustomHandle->semiMinorAxis) < 1e-6 &&
            myCustomHandle->projection == rhsCustomHandle->projection);
}

const SpatialReference *
SpatialReference::getGeographicSRS() const
{
    if (isGeographic())
        return this;

    if (_is_spherical_mercator)
        return get("wgs84", _key.vertLower);

    if (!_geo_srs.valid())
    {
        std::lock_guard<std::mutex> lock(_mutex);

        if (!_geo_srs.valid()) // double-check pattern
        {
            // 自定义实现：从投影坐标系统提取地理坐标系统
            void *handle = getHandle();
            if (handle)
            {
                auto it = g_spatialRefHandles.find(handle);
                if (it != g_spatialRefHandles.end())
                {
                    auto customHandle = it->second;

                    // 创建地理坐标系统
                    auto geoHandle = std::make_shared<CustomSpatialRefHandle>();

                    // 复制椭球和基准信息
                    geoHandle->semiMajorAxis = customHandle->semiMajorAxis;
                    geoHandle->semiMinorAxis = customHandle->semiMinorAxis;
                    geoHandle->datum = customHandle->datum;
                    geoHandle->isGeographic = true;
                    geoHandle->isProjected = false;
                    geoHandle->linearUnits = 1.0;

                    // 设置名称和属性
                    geoHandle->name = customHandle->datum.empty() ? "Geographic" : customHandle->datum;
                    geoHandle->attributes["GEOGCS"] = geoHandle->name;
                    geoHandle->attributes["DATUM"] = customHandle->datum;
                    geoHandle->attributes["SPHEROID"] = "WGS 84"; // 默认椭球名
                    geoHandle->attributes["UNIT"] = "degree";

                    // 生成PROJ4字符串
                    geoHandle->proj4String = "+proj=longlat +ellps=WGS84 +datum=" +
                                             (customHandle->datum.empty() ? "WGS84" : customHandle->datum) + " +no_defs";

                    // 生成WKT字符串
                    geoHandle->wktString =
                        "GEOGCS[\"" + geoHandle->name + "\"," +
                        "DATUM[\"" + customHandle->datum + "\"," +
                        "SPHEROID[\"WGS 84\"," + std::to_string(customHandle->semiMajorAxis) + "," +
                        std::to_string(customHandle->semiMajorAxis / customHandle->semiMinorAxis) + "]]," +
                        "PRIMEM[\"Greenwich\",0]," +
                        "UNIT[\"degree\",0.0174532925199433]]";

                    // 创建新的SpatialReference
                    Key key(geoHandle->wktString, _key.vertLower);
                    _geo_srs = new SpatialReference(key);
                }
            }
        }
    }

    return _geo_srs.get();
}

const SpatialReference *
SpatialReference::getGeodeticSRS() const
{
    if (isGeodetic())
        return this;

    if (!_geodetic_srs.valid())
    {
        std::lock_guard<std::mutex> lock(_mutex);

        if (!_geodetic_srs.valid()) // double check pattern
        {
            // 自定义实现：从当前坐标系统提取大地坐标系统（无垂直基准）
            void *handle = getHandle();
            if (handle)
            {
                auto it = g_spatialRefHandles.find(handle);
                if (it != g_spatialRefHandles.end())
                {
                    auto customHandle = it->second;

                    // 创建大地坐标系统（与地理坐标系统相同，但无垂直基准）
                    auto geodeticHandle = std::make_shared<CustomSpatialRefHandle>();

                    // 复制椭球和基准信息
                    geodeticHandle->semiMajorAxis = customHandle->semiMajorAxis;
                    geodeticHandle->semiMinorAxis = customHandle->semiMinorAxis;
                    geodeticHandle->datum = customHandle->datum;
                    geodeticHandle->isGeographic = true;
                    geodeticHandle->isProjected = false;
                    geodeticHandle->linearUnits = 1.0;

                    // 设置名称和属性
                    geodeticHandle->name = (customHandle->datum.empty() ? "WGS84" : customHandle->datum) + " Geodetic";
                    geodeticHandle->attributes["GEOGCS"] = geodeticHandle->name;
                    geodeticHandle->attributes["DATUM"] = customHandle->datum;
                    geodeticHandle->attributes["SPHEROID"] = "WGS 84"; // 默认椭球名
                    geodeticHandle->attributes["UNIT"] = "degree";

                    // 生成PROJ4字符串
                    geodeticHandle->proj4String = "+proj=longlat +ellps=WGS84 +datum=" +
                                                  (customHandle->datum.empty() ? "WGS84" : customHandle->datum) + " +no_defs";

                    // 生成WKT字符串
                    geodeticHandle->wktString =
                        "GEOGCS[\"" + geodeticHandle->name + "\"," +
                        "DATUM[\"" + customHandle->datum + "\"," +
                        "SPHEROID[\"WGS 84\"," + std::to_string(customHandle->semiMajorAxis) + "," +
                        std::to_string(customHandle->semiMajorAxis / (customHandle->semiMajorAxis - customHandle->semiMinorAxis)) + "]]," +
                        "PRIMEM[\"Greenwich\",0]," +
                        "UNIT[\"degree\",0.0174532925199433]]";

                    // 创建新的SpatialReference，注意垂直基准为空字符串
                    Key key(geodeticHandle->wktString, "");
                    _geodetic_srs = new SpatialReference(key);
                }
            }
        }
    }

    return _geodetic_srs.get();
}

const SpatialReference *
SpatialReference::getGeocentricSRS() const
{
    if (isGeocentric())
        return this;

    if (!_geocentric_srs.valid())
    {
        std::lock_guard<std::mutex> lock(_mutex);

        if (!_geocentric_srs.valid()) // double-check pattern
        {
            // 自定义实现：从当前坐标系统提取地心坐标系统
            void *handle = getHandle();
            if (handle)
            {
                auto it = g_spatialRefHandles.find(handle);
                if (it != g_spatialRefHandles.end())
                {
                    auto customHandle = it->second;

                    // 创建地心坐标系统（使用笛卡尔坐标X,Y,Z）
                    auto geocentricHandle = std::make_shared<CustomSpatialRefHandle>();

                    // 复制椭球和基准信息
                    geocentricHandle->semiMajorAxis = customHandle->semiMajorAxis;
                    geocentricHandle->semiMinorAxis = customHandle->semiMinorAxis;
                    geocentricHandle->datum = customHandle->datum;
                    geocentricHandle->isGeographic = false;
                    geocentricHandle->isProjected = false; // 地心坐标系统是特殊的3D坐标系统
                    geocentricHandle->linearUnits = 1.0;   // 单位为米
                    geocentricHandle->projection = "geocentric";

                    // 设置名称和属性
                    geocentricHandle->name = (customHandle->datum.empty() ? "WGS84" : customHandle->datum) + " Geocentric";
                    geocentricHandle->attributes["GEOCCS"] = geocentricHandle->name;
                    geocentricHandle->attributes["DATUM"] = customHandle->datum;
                    geocentricHandle->attributes["SPHEROID"] = "WGS 84";
                    geocentricHandle->attributes["UNIT"] = "metre";

                    // 生成PROJ4字符串（地心坐标系统）
                    geocentricHandle->proj4String = "+proj=geocent +ellps=WGS84 +datum=" +
                                                    (customHandle->datum.empty() ? "WGS84" : customHandle->datum) + " +units=m +no_defs";

                    // 生成WKT字符串（地心坐标系统）
                    geocentricHandle->wktString =
                        "GEOCCS[\"" + geocentricHandle->name + "\"," +
                        "DATUM[\"" + customHandle->datum + "\"," +
                        "SPHEROID[\"WGS 84\"," + std::to_string(customHandle->semiMajorAxis) + "," +
                        std::to_string(customHandle->semiMajorAxis / (customHandle->semiMajorAxis - customHandle->semiMinorAxis)) + "]]," +
                        "PRIMEM[\"Greenwich\",0]," +
                        "UNIT[\"metre\",1]," +
                        "AXIS[\"Geocentric X\",OTHER]," +
                        "AXIS[\"Geocentric Y\",OTHER]," +
                        "AXIS[\"Geocentric Z\",NORTH]]";

                    // 创建新的SpatialReference，注意垂直基准为空字符串（在ECEF中没有垂直基准）
                    Key key(geocentricHandle->wktString, "");
                    _geocentric_srs = new SpatialReference(key);
                    _geocentric_srs->_domain = GEOCENTRIC;
                }
            }
        }
    }

    return _geocentric_srs.get();
}

const SpatialReference *
SpatialReference::createTangentPlaneSRS(const osg::Vec3d &origin) const
{
    if (!valid())
        return nullptr;

    osg::Vec3d lla;
    const SpatialReference *srs = getGeographicSRS();
    if (srs && transform(origin, srs, lla))
    {
        const Key &key = srs->getKey();
        SpatialReference *ltp = new TangentPlaneSpatialReference(key, lla);
        return ltp;
    }
    else
    {
        OE_WARN << LC << "Unable to create LTP SRS" << std::endl;
        return nullptr;
    }
}

const SpatialReference *
SpatialReference::createTransMercFromLongitude(const Angle &lon) const
{
    if (!valid())
        return nullptr;

    // note. using tmerc with +lat_0 <> 0 is sloooooow.
    std::string datum = getDatumName();
    std::string horiz = Stringify()
                        << "+proj=tmerc +lat_0=0"
                        << " +lon_0=" << lon.as(Units::DEGREES)
                        << " +datum=" << (datum.empty() ? "WGS84" : datum);

    return SpatialReference::create(horiz, getVertInitString());
}

const SpatialReference *
SpatialReference::createUTMFromLonLat(const Angle &lon, const Angle &lat) const
{
    if (!valid())
        return nullptr;

    // note. UTM is up to 10% faster than TMERC for the same meridian.
    unsigned zone = 1 + (unsigned)floor((lon.as(Units::DEGREES) + 180.0) / 6.0);
    std::string datum = getDatumName();
    std::string horiz = Stringify()
                        << "+proj=utm +zone=" << zone
                        << (lat.as(Units::DEGREES) < 0 ? " +south" : "")
                        << " +datum=" << (datum.empty() ? "WGS84" : datum);

    return SpatialReference::create(horiz, getVertInitString());
}

const SpatialReference *
SpatialReference::createEquirectangularSRS() const
{
    if (!valid())
        return nullptr;

    return SpatialReference::create(
        "+proj=eqc +units=m +no_defs",
        getVertInitString());
}

bool SpatialReference::isMercator() const
{
    return _is_mercator;
}

bool SpatialReference::isSphericalMercator() const
{
    return _is_spherical_mercator;
}

bool SpatialReference::isNorthPolar() const
{
    return _is_north_polar;
}

bool SpatialReference::isSouthPolar() const
{
    return _is_south_polar;
}

bool SpatialReference::isUserDefined() const
{
    return _is_user_defined;
}

bool SpatialReference::isCube() const
{
    return _is_cube;
}

bool SpatialReference::populateCoordinateSystemNode(osg::CoordinateSystemNode *csn) const
{
    OE_SOFT_ASSERT_AND_RETURN(csn != nullptr, false);

    if (!_wkt.empty())
    {
        csn->setFormat("WKT");
        csn->setCoordinateSystem(_wkt);
    }
    else if (!_proj4.empty())
    {
        csn->setFormat("PROJ4");
        csn->setCoordinateSystem(_proj4);
    }

    csn->setEllipsoidModel(
        new osg::EllipsoidModel(
            _ellipsoid.getSemiMajorAxis(),
            _ellipsoid.getSemiMinorAxis()));

    return true;
}

bool SpatialReference::createLocalToWorld(const osg::Vec3d &xyz, osg::Matrixd &out_local2world) const
{
    if (!valid())
        return false;

    if (isProjected() && !isCube())
    {
        osg::Vec3d world;
        if (!transformToWorld(xyz, world))
            return false;
        out_local2world = osg::Matrix::translate(world);
    }
    else if (isGeocentric())
    {
        out_local2world = _ellipsoid.geocentricToLocalToWorld(xyz);
    }
    else
    {
        // convert to ECEF:
        osg::Vec3d ecef;
        if (!transform(xyz, getGeocentricSRS(), ecef))
            return false;

        // and create the matrix.
        out_local2world = _ellipsoid.geocentricToLocalToWorld(ecef);
    }
    return true;
}

bool SpatialReference::createWorldToLocal(const osg::Vec3d &xyz, osg::Matrixd &out_world2local) const
{
    osg::Matrixd local2world;
    if (!createLocalToWorld(xyz, local2world))
        return false;
    out_world2local.invert(local2world);
    return true;
}

bool SpatialReference::transform(const osg::Vec3d &input,
                                 const SpatialReference *outputSRS,
                                 osg::Vec3d &output) const
{
    OE_SOFT_ASSERT_AND_RETURN(outputSRS != nullptr, false);

    if (!valid())
        return false;

    std::vector<osg::Vec3d> v(1, input);

    if (transform(v, outputSRS))
    {
        output = v[0];
        return true;
    }
    return false;
}

bool SpatialReference::transform(std::vector<osg::Vec3d> &points,
                                 const SpatialReference *outputSRS) const
{
    OE_SOFT_ASSERT_AND_RETURN(outputSRS != nullptr, false);

    if (!valid())
        return false;

    // trivial equivalency:
    if (isEquivalentTo(outputSRS))
        return true;

    bool success = false;

    // do the pre-transformation pass:
    const SpatialReference *inputSRS = preTransform(points);
    if (!inputSRS)
        return false;

    if (inputSRS->isGeocentric() && !outputSRS->isGeocentric())
    {
        const SpatialReference *outputGeoSRS = outputSRS->getGeodeticSRS();
        geocentricToGeodetic(points, outputGeoSRS->getEllipsoid());
        return outputGeoSRS->transform(points, outputSRS);
    }

    else if (!inputSRS->isGeocentric() && outputSRS->isGeocentric())
    {
        const SpatialReference *outputGeoSRS = outputSRS->getGeodeticSRS();
        success = inputSRS->transform(points, outputGeoSRS);
        geodeticToGeocentric(points, outputGeoSRS->getEllipsoid());
        return success;
    }

    // if the points are starting as geographic, do the Z's first to avoid an unneccesary
    // transformation in the case of differing vdatums.
    bool z_done = false;
    if (inputSRS->isGeographic())
    {
        z_done = inputSRS->transformZ(points, outputSRS, true);
    }

    ThreadLocal &local = getLocal();

    // move the xy data into straight arrays that OGR can use
    unsigned count = points.size();

    if (count * 2 > local._workspaceSize)
    {
        if (local._workspace)
            delete[] local._workspace;
        local._workspace = new double[count * 2];
        local._workspaceSize = count * 2;
    }

    double *x = local._workspace;
    double *y = local._workspace + count;

    for (unsigned i = 0; i < count; i++)
    {
        x[i] = points[i].x();
        y[i] = points[i].y();
    }

    success = inputSRS->transformXYPointArrays(local, x, y, count, outputSRS);

    if (success)
    {
        if (inputSRS->isProjected() && outputSRS->isGeographic())
        {
            // special case: when going from projected to geographic, clamp the
            // points to the maximum geographic extent. Sometimes the conversion from
            // a global/projected SRS (like mercator) will result in *slightly* invalid
            // geographic points (like long=180.000003), so this addresses that issue.
            for (unsigned i = 0; i < count; i++)
            {
                points[i].x() = osg::clampBetween(x[i], -180.0, 180.0);
                points[i].y() = osg::clampBetween(y[i], -90.0, 90.0);
            }
        }
        else
        {
            for (unsigned i = 0; i < count; i++)
            {
                points[i].x() = x[i];
                points[i].y() = y[i];
            }
        }
    }

    if (success)
    {
        // calculate the Zs if we haven't already done so
        if (!z_done)
        {
            z_done = inputSRS->transformZ(points, outputSRS, outputSRS->isGeographic());
        }

        // run the user post-transform code
        outputSRS->postTransform(points);
    }

    return success;
}

bool SpatialReference::transform2D(double x, double y,
                                   const SpatialReference *outputSRS,
                                   double &out_x, double &out_y) const
{
    OE_SOFT_ASSERT_AND_RETURN(outputSRS != nullptr, false);

    if (!valid())
        return false;

    osg::Vec3d temp(x, y, 0);
    bool ok = transform(temp, outputSRS, temp);
    if (ok)
    {
        out_x = temp.x();
        out_y = temp.y();
    }
    return ok;
}

bool SpatialReference::transformXYPointArrays(
    ThreadLocal &local,
    double *x,
    double *y,
    unsigned count,
    const SpatialReference *out_srs) const
{
    OE_SOFT_ASSERT_AND_RETURN(out_srs != nullptr, false);

    if (!valid())
        return false;

    // 获取变换句柄的缓存key
    std::string cacheKey = out_srs->getWKT();
    optional<TransformInfo> &xform = local._xformCache[cacheKey];

    if (!xform.isSet())
    {
        // 创建新的变换句柄
        void *newTransformHandle = createNewHandle();
        auto transformHandle = std::make_shared<CustomTransformHandle>();

        // 获取源和目标空间参考
        auto fromIt = g_spatialRefHandles.find(local._handle);
        auto toIt = g_spatialRefHandles.find(out_srs->getHandle());

        if (fromIt != g_spatialRefHandles.end() && toIt != g_spatialRefHandles.end())
        {
            transformHandle->fromSRS = fromIt->second;
            transformHandle->toSRS = toIt->second;
            transformHandle->isValid = true;

            g_transformHandles[newTransformHandle] = transformHandle;

            xform.mutable_value()._handle = newTransformHandle;
            xform.mutable_value()._failed = false;
        }
        else
        {
            OE_WARN << LC
                    << "SRS xform not possible:" << std::endl
                    << "    From => " << getName() << std::endl
                    << "    To   => " << out_srs->getName() << std::endl;

            g_lastErrorMsg = "Unable to find spatial reference handles";
            OE_WARN << LC << "ERROR: " << g_lastErrorMsg << std::endl;

            xform.mutable_value()._handle = nullptr;
            xform.mutable_value()._failed = true;

            return false;
        }
    }

    if (xform->_failed)
    {
        return false;
    }

    // 执行坐标变换
    auto transformIt = g_transformHandles.find(xform->_handle);
    if (transformIt == g_transformHandles.end())
        return false;

    auto transformHandle = transformIt->second;
    if (!transformHandle || !transformHandle->isValid)
        return false;

    // 对每个点执行变换
    bool allSuccess = true;
    for (unsigned i = 0; i < count; ++i)
    {
        double z = 0.0; // 2D变换，Z设为0
        if (!performBasicTransform(transformHandle->fromSRS, transformHandle->toSRS, x[i], y[i], z))
        {
            allSuccess = false;
            break;
        }
    }

    return allSuccess;
}

bool SpatialReference::transformZ(std::vector<osg::Vec3d> &points,
                                  const SpatialReference *outputSRS,
                                  bool pointsAreLatLong) const
{
    OE_SOFT_ASSERT_AND_RETURN(outputSRS != nullptr, false);

    if (!valid())
        return false;

    const VerticalDatum *outVDatum = outputSRS->getVerticalDatum();

    // same vdatum, no xformation necessary.
    if (_vdatum.get() == outVDatum)
        return true;

    UnitsType inUnits = _vdatum.valid() ? _vdatum->getUnits() : Units::METERS;
    UnitsType outUnits = outVDatum ? outVDatum->getUnits() : inUnits;

    if (isGeographic() || pointsAreLatLong)
    {
        for (unsigned i = 0; i < points.size(); ++i)
        {
            if (_vdatum.valid())
            {
                // to HAE:
                points[i].z() = _vdatum->msl2hae(points[i].y(), points[i].x(), points[i].z());
            }

            // do the units conversion:
            points[i].z() = inUnits.convertTo(outUnits, points[i].z());

            if (outVDatum)
            {
                // to MSL:
                points[i].z() = outVDatum->hae2msl(points[i].y(), points[i].x(), points[i].z());
            }
        }
    }

    else // need to xform input points
    {
        // copy the points and convert them to geographic coordinates (lat/long with the same Z):
        std::vector<osg::Vec3d> geopoints(points);
        transform(geopoints, getGeographicSRS());

        for (unsigned i = 0; i < geopoints.size(); ++i)
        {
            if (_vdatum.valid())
            {
                // to HAE:
                points[i].z() = _vdatum->msl2hae(geopoints[i].y(), geopoints[i].x(), points[i].z());
            }

            // do the units conversion:
            points[i].z() = inUnits.convertTo(outUnits, points[i].z());

            if (outVDatum)
            {
                // to MSL:
                points[i].z() = outVDatum->hae2msl(geopoints[i].y(), geopoints[i].x(), points[i].z());
            }
        }
    }

    return true;
}

bool SpatialReference::transformToWorld(const osg::Vec3d &input,
                                        osg::Vec3d &output) const
{
    if (!valid())
        return false;

    if (isGeographic() || isCube())
    {
        return transform(input, getGeocentricSRS(), output);
    }
    else // isProjected
    {
        output = input;
        if (_vdatum.valid())
        {
            osg::Vec3d geo(input);
            if (!transform(input, getGeographicSRS(), geo))
                return false;

            output.z() = _vdatum->msl2hae(geo.y(), geo.x(), input.z());
        }
        return true;
    }
}

bool SpatialReference::transformFromWorld(const osg::Vec3d &world,
                                          osg::Vec3d &output,
                                          double *out_haeZ) const
{
    if (isGeographic() || isCube())
    {
        bool ok = getGeocentricSRS()->transform(world, this, output);
        if (ok && out_haeZ)
        {
            if (_vdatum.valid())
                *out_haeZ = _vdatum->msl2hae(output.y(), output.x(), output.z());
            else
                *out_haeZ = output.z();
        }
        return ok;
    }
    else // isProjected
    {
        output = world;

        if (out_haeZ)
            *out_haeZ = world.z();

        if (_vdatum.valid())
        {
            // get the geographic coords by converting x/y/hae -> lat/long/msl:
            osg::Vec3d lla;
            if (!transform(world, getGeographicSRS(), lla))
                return false;

            output.z() = lla.z();
        }

        return true;
    }
}

double
SpatialReference::transformUnits(double input,
                                 const SpatialReference *outSRS,
                                 double latitude) const
{
    OE_SOFT_ASSERT_AND_RETURN(outSRS != nullptr, input);

    if (this->isProjected() && outSRS->isGeographic())
    {
        return Units::DEGREES.convertTo(
            outSRS->getUnits(),
            outSRS->getEllipsoid().metersToLongitudinalDegrees(
                getUnits().convertTo(Units::METERS, input),
                latitude));
    }
    else if (this->isGeocentric() && outSRS->isGeographic())
    {
        return Units::DEGREES.convertTo(
            outSRS->getUnits(),
            outSRS->getEllipsoid().metersToLongitudinalDegrees(input, latitude));
    }
    else if (this->isGeographic() && outSRS->isProjected())
    {
        return Units::METERS.convertTo(
            outSRS->getUnits(),
            outSRS->getEllipsoid().longitudinalDegreesToMeters(
                getUnits().convertTo(Units::DEGREES, input),
                latitude));
    }
    else if (this->isGeographic() && outSRS->isGeocentric())
    {
        return outSRS->getEllipsoid().longitudinalDegreesToMeters(
            getUnits().convertTo(Units::DEGREES, input),
            latitude);
    }
    else // both projected or both geographic.
    {
        return getUnits().convertTo(outSRS->getUnits(), input);
    }
}

double
SpatialReference::transformUnits(const Distance &distance,
                                 const SpatialReference *outSRS,
                                 double latitude)
{
    OE_SOFT_ASSERT_AND_RETURN(outSRS != nullptr, distance.getValue());

    if (distance.getUnits().isLinear() && outSRS->isGeographic())
    {
        return Units::DEGREES.convertTo(
            outSRS->getUnits(),
            outSRS->getEllipsoid().metersToLongitudinalDegrees(
                distance.as(Units::METERS),
                latitude));
    }
    else if (distance.getUnits().isAngular() && outSRS->isProjected())
    {
        return Units::METERS.convertTo(
            outSRS->getUnits(),
            outSRS->getEllipsoid().longitudinalDegreesToMeters(
                distance.as(Units::DEGREES),
                latitude));
    }
    else // both projected or both geographic.
    {
        return distance.as(outSRS->getUnits());
    }
}

double
SpatialReference::transformDistance(const Distance &input, const UnitsType &outputUnits, double referenceLatitude) const
{
    auto inputUnits = input.getUnits();

    if (inputUnits.isAngle() && outputUnits.isLinear())
    {
        auto meters = getEllipsoid().longitudinalDegreesToMeters(input.as(Units::DEGREES), referenceLatitude);
        return Units::convert(Units::METERS, outputUnits, meters);
    }
    else if (inputUnits.isLinear() && outputUnits.isAngle())
    {
        auto degrees = getEllipsoid().metersToLongitudinalDegrees(input.as(Units::METERS), referenceLatitude);
        return Units::convert(Units::DEGREES, outputUnits, degrees);
    }
    else
    {
        return input.as(outputUnits);
    }
}

bool SpatialReference::clampExtentToLegalBounds(
    const SpatialReference *target_srs,
    double &in_out_xmin,
    double &in_out_ymin,
    double &in_out_xmax,
    double &in_out_ymax) const
{
    OE_SOFT_ASSERT_AND_RETURN(target_srs, false);

    Bounds rhs_bounds;
    if (!target_srs->getBounds(rhs_bounds) || !rhs_bounds.valid())
        return false;

    Bounds rhs_bounds_geo = rhs_bounds;
    if (!target_srs->isGeographic())
    {
        auto *geo_srs = target_srs->getGeographicSRS();
        target_srs->transform(rhs_bounds._min, geo_srs, rhs_bounds_geo._max);
        target_srs->transform(rhs_bounds._max, geo_srs, rhs_bounds_geo._max);
    }

    Bounds lhs_bounds(in_out_xmin, in_out_ymin, 0.0, in_out_xmax, in_out_ymax, 0.0);
    Bounds lhs_bounds_geo = lhs_bounds;
    if (!this->isGeographic())
    {
        auto *geo_srs = this->getGeographicSRS();
        target_srs->transform(lhs_bounds._min, geo_srs, lhs_bounds_geo._max);
        target_srs->transform(lhs_bounds._max, geo_srs, lhs_bounds_geo._max);
    }

    lhs_bounds_geo = intersectionOf(lhs_bounds_geo, rhs_bounds_geo);

    if (!this->isGeographic())
    {
        auto *geo_srs = this->getGeographicSRS();
        geo_srs->transform(lhs_bounds_geo._min, this, lhs_bounds._min);
        geo_srs->transform(lhs_bounds_geo._max, this, lhs_bounds._max);
        in_out_xmin = lhs_bounds.xMin();
        in_out_ymin = lhs_bounds.yMin();
        in_out_xmax = lhs_bounds.xMax();
        in_out_ymax = lhs_bounds.yMax();
    }
    else
    {
        in_out_xmin = lhs_bounds_geo.xMin();
        in_out_ymin = lhs_bounds_geo.yMin();
        in_out_xmax = lhs_bounds_geo.xMax();
        in_out_ymax = lhs_bounds_geo.yMax();
    }

    return true;
}

bool SpatialReference::transformExtentToMBR(
    const SpatialReference *to_srs,
    double &in_out_xmin,
    double &in_out_ymin,
    double &in_out_xmax,
    double &in_out_ymax) const
{
    OE_SOFT_ASSERT_AND_RETURN(to_srs != nullptr, false);

    if (!valid())
        return false;

    // Same SRS? no work to do.
    if (isHorizEquivalentTo(to_srs))
        return true;

    // Transform all points and take the maximum bounding rectangle the resulting points
    std::vector<osg::Vec3d> v;

    // Start by clamping to the out_srs' legal bounds, if possible.
    // TODO: rethink this to be more generic.
    if (isGeographic() && (to_srs->isMercator() || to_srs->isSphericalMercator()))
    {
        osg::ref_ptr<const Profile> merc = Profile::create(Profile::SPHERICAL_MERCATOR);
        in_out_ymin = clamp(in_out_ymin, merc->getLatLongExtent().yMin(), merc->getLatLongExtent().yMax());
        in_out_ymax = clamp(in_out_ymax, merc->getLatLongExtent().yMin(), merc->getLatLongExtent().yMax());
    }

    double height = in_out_ymax - in_out_ymin;
    double width = in_out_xmax - in_out_xmin;
    unsigned int numSamples = 5;

    v.reserve(5 + numSamples * 4);

    // first point is a centroid. This we will use to make sure none of the corner points
    // wraps around if the target SRS is geographic.
    v.push_back(osg::Vec3d(in_out_xmin + width * 0.5, in_out_ymin + height * 0.5, 0)); // centroid.

    // add the four corners
    v.push_back(osg::Vec3d(in_out_xmin, in_out_ymin, 0)); // ll
    v.push_back(osg::Vec3d(in_out_xmin, in_out_ymax, 0)); // ul
    v.push_back(osg::Vec3d(in_out_xmax, in_out_ymax, 0)); // ur
    v.push_back(osg::Vec3d(in_out_xmax, in_out_ymin, 0)); // lr

    // We also sample along the edges of the bounding box and include them in the
    // MBR computation in case you are dealing with a projection that will cause the edges
    // of the bounding box to be expanded.  This was first noticed when dealing with converting
    // Hotline Oblique Mercator to WGS84

    // Sample the edges
    double dWidth = width / (numSamples - 1);
    double dHeight = height / (numSamples - 1);

    // Left edge
    for (unsigned int i = 0; i < numSamples; i++)
    {
        v.push_back(osg::Vec3d(in_out_xmin, in_out_ymin + dHeight * (double)i, 0));
    }

    // Right edge
    for (unsigned int i = 0; i < numSamples; i++)
    {
        v.push_back(osg::Vec3d(in_out_xmax, in_out_ymin + dHeight * (double)i, 0));
    }

    // Top edge
    for (unsigned int i = 0; i < numSamples; i++)
    {
        v.push_back(osg::Vec3d(in_out_xmin + dWidth * (double)i, in_out_ymax, 0));
    }

    // Bottom edge
    for (unsigned int i = 0; i < numSamples; i++)
    {
        v.push_back(osg::Vec3d(in_out_xmin + dWidth * (double)i, in_out_ymin, 0));
    }

    if (transform(v, to_srs))
    {
        in_out_xmin = DBL_MAX;
        in_out_ymin = DBL_MAX;
        in_out_xmax = -DBL_MAX;
        in_out_ymax = -DBL_MAX;

        // For a geographic target, make sure the new extents contain the centroid
        // because they might have wrapped around or run into a precision failure.
        // v[0]=centroid, v[1]=LL, v[2]=UL, v[3]=UR, v[4]=LR
        if (to_srs->isGeographic())
        {
            if (v[1].x() > v[0].x() || v[2].x() > v[0].x())
                in_out_xmin = -180.0;
            if (v[3].x() < v[0].x() || v[4].x() < v[0].x())
                in_out_xmax = 180.0;
        }

        // enforce an MBR:
        for (unsigned int i = 0; i < v.size(); i++)
        {
            in_out_xmin = std::min(v[i].x(), in_out_xmin);
            in_out_ymin = std::min(v[i].y(), in_out_ymin);
            in_out_xmax = std::max(v[i].x(), in_out_xmax);
            in_out_ymax = std::max(v[i].y(), in_out_ymax);
        }

        return true;
    }

    return false;
}

bool SpatialReference::transformGrid(
    const SpatialReference *to_srs,
    double in_xmin, double in_ymin,
    double in_xmax, double in_ymax,
    double *x, double *y,
    unsigned int numx, unsigned int numy) const
{
    OE_SOFT_ASSERT_AND_RETURN(to_srs != nullptr, false);

    if (!valid())
        return false;

    std::vector<osg::Vec3d> points;

    const double dx = (in_xmax - in_xmin) / (numx - 1);
    const double dy = (in_ymax - in_ymin) / (numy - 1);

    unsigned int pixel = 0;
    double fc = 0.0;
    for (unsigned int c = 0; c < numx; ++c, ++fc)
    {
        const double dest_x = in_xmin + fc * dx;
        double fr = 0.0;
        for (unsigned int r = 0; r < numy; ++r, ++fr)
        {
            const double dest_y = in_ymin + fr * dy;

            points.push_back(osg::Vec3d(dest_x, dest_y, 0));
            pixel++;
        }
    }

    if (transform(points, to_srs))
    {
        for (unsigned i = 0; i < points.size(); ++i)
        {
            x[i] = points[i].x();
            y[i] = points[i].y();
        }
        return true;
    }
    return false;
}

void SpatialReference::init()
{
    void *handle = getHandle();

    if (!handle)
    {
        OE_WARN << LC << "Initialization failed: handle is null." << std::endl;
        _valid = false;
        return;
    }

    // 获取自定义句柄
    auto it = g_spatialRefHandles.find(handle);
    if (it == g_spatialRefHandles.end())
    {
        OE_WARN << LC << "Initialization failed: could not find custom handle." << std::endl;
        _valid = false;
        return;
    }

    auto customHandle = it->second;

    if (_is_ltp)
    {
        _is_user_defined = true;
        _domain = PROJECTED;
    }
    else if (_is_cube)
    {
        _domain = PROJECTED;
    }
    else if (customHandle->projection == "geocentric")
    {
        _domain = GEOCENTRIC;
    }
    else
    {
        _domain = customHandle->isGeographic ? GEOGRAPHIC : PROJECTED;
    }

    // Give the SRS a name if it doesn't have one:
    if (!_setup.name.empty())
    {
        _name = _setup.name;
    }

    // extract the ellipsoid parameters:
    _ellipsoid.setSemiMajorAxis(customHandle->semiMajorAxis);
    _ellipsoid.setSemiMinorAxis(customHandle->semiMinorAxis);

    // unique ID for comparing ellipsoids quickly:
    _ellipsoidId = hashString(Stringify()
                              << std::fixed << std::setprecision(10)
                              << _ellipsoid.getSemiMajorAxis() << ";" << _ellipsoid.getSemiMinorAxis());

    // try to get an ellipsoid name:
    _ellipsoid.setName(getOGRAttrValue(handle, "SPHEROID"));

    // extract the projection:
    if (_name.empty() || _name == "unnamed" || _name == "unknown")
    {
        if (isGeographic())
        {
            _name = getOGRAttrValue(handle, "GEOGCS", 0);
            if (_name.empty())
                _name = getOGRAttrValue(handle, "GEOGCRS");
        }
        else
        {
            _name = getOGRAttrValue(handle, "PROJCS");
            if (_name.empty())
                _name = getOGRAttrValue(handle, "PROJCRS");
        }
    }
    std::string projection = getOGRAttrValue(handle, "PROJECTION");
    if (projection.empty())
        projection = customHandle->projection;
    std::string projection_lc = toLower(projection);

    // check for the Mercator projection:
    _is_mercator = !projection_lc.empty() && projection_lc.find("mercator") == 0;

    // check for spherical mercator (a special case)
    _is_spherical_mercator = _is_mercator && osg::equivalent(
                                                 _ellipsoid.getSemiMajorAxis(),
                                                 _ellipsoid.getSemiMinorAxis());

    // check for the Polar projection:
    if (!projection_lc.empty() && projection_lc.find("polar_stereographic") != std::string::npos)
    {
        double lat = as<double>(getOGRAttrValue(handle, "latitude_of_origin", 0, true), -90.0);
        _is_north_polar = lat > 0.0;
        _is_south_polar = lat < 0.0;
    }
    else
    {
        _is_north_polar = false;
        _is_south_polar = false;
    }

    // Try to extract the horizontal datum
    _datum = getOGRAttrValue(handle, "DATUM", 0, true);
    if (_datum.empty())
        _datum = customHandle->datum;

    // Fix bad return values..
    if (_datum == "wgs_1984" || _datum == "WGS_1984")
        _datum = "WGS84";
    else if (_datum == "nad_1983" || _datum == "NAD_1983")
        _datum = "NAD83";

    // Extract the base units:
    std::string units_name = getOGRAttrValue(handle, "UNIT", 0, true);
    double unitMultiplier = osgEarth::Util::as<double>(getOGRAttrValue(handle, "UNIT", 1, true), 1.0);
    if (units_name.empty())
    {
        if (isGeographic())
        {
            units_name = "degree";
            unitMultiplier = 0.0174532925199433; // 度转弧度
        }
        else
        {
            units_name = "metre";
            unitMultiplier = 1.0;
        }
    }

    if (isGeographic())
        _units = UnitsType(units_name.c_str(), units_name.c_str(), Units::Domain::ANGLE, unitMultiplier);
    else
        _units = UnitsType(units_name.c_str(), units_name.c_str(), Units::Domain::DISTANCE, unitMultiplier);

    _reportedLinearUnits = customHandle->linearUnits;

    // Try to extract the PROJ4 initialization string:
    _proj4 = customHandle->proj4String;

    // Try to extract the OGC well-known-text (WKT) string:
    _wkt = customHandle->wktString;

    if (_name.empty() || _name == "unnamed" || _name == "unknown")
    {
        if (!customHandle->name.empty())
        {
            _name = customHandle->name;
        }
        else
        {
            StringTable proj4_tok;

            auto kvps = StringTokenizer()
                            .whitespaceDelims()
                            .standardQuotes()
                            .tokenize(_proj4);

            StringTokenizer tokenize_kvp;
            tokenize_kvp.delim("=");
            for (auto &kvp : kvps)
            {
                auto tokens = tokenize_kvp(kvp);
                if (tokens.size() == 2)
                    proj4_tok[tokens[0]] = tokens[1];
            }

            if (proj4_tok["+proj"] == "utm")
            {
                _name = Stringify() << "UTM " << proj4_tok["+zone"];
            }
            else
            {
                _name =
                    isGeographic() && !_datum.empty() ? _datum : isGeographic() && !_ellipsoid.getName().empty() ? _ellipsoid.getName()
                                                             : isGeographic()                                    ? "Geographic"
                                                             : isGeocentric()                                    ? "Geocentric"
                                                             : isCube()                                          ? "Unified Cube"
                                                             : isLTP()                                           ? "Tangent Plane"
                                                             : !projection.empty()                               ? projection
                                                             : _is_spherical_mercator                            ? "Spherical Mercator"
                                                             : _is_mercator                                      ? "Mercator"
                                                                                                                 : (!_proj4.empty() ? _proj4 : "Projected");
            }
        }
    }

    // Build a 'normalized' initialization key.
    if (!_proj4.empty())
    {
        _key.horiz = _proj4;
        _key.horizLower = toLower(_key.horiz);
    }
    else if (!_wkt.empty())
    {
        _key.horiz = _wkt;
        _key.horizLower = toLower(_key.horiz);
    }
    if (_vdatum.valid())
    {
        _key.vert = _vdatum->getInitString();
        _key.vertLower = toLower(_key.vert);
    }

    // Guess the appropriate bounds for this SRS.
    _bounds.set(-FLT_MAX, -FLT_MAX, 0.0, FLT_MAX, FLT_MAX, 0.0);

    if (_bounds.xMin() == -FLT_MAX)
    {
        if (isGeographic() || isGeocentric())
        {
            _bounds.set(-180.0, -90.0, 0.0, 180.0, 90.0, 0.0);
        }
        else if (isMercator() || isSphericalMercator())
        {
            _bounds.set(MERC_MINX, MERC_MINY, 0.0, MERC_MAXX, MERC_MAXY, 0.0);
        }
        else if (projection_lc.find("utm") != std::string::npos)
        {
            // 简单的UTM边界估算
            _bounds.set(166000, 0, 0.0, 834000, 9330000, 0.0);
        }
        else if (projection_lc == "equirectangular") // plate carre
        {
            _bounds.set(MERC_MINX, MERC_MINY * 0.5, 0.0, MERC_MAXX, MERC_MAXY * 0.5, 0.0);
        }
    }

    _initialized = true;
}
