check-for-atomics-support.patch 3f49d2964db655e27a83ca5d941c33584ecdd8179659eb9099cecd6ac4b819ec
cmake 3.30.1
features core
fix-library-name.patch 791664e7c66bfd4bf497454d47fe67d61e58a1ed7f1a7ae9cc4b8c18fde26ca2
libjpeg-turbo 296ca75fcb1eae1dff46c32a7821fd5c366312e98bae969dc521098daf59a53e
no_stdc_check.patch 2170f3e0e4badd2061d666d29400aa5a3b183e8eca1532ec8d05888352f7bec9
portfile.cmake 51fdea1e2a310f5ac8bc942b1abd114ee0eb0336aaf8e89a3d141ac5c6ea10d9
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
powershell 7.5.1
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-9322494aa025c04635f453cbc392b0ca170d297c
vcpkg-cmake fa0a421976433b6b0635f7ccfd6e08f02a20aef877e749d6792bc1d9ae8233af
vcpkg-cmake-config 8d261494f5cab6cecd7851edc7eb14f27f3c82cd259e8a56095998d1199973a0
vcpkg.json e8f3d25524fd0bd934af4a7c61b72a9877e61a1cba96680b90a297c3611df05d
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
