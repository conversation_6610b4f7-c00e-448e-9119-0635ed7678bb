CMakeLists.txt 58a8501997b7f4c48cacc3180c2c3ade92b246ec99ea5035e5607b99a8327747
add-config-include.patch 282d8b4716abc154e121bf89ef2db565220e705ce68abf6f626aceb92c10040b
cmake 3.30.1
features core;json1;rtree;tool
fix-arm-uwp.patch f4397f84d931cccb44a038194c895e28c749f1825ab45db4b5dccd6e3b6b5967
portfile.cmake 557e697f8b52224206c11f62e8757faa5da2c88cff676ac428e14765d075cfc2
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
powershell 7.5.1
sqlite3-config.in.cmake f073f9c56d565a12880599c85cea34de45224f61835a1521117707411374f624
sqlite3-vcpkg-config.h.in 044f8cedfe5964996d7cc185e8d4ee51f574da37359b9bbfb05c1f1892bdc71f
sqlite3.pc.in 98f3a7f858317c8a03e2ae14c3420a7e3a6d20491c1e9dbb13d9af279c0609ec
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-9322494aa025c04635f453cbc392b0ca170d297c
usage 1cd1a4fe57d9caf51321f363f1d395074ac7d0c14b7035a5d2c218670be4b5b3
vcpkg-cmake fa0a421976433b6b0635f7ccfd6e08f02a20aef877e749d6792bc1d9ae8233af
vcpkg-cmake-config 8d261494f5cab6cecd7851edc7eb14f27f3c82cd259e8a56095998d1199973a0
vcpkg-cmake-wrapper.cmake 56fcd77229aa401639b09018994a903ed9f0d8bc298e2b4d3bd9899715c1de5d
vcpkg.json 4ccdaf6af945997edfc995e77657ad87351529adbaff7b271b43000767f36282
vcpkg_check_features 943b217e0968d64cf2cb9c272608e6a0b497377e792034f819809a79e1502c2b
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_copy_tools 3d45ff761bddbabe8923b52330168dc3abd295fa469d3f2e47cb14dce85332d5
vcpkg_download_distfile 5b22c0d0a397f4fdd0aa1c054afc84c20c91591d669027688b7c11334c3e4d0e
vcpkg_extract_source_archive 9c733832f8b58b05e6c329ae8b6ad1cfa858224fdc06d4a2d08b428e31de1a51
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
