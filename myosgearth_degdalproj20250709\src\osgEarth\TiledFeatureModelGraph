/* --*-c++-*-- */
/* osgEarth - Geospatial SDK for OpenSceneGraph
 * Copyright 2020 Pelican Mapping
 * MIT License
 */

#ifndef OSGEARTH_TILED_FEATURE_MODEL_GRAPH_H
#define OSGEARTH_TILED_FEATURE_MODEL_GRAPH_H 1

#include <osgEarth/Common>
#include <osgEarth/FeatureSource>
#include <osgEarth/FeatureSourceIndexNode>
#include <osgEarth/Style>
#include <osgEarth/SimplePager>
#include <osgEarth/MapNode>
#include <osgEarth/StyleSheet>

namespace osgEarth {

    /**
     * A scene graph node that renders tiled feature data.
     */
    class OSGEARTH_EXPORT TiledFeatureModelGraph : public SimplePager
    {
    public:
        TiledFeatureModelGraph(const osgEarth::Map* map,
                               FeatureSource* features,
                               StyleSheet* styleSheet,
                               Session* session);

        //! Sets an optional feature filter collection
        void setFilterChain(FeatureFilterChain* chain);

        void setOwnerName(const std::string& value);

    public: // SimplePager

        virtual osg::ref_ptr<osg::Node> createNode(const TileKey& key, ProgressCallback* progress) override;

    private:

        osg::ref_ptr < FeatureSource > _features;
        osg::ref_ptr< StyleSheet > _styleSheet;
        osg::ref_ptr< Session > _session;
        osg::ref_ptr< FeatureFilterChain > _filterChain;
        std::string _ownerName;
        
        osg::ref_ptr<FeatureSourceIndex> _featureIndex;       


        FeatureCursor* createCursor(FeatureSource* fs, FilterContext& cx, const Query& query, ProgressCallback* progress) const;
    };
}

#endif // OSGEARTH_TILED_FEATURE_MODEL_GRAPH_H
