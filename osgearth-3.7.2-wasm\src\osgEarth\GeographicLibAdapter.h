#pragma once

#include <osgEarth/Export>
#include <osgEarth/Common>
#include <string>
#include <memory>

namespace osgEarth
{
    /**
     * GeographicLib适配器类，用于替代PROJ库进行坐标转换
     * 主要支持WGS84地理坐标系与Web墨卡托投影之间的转换
     */
    class OSGEARTH_EXPORT GeographicLibAdapter
    {
    public:
        /**
         * 投影类型枚举
         */
        enum ProjectionType
        {
            GEOGRAPHIC,     // 地理坐标系 (经纬度)
            WEB_MERCATOR,   // Web墨卡托投影
            UTM,            // UTM投影
            TRANSVERSE_MERCATOR, // 横轴墨卡托投影
            UNKNOWN
        };

        /**
         * 坐标转换结果
         */
        struct TransformResult
        {
            bool success;
            double x, y, z;
            std::string error;

            TransformResult() : success(false), x(0), y(0), z(0) {}
            TransformResult(bool s, double _x, double _y, double _z = 0) 
                : success(s), x(_x), y(_y), z(_z) {}
        };

        /**
         * 投影参数结构
         */
        struct ProjectionParams
        {
            ProjectionType type;
            double centralMeridian;  // 中央经线
            double falseEasting;     // 东偏移
            double falseNorthing;    // 北偏移
            double scaleFactor;      // 比例因子
            int utmZone;            // UTM区域号
            bool isNorthern;        // UTM是否为北半球

            ProjectionParams() 
                : type(GEOGRAPHIC), centralMeridian(0), falseEasting(0), 
                  falseNorthing(0), scaleFactor(1.0), utmZone(0), isNorthern(true) {}
        };

    public:
        GeographicLibAdapter();
        ~GeographicLibAdapter();

        /**
         * 从PROJ4字符串解析投影参数
         */
        static ProjectionParams parseProj4String(const std::string& proj4);

        /**
         * 从WKT字符串解析投影参数
         */
        static ProjectionParams parseWKTString(const std::string& wkt);

        /**
         * 地理坐标转Web墨卡托
         * @param lon 经度 (度)
         * @param lat 纬度 (度)
         * @return 转换结果 (x, y 单位为米)
         */
        static TransformResult geographicToWebMercator(double lon, double lat);

        /**
         * Web墨卡托转地理坐标
         * @param x 东坐标 (米)
         * @param y 北坐标 (米)
         * @return 转换结果 (经度, 纬度 单位为度)
         */
        static TransformResult webMercatorToGeographic(double x, double y);

        /**
         * 地理坐标转UTM
         * @param lon 经度 (度)
         * @param lat 纬度 (度)
         * @param zone UTM区域号 (如果为0则自动计算)
         * @param isNorthern 是否为北半球
         * @return 转换结果 (x, y 单位为米)
         */
        static TransformResult geographicToUTM(double lon, double lat, int zone = 0, bool isNorthern = true);

        /**
         * UTM转地理坐标
         * @param x 东坐标 (米)
         * @param y 北坐标 (米)
         * @param zone UTM区域号
         * @param isNorthern 是否为北半球
         * @return 转换结果 (经度, 纬度 单位为度)
         */
        static TransformResult utmToGeographic(double x, double y, int zone, bool isNorthern);

        /**
         * 地理坐标转横轴墨卡托
         * @param lon 经度 (度)
         * @param lat 纬度 (度)
         * @param params 投影参数
         * @return 转换结果 (x, y 单位为米)
         */
        static TransformResult geographicToTransverseMercator(double lon, double lat, const ProjectionParams& params);

        /**
         * 横轴墨卡托转地理坐标
         * @param x 东坐标 (米)
         * @param y 北坐标 (米)
         * @param params 投影参数
         * @return 转换结果 (经度, 纬度 单位为度)
         */
        static TransformResult transverseMercatorToGeographic(double x, double y, const ProjectionParams& params);

        /**
         * 通用坐标转换函数
         * @param x 输入X坐标
         * @param y 输入Y坐标
         * @param z 输入Z坐标
         * @param fromParams 源投影参数
         * @param toParams 目标投影参数
         * @return 转换结果
         */
        static TransformResult transform(double x, double y, double z,
                                       const ProjectionParams& fromParams,
                                       const ProjectionParams& toParams);

        /**
         * 批量坐标转换
         * @param points 坐标点数组 (输入输出)
         * @param count 点数
         * @param fromParams 源投影参数
         * @param toParams 目标投影参数
         * @return 是否成功
         */
        static bool transformArray(double* x, double* y, double* z, unsigned count,
                                 const ProjectionParams& fromParams,
                                 const ProjectionParams& toParams);

        /**
         * 计算UTM区域号
         * @param lon 经度 (度)
         * @return UTM区域号 (1-60)
         */
        static int calculateUTMZone(double lon);

        /**
         * 检查坐标是否在有效范围内
         */
        static bool isValidGeographic(double lon, double lat);
        static bool isValidWebMercator(double x, double y);

    private:
        // 内部辅助函数
        static std::string trim(const std::string& str);
        static std::string toLower(const std::string& str);
        static double parseDouble(const std::string& str, double defaultValue = 0.0);
        static int parseInt(const std::string& str, int defaultValue = 0);
    };

} // namespace osgEarth
