#pragma once

#include <osgEarth/Export>
#include <osgEarth/Common>
#include <string>
#include <memory>

namespace osgEarth
{
    /**
     * GeographicLib适配器类，用于替代PROJ库进行坐标转换
     * 主要支持WGS84地理坐标系与Web墨卡托投影之间的转换
     */
    class OSGEARTH_EXPORT GeographicLibAdapter
    {
    public:
        /**
         * 投影类型枚举
         */
        enum ProjectionType
        {
            GEOGRAPHIC,     // 地理坐标系 (经纬度)
            WEB_MERCATOR,   // Web墨卡托投影
            UTM,            // UTM投影
            TRANSVERSE_MERCATOR, // 横轴墨卡托投影
            UNKNOWN
        };

        /**
         * 坐标转换结果
         */
        struct TransformResult
        {
            bool success;
            double x, y, z;
            std::string error;

            TransformResult() : success(false), x(0), y(0), z(0) {}
            TransformResult(bool s, double _x, double _y, double _z = 0) 
                : success(s), x(_x), y(_y), z(_z) {}
        };

        /**
         * 投影参数结构
         */
        struct ProjectionParams
        {
            ProjectionType type;
            double centralMeridian;  // 中央经线
            double falseEasting;     // 东偏移
            double falseNorthing;    // 北偏移
            double scaleFactor;      // 比例因子
            int utmZone;            // UTM区域号
            bool isNorthern;        // 是否北半球
            std::string datum;      // 基准面
            std::string ellipsoid;  // 椭球体

            ProjectionParams() : type(UNKNOWN), centralMeridian(0), falseEasting(0), 
                               falseNorthing(0), scaleFactor(1.0), utmZone(0), 
                               isNorthern(true), datum("WGS84"), ellipsoid("WGS84") {}
        };

    public:
        GeographicLibAdapter();
        ~GeographicLibAdapter();

        /**
         * 从PROJ4字符串解析投影参数
         */
        static ProjectionParams parseProj4String(const std::string& proj4);

        /**
         * 从WKT字符串解析投影参数
         */
        static ProjectionParams parseWKTString(const std::string& wkt);

        /**
         * 创建投影转换器
         */
        bool createTransform(const ProjectionParams& from, const ProjectionParams& to);

        /**
         * 执行坐标转换
         */
        TransformResult transform(double x, double y, double z = 0.0) const;

        /**
         * 批量坐标转换
         */
        std::vector<TransformResult> transformBatch(const std::vector<std::array<double, 3>>& coords) const;

        /**
         * 地理坐标转Web墨卡托
         */
        static TransformResult geographicToWebMercator(double lon, double lat, double alt = 0.0);

        /**
         * Web墨卡托转地理坐标
         */
        static TransformResult webMercatorToGeographic(double x, double y, double z = 0.0);

        /**
         * 地理坐标转UTM
         */
        static TransformResult geographicToUTM(double lon, double lat, double alt = 0.0, int zone = 0);

        /**
         * UTM转地理坐标
         */
        static TransformResult utmToGeographic(double x, double y, double z, int zone, bool isNorthern = true);

        /**
         * 根据经纬度自动计算UTM区域
         */
        static int calculateUTMZone(double longitude);

        /**
         * 获取错误信息
         */
        const std::string& getLastError() const { return lastError_; }

        /**
         * 检查是否有效
         */
        bool isValid() const { return isValid_; }

    private:
        class Impl;
        std::unique_ptr<Impl> impl_;
        mutable std::string lastError_;
        bool isValid_;

        // 辅助函数
        static double degToRad(double deg) { return deg * M_PI / 180.0; }
        static double radToDeg(double rad) { return rad * 180.0 / M_PI; }
        
        // Web墨卡托投影常数
        static constexpr double WEB_MERCATOR_MAX = 20037508.342789244;
        static constexpr double EARTH_RADIUS = 6378137.0;
    };

    /**
     * 全局辅助函数
     */
    namespace GeographicLibUtils
    {
        /**
         * 检查坐标是否在有效范围内
         */
        OSGEARTH_EXPORT bool isValidGeographic(double lon, double lat);
        
        /**
         * 检查Web墨卡托坐标是否有效
         */
        OSGEARTH_EXPORT bool isValidWebMercator(double x, double y);
        
        /**
         * 标准化经度到[-180, 180]范围
         */
        OSGEARTH_EXPORT double normalizeLongitude(double lon);
        
        /**
         * 标准化纬度到[-90, 90]范围
         */
        OSGEARTH_EXPORT double normalizeLatitude(double lat);
    }
}
