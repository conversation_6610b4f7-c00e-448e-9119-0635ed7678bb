x64-windows/
x64-windows/bin/
x64-windows/bin/geos.dll
x64-windows/bin/geos.pdb
x64-windows/bin/geos_c.dll
x64-windows/bin/geos_c.pdb
x64-windows/debug/
x64-windows/debug/bin/
x64-windows/debug/bin/geos.dll
x64-windows/debug/bin/geos.pdb
x64-windows/debug/bin/geos_c.dll
x64-windows/debug/bin/geos_c.pdb
x64-windows/debug/lib/
x64-windows/debug/lib/geos.lib
x64-windows/debug/lib/geos_c.lib
x64-windows/debug/lib/pkgconfig/
x64-windows/debug/lib/pkgconfig/geos.pc
x64-windows/include/
x64-windows/include/geos.h
x64-windows/include/geos/
x64-windows/include/geos/algorithm/
x64-windows/include/geos/algorithm/Angle.h
x64-windows/include/geos/algorithm/Area.h
x64-windows/include/geos/algorithm/BoundaryNodeRule.h
x64-windows/include/geos/algorithm/CGAlgorithmsDD.h
x64-windows/include/geos/algorithm/CentralEndpointIntersector.h
x64-windows/include/geos/algorithm/Centroid.h
x64-windows/include/geos/algorithm/CircularArcs.h
x64-windows/include/geos/algorithm/ConvexHull.h
x64-windows/include/geos/algorithm/Distance.h
x64-windows/include/geos/algorithm/HCoordinate.h
x64-windows/include/geos/algorithm/InteriorPointArea.h
x64-windows/include/geos/algorithm/InteriorPointLine.h
x64-windows/include/geos/algorithm/InteriorPointPoint.h
x64-windows/include/geos/algorithm/Interpolate.h
x64-windows/include/geos/algorithm/Intersection.h
x64-windows/include/geos/algorithm/Length.h
x64-windows/include/geos/algorithm/LineIntersector.h
x64-windows/include/geos/algorithm/MinimumAreaRectangle.h
x64-windows/include/geos/algorithm/MinimumBoundingCircle.h
x64-windows/include/geos/algorithm/MinimumDiameter.h
x64-windows/include/geos/algorithm/NotRepresentableException.h
x64-windows/include/geos/algorithm/Orientation.h
x64-windows/include/geos/algorithm/PointInRing.h
x64-windows/include/geos/algorithm/PointLocation.h
x64-windows/include/geos/algorithm/PointLocator.h
x64-windows/include/geos/algorithm/PolygonNodeTopology.h
x64-windows/include/geos/algorithm/RayCrossingCounter.h
x64-windows/include/geos/algorithm/Rectangle.h
x64-windows/include/geos/algorithm/RobustDeterminant.h
x64-windows/include/geos/algorithm/SimplePointInRing.h
x64-windows/include/geos/algorithm/construct/
x64-windows/include/geos/algorithm/construct/IndexedDistanceToPoint.h
x64-windows/include/geos/algorithm/construct/IndexedPointInPolygonsLocator.h
x64-windows/include/geos/algorithm/construct/LargestEmptyCircle.h
x64-windows/include/geos/algorithm/construct/MaximumInscribedCircle.h
x64-windows/include/geos/algorithm/distance/
x64-windows/include/geos/algorithm/distance/DiscreteFrechetDistance.h
x64-windows/include/geos/algorithm/distance/DiscreteHausdorffDistance.h
x64-windows/include/geos/algorithm/distance/DistanceToPoint.h
x64-windows/include/geos/algorithm/distance/PointPairDistance.h
x64-windows/include/geos/algorithm/hull/
x64-windows/include/geos/algorithm/hull/ConcaveHull.h
x64-windows/include/geos/algorithm/hull/ConcaveHullOfPolygons.h
x64-windows/include/geos/algorithm/hull/HullTri.h
x64-windows/include/geos/algorithm/hull/HullTriangulation.h
x64-windows/include/geos/algorithm/locate/
x64-windows/include/geos/algorithm/locate/IndexedPointInAreaLocator.h
x64-windows/include/geos/algorithm/locate/PointOnGeometryLocator.h
x64-windows/include/geos/algorithm/locate/SimplePointInAreaLocator.h
x64-windows/include/geos/constants.h
x64-windows/include/geos/coverage/
x64-windows/include/geos/coverage/Corner.h
x64-windows/include/geos/coverage/CoverageBoundarySegmentFinder.h
x64-windows/include/geos/coverage/CoverageEdge.h
x64-windows/include/geos/coverage/CoverageGapFinder.h
x64-windows/include/geos/coverage/CoveragePolygon.h
x64-windows/include/geos/coverage/CoveragePolygonValidator.h
x64-windows/include/geos/coverage/CoverageRing.h
x64-windows/include/geos/coverage/CoverageRingEdges.h
x64-windows/include/geos/coverage/CoverageSimplifier.h
x64-windows/include/geos/coverage/CoverageUnion.h
x64-windows/include/geos/coverage/CoverageValidator.h
x64-windows/include/geos/coverage/InvalidSegmentDetector.h
x64-windows/include/geos/coverage/TPVWSimplifier.h
x64-windows/include/geos/coverage/VertexRingCounter.h
x64-windows/include/geos/edgegraph/
x64-windows/include/geos/edgegraph/EdgeGraph.h
x64-windows/include/geos/edgegraph/EdgeGraphBuilder.h
x64-windows/include/geos/edgegraph/HalfEdge.h
x64-windows/include/geos/edgegraph/MarkHalfEdge.h
x64-windows/include/geos/export.h
x64-windows/include/geos/geom.h
x64-windows/include/geos/geom/
x64-windows/include/geos/geom/CircularArc.h
x64-windows/include/geos/geom/CircularString.h
x64-windows/include/geos/geom/CompoundCurve.h
x64-windows/include/geos/geom/Coordinate.h
x64-windows/include/geos/geom/CoordinateFilter.h
x64-windows/include/geos/geom/CoordinateList.h
x64-windows/include/geos/geom/CoordinateSequence.h
x64-windows/include/geos/geom/CoordinateSequenceFilter.h
x64-windows/include/geos/geom/CoordinateSequenceIterator.h
x64-windows/include/geos/geom/CoordinateSequences.h
x64-windows/include/geos/geom/Curve.h
x64-windows/include/geos/geom/CurvePolygon.h
x64-windows/include/geos/geom/Dimension.h
x64-windows/include/geos/geom/Envelope.h
x64-windows/include/geos/geom/Geometry.h
x64-windows/include/geos/geom/GeometryCollection.h
x64-windows/include/geos/geom/GeometryComponentFilter.h
x64-windows/include/geos/geom/GeometryFactory.h
x64-windows/include/geos/geom/GeometryFilter.h
x64-windows/include/geos/geom/GeometryTypeName.h
x64-windows/include/geos/geom/HeuristicOverlay.h
x64-windows/include/geos/geom/IntersectionMatrix.h
x64-windows/include/geos/geom/LineSegment.h
x64-windows/include/geos/geom/LineString.h
x64-windows/include/geos/geom/LinearRing.h
x64-windows/include/geos/geom/Location.h
x64-windows/include/geos/geom/MultiCurve.h
x64-windows/include/geos/geom/MultiLineString.h
x64-windows/include/geos/geom/MultiPoint.h
x64-windows/include/geos/geom/MultiPolygon.h
x64-windows/include/geos/geom/MultiSurface.h
x64-windows/include/geos/geom/Point.h
x64-windows/include/geos/geom/Polygon.h
x64-windows/include/geos/geom/Position.h
x64-windows/include/geos/geom/PrecisionModel.h
x64-windows/include/geos/geom/Quadrant.h
x64-windows/include/geos/geom/SimpleCurve.h
x64-windows/include/geos/geom/Surface.h
x64-windows/include/geos/geom/SurfaceImpl.h
x64-windows/include/geos/geom/Triangle.h
x64-windows/include/geos/geom/prep/
x64-windows/include/geos/geom/prep/AbstractPreparedPolygonContains.h
x64-windows/include/geos/geom/prep/BasicPreparedGeometry.h
x64-windows/include/geos/geom/prep/PreparedGeometry.h
x64-windows/include/geos/geom/prep/PreparedGeometryFactory.h
x64-windows/include/geos/geom/prep/PreparedLineString.h
x64-windows/include/geos/geom/prep/PreparedLineStringDistance.h
x64-windows/include/geos/geom/prep/PreparedLineStringIntersects.h
x64-windows/include/geos/geom/prep/PreparedLineStringNearestPoints.h
x64-windows/include/geos/geom/prep/PreparedPoint.h
x64-windows/include/geos/geom/prep/PreparedPolygon.h
x64-windows/include/geos/geom/prep/PreparedPolygonContains.h
x64-windows/include/geos/geom/prep/PreparedPolygonContainsProperly.h
x64-windows/include/geos/geom/prep/PreparedPolygonCovers.h
x64-windows/include/geos/geom/prep/PreparedPolygonDistance.h
x64-windows/include/geos/geom/prep/PreparedPolygonIntersects.h
x64-windows/include/geos/geom/prep/PreparedPolygonPredicate.h
x64-windows/include/geos/geom/util/
x64-windows/include/geos/geom/util/ComponentCoordinateExtracter.h
x64-windows/include/geos/geom/util/CoordinateOperation.h
x64-windows/include/geos/geom/util/Densifier.h
x64-windows/include/geos/geom/util/GeometryCombiner.h
x64-windows/include/geos/geom/util/GeometryEditor.h
x64-windows/include/geos/geom/util/GeometryEditorOperation.h
x64-windows/include/geos/geom/util/GeometryExtracter.h
x64-windows/include/geos/geom/util/GeometryFixer.h
x64-windows/include/geos/geom/util/GeometryLister.h
x64-windows/include/geos/geom/util/GeometryMapper.h
x64-windows/include/geos/geom/util/GeometryTransformer.h
x64-windows/include/geos/geom/util/LinearComponentExtracter.h
x64-windows/include/geos/geom/util/NoOpGeometryOperation.h
x64-windows/include/geos/geom/util/PointExtracter.h
x64-windows/include/geos/geom/util/PolygonExtracter.h
x64-windows/include/geos/geom/util/PolygonalExtracter.h
x64-windows/include/geos/geom/util/ShortCircuitedGeometryVisitor.h
x64-windows/include/geos/geom/util/SineStarFactory.h
x64-windows/include/geos/geomgraph/
x64-windows/include/geos/geomgraph/Depth.h
x64-windows/include/geos/geomgraph/DirectedEdge.h
x64-windows/include/geos/geomgraph/DirectedEdgeStar.h
x64-windows/include/geos/geomgraph/Edge.h
x64-windows/include/geos/geomgraph/EdgeEnd.h
x64-windows/include/geos/geomgraph/EdgeEndStar.h
x64-windows/include/geos/geomgraph/EdgeIntersection.h
x64-windows/include/geos/geomgraph/EdgeIntersectionList.h
x64-windows/include/geos/geomgraph/EdgeList.h
x64-windows/include/geos/geomgraph/EdgeNodingValidator.h
x64-windows/include/geos/geomgraph/EdgeRing.h
x64-windows/include/geos/geomgraph/GeometryGraph.h
x64-windows/include/geos/geomgraph/GraphComponent.h
x64-windows/include/geos/geomgraph/Label.h
x64-windows/include/geos/geomgraph/Node.h
x64-windows/include/geos/geomgraph/NodeFactory.h
x64-windows/include/geos/geomgraph/NodeMap.h
x64-windows/include/geos/geomgraph/PlanarGraph.h
x64-windows/include/geos/geomgraph/TopologyLocation.h
x64-windows/include/geos/geomgraph/index/
x64-windows/include/geos/geomgraph/index/EdgeSetIntersector.h
x64-windows/include/geos/geomgraph/index/MonotoneChain.h
x64-windows/include/geos/geomgraph/index/MonotoneChainEdge.h
x64-windows/include/geos/geomgraph/index/MonotoneChainIndexer.h
x64-windows/include/geos/geomgraph/index/SegmentIntersector.h
x64-windows/include/geos/geomgraph/index/SimpleEdgeSetIntersector.h
x64-windows/include/geos/geomgraph/index/SimpleMCSweepLineIntersector.h
x64-windows/include/geos/geomgraph/index/SimpleSweepLineIntersector.h
x64-windows/include/geos/geomgraph/index/SweepLineEvent.h
x64-windows/include/geos/geomgraph/index/SweepLineEventObj.h
x64-windows/include/geos/geomgraph/index/SweepLineSegment.h
x64-windows/include/geos/index/
x64-windows/include/geos/index/ItemVisitor.h
x64-windows/include/geos/index/SpatialIndex.h
x64-windows/include/geos/index/VertexSequencePackedRtree.h
x64-windows/include/geos/index/bintree/
x64-windows/include/geos/index/bintree/Bintree.h
x64-windows/include/geos/index/bintree/Interval.h
x64-windows/include/geos/index/bintree/Key.h
x64-windows/include/geos/index/bintree/Node.h
x64-windows/include/geos/index/bintree/NodeBase.h
x64-windows/include/geos/index/bintree/Root.h
x64-windows/include/geos/index/chain/
x64-windows/include/geos/index/chain/MonotoneChain.h
x64-windows/include/geos/index/chain/MonotoneChainBuilder.h
x64-windows/include/geos/index/chain/MonotoneChainOverlapAction.h
x64-windows/include/geos/index/chain/MonotoneChainSelectAction.h
x64-windows/include/geos/index/intervalrtree/
x64-windows/include/geos/index/intervalrtree/IntervalRTreeBranchNode.h
x64-windows/include/geos/index/intervalrtree/IntervalRTreeLeafNode.h
x64-windows/include/geos/index/intervalrtree/IntervalRTreeNode.h
x64-windows/include/geos/index/intervalrtree/SortedPackedIntervalRTree.h
x64-windows/include/geos/index/kdtree/
x64-windows/include/geos/index/kdtree/KdNode.h
x64-windows/include/geos/index/kdtree/KdNodeVisitor.h
x64-windows/include/geos/index/kdtree/KdTree.h
x64-windows/include/geos/index/quadtree/
x64-windows/include/geos/index/quadtree/IntervalSize.h
x64-windows/include/geos/index/quadtree/Key.h
x64-windows/include/geos/index/quadtree/Node.h
x64-windows/include/geos/index/quadtree/NodeBase.h
x64-windows/include/geos/index/quadtree/Quadtree.h
x64-windows/include/geos/index/quadtree/Root.h
x64-windows/include/geos/index/strtree/
x64-windows/include/geos/index/strtree/AbstractNode.h
x64-windows/include/geos/index/strtree/AbstractSTRtree.h
x64-windows/include/geos/index/strtree/Boundable.h
x64-windows/include/geos/index/strtree/BoundablePair.h
x64-windows/include/geos/index/strtree/EnvelopeUtil.h
x64-windows/include/geos/index/strtree/GeometryItemDistance.h
x64-windows/include/geos/index/strtree/Interval.h
x64-windows/include/geos/index/strtree/ItemBoundable.h
x64-windows/include/geos/index/strtree/ItemDistance.h
x64-windows/include/geos/index/strtree/SIRtree.h
x64-windows/include/geos/index/strtree/STRtree.h
x64-windows/include/geos/index/strtree/SimpleSTRdistance.h
x64-windows/include/geos/index/strtree/SimpleSTRnode.h
x64-windows/include/geos/index/strtree/SimpleSTRtree.h
x64-windows/include/geos/index/strtree/TemplateSTRNode.h
x64-windows/include/geos/index/strtree/TemplateSTRNodePair.h
x64-windows/include/geos/index/strtree/TemplateSTRtree.h
x64-windows/include/geos/index/strtree/TemplateSTRtreeDistance.h
x64-windows/include/geos/index/sweepline/
x64-windows/include/geos/index/sweepline/SweepLineEvent.h
x64-windows/include/geos/index/sweepline/SweepLineIndex.h
x64-windows/include/geos/index/sweepline/SweepLineInterval.h
x64-windows/include/geos/index/sweepline/SweepLineOverlapAction.h
x64-windows/include/geos/io/
x64-windows/include/geos/io/ByteOrderDataInStream.h
x64-windows/include/geos/io/ByteOrderValues.h
x64-windows/include/geos/io/CLocalizer.h
x64-windows/include/geos/io/CheckOrdinatesFilter.h
x64-windows/include/geos/io/GeoJSON.h
x64-windows/include/geos/io/GeoJSONReader.h
x64-windows/include/geos/io/GeoJSONWriter.h
x64-windows/include/geos/io/OrdinateSet.h
x64-windows/include/geos/io/ParseException.h
x64-windows/include/geos/io/StringTokenizer.h
x64-windows/include/geos/io/WKBConstants.h
x64-windows/include/geos/io/WKBReader.h
x64-windows/include/geos/io/WKBStreamReader.h
x64-windows/include/geos/io/WKBWriter.h
x64-windows/include/geos/io/WKTFileReader.h
x64-windows/include/geos/io/WKTReader.h
x64-windows/include/geos/io/WKTStreamReader.h
x64-windows/include/geos/io/WKTWriter.h
x64-windows/include/geos/io/Writer.h
x64-windows/include/geos/linearref/
x64-windows/include/geos/linearref/ExtractLineByLocation.h
x64-windows/include/geos/linearref/LengthIndexOfPoint.h
x64-windows/include/geos/linearref/LengthIndexedLine.h
x64-windows/include/geos/linearref/LengthLocationMap.h
x64-windows/include/geos/linearref/LinearGeometryBuilder.h
x64-windows/include/geos/linearref/LinearIterator.h
x64-windows/include/geos/linearref/LinearLocation.h
x64-windows/include/geos/linearref/LocationIndexOfLine.h
x64-windows/include/geos/linearref/LocationIndexOfPoint.h
x64-windows/include/geos/linearref/LocationIndexedLine.h
x64-windows/include/geos/math/
x64-windows/include/geos/math/DD.h
x64-windows/include/geos/namespaces.h
x64-windows/include/geos/noding/
x64-windows/include/geos/noding/BasicSegmentString.h
x64-windows/include/geos/noding/BoundaryChainNoder.h
x64-windows/include/geos/noding/FastNodingValidator.h
x64-windows/include/geos/noding/FastSegmentSetIntersectionFinder.h
x64-windows/include/geos/noding/GeometryNoder.h
x64-windows/include/geos/noding/IntersectionAdder.h
x64-windows/include/geos/noding/IntersectionFinderAdder.h
x64-windows/include/geos/noding/IteratedNoder.h
x64-windows/include/geos/noding/MCIndexNoder.h
x64-windows/include/geos/noding/MCIndexSegmentSetMutualIntersector.h
x64-windows/include/geos/noding/NodableSegmentString.h
x64-windows/include/geos/noding/NodedSegmentString.h
x64-windows/include/geos/noding/Noder.h
x64-windows/include/geos/noding/NodingIntersectionFinder.h
x64-windows/include/geos/noding/NodingValidator.h
x64-windows/include/geos/noding/Octant.h
x64-windows/include/geos/noding/OrientedCoordinateArray.h
x64-windows/include/geos/noding/ScaledNoder.h
x64-windows/include/geos/noding/SegmentExtractingNoder.h
x64-windows/include/geos/noding/SegmentIntersectionDetector.h
x64-windows/include/geos/noding/SegmentIntersector.h
x64-windows/include/geos/noding/SegmentNode.h
x64-windows/include/geos/noding/SegmentNodeList.h
x64-windows/include/geos/noding/SegmentPointComparator.h
x64-windows/include/geos/noding/SegmentSetMutualIntersector.h
x64-windows/include/geos/noding/SegmentString.h
x64-windows/include/geos/noding/SegmentStringUtil.h
x64-windows/include/geos/noding/SimpleNoder.h
x64-windows/include/geos/noding/SinglePassNoder.h
x64-windows/include/geos/noding/ValidatingNoder.h
x64-windows/include/geos/noding/snap/
x64-windows/include/geos/noding/snap/SnappingIntersectionAdder.h
x64-windows/include/geos/noding/snap/SnappingNoder.h
x64-windows/include/geos/noding/snap/SnappingPointIndex.h
x64-windows/include/geos/noding/snapround/
x64-windows/include/geos/noding/snapround/HotPixel.h
x64-windows/include/geos/noding/snapround/HotPixelIndex.h
x64-windows/include/geos/noding/snapround/MCIndexPointSnapper.h
x64-windows/include/geos/noding/snapround/MCIndexSnapRounder.h
x64-windows/include/geos/noding/snapround/SnapRoundingIntersectionAdder.h
x64-windows/include/geos/noding/snapround/SnapRoundingNoder.h
x64-windows/include/geos/operation/
x64-windows/include/geos/operation/BoundaryOp.h
x64-windows/include/geos/operation/GeometryGraphOperation.h
x64-windows/include/geos/operation/buffer/
x64-windows/include/geos/operation/buffer/BufferBuilder.h
x64-windows/include/geos/operation/buffer/BufferCurveSetBuilder.h
x64-windows/include/geos/operation/buffer/BufferInputLineSimplifier.h
x64-windows/include/geos/operation/buffer/BufferOp.h
x64-windows/include/geos/operation/buffer/BufferParameters.h
x64-windows/include/geos/operation/buffer/BufferSubgraph.h
x64-windows/include/geos/operation/buffer/OffsetCurve.h
x64-windows/include/geos/operation/buffer/OffsetCurveBuilder.h
x64-windows/include/geos/operation/buffer/OffsetCurveSection.h
x64-windows/include/geos/operation/buffer/OffsetSegmentGenerator.h
x64-windows/include/geos/operation/buffer/OffsetSegmentString.h
x64-windows/include/geos/operation/buffer/RightmostEdgeFinder.h
x64-windows/include/geos/operation/buffer/SegmentMCIndex.h
x64-windows/include/geos/operation/buffer/SubgraphDepthLocater.h
x64-windows/include/geos/operation/cluster/
x64-windows/include/geos/operation/cluster/AbstractClusterFinder.h
x64-windows/include/geos/operation/cluster/Clusters.h
x64-windows/include/geos/operation/cluster/DBSCANClusterFinder.h
x64-windows/include/geos/operation/cluster/DisjointOperation.h
x64-windows/include/geos/operation/cluster/EnvelopeDistanceClusterFinder.h
x64-windows/include/geos/operation/cluster/EnvelopeIntersectsClusterFinder.h
x64-windows/include/geos/operation/cluster/GeometryDistanceClusterFinder.h
x64-windows/include/geos/operation/cluster/GeometryFlattener.h
x64-windows/include/geos/operation/cluster/GeometryIntersectsClusterFinder.h
x64-windows/include/geos/operation/cluster/UnionFind.h
x64-windows/include/geos/operation/distance/
x64-windows/include/geos/operation/distance/ConnectedElementLocationFilter.h
x64-windows/include/geos/operation/distance/ConnectedElementPointFilter.h
x64-windows/include/geos/operation/distance/DistanceOp.h
x64-windows/include/geos/operation/distance/FacetSequence.h
x64-windows/include/geos/operation/distance/FacetSequenceTreeBuilder.h
x64-windows/include/geos/operation/distance/GeometryLocation.h
x64-windows/include/geos/operation/distance/IndexedFacetDistance.h
x64-windows/include/geos/operation/intersection/
x64-windows/include/geos/operation/intersection/Rectangle.h
x64-windows/include/geos/operation/intersection/RectangleIntersection.h
x64-windows/include/geos/operation/intersection/RectangleIntersectionBuilder.h
x64-windows/include/geos/operation/linemerge/
x64-windows/include/geos/operation/linemerge/EdgeString.h
x64-windows/include/geos/operation/linemerge/LineMergeDirectedEdge.h
x64-windows/include/geos/operation/linemerge/LineMergeEdge.h
x64-windows/include/geos/operation/linemerge/LineMergeGraph.h
x64-windows/include/geos/operation/linemerge/LineMerger.h
x64-windows/include/geos/operation/linemerge/LineSequencer.h
x64-windows/include/geos/operation/overlay/
x64-windows/include/geos/operation/overlay/MaximalEdgeRing.h
x64-windows/include/geos/operation/overlay/MinimalEdgeRing.h
x64-windows/include/geos/operation/overlay/OverlayNodeFactory.h
x64-windows/include/geos/operation/overlay/PolygonBuilder.h
x64-windows/include/geos/operation/overlay/snap/
x64-windows/include/geos/operation/overlay/snap/GeometrySnapper.h
x64-windows/include/geos/operation/overlay/snap/LineStringSnapper.h
x64-windows/include/geos/operation/overlay/snap/SnapOverlayOp.h
x64-windows/include/geos/operation/overlay/validate/
x64-windows/include/geos/operation/overlay/validate/FuzzyPointLocator.h
x64-windows/include/geos/operation/overlay/validate/OffsetPointGenerator.h
x64-windows/include/geos/operation/overlay/validate/OverlayResultValidator.h
x64-windows/include/geos/operation/overlayng/
x64-windows/include/geos/operation/overlayng/CoverageUnion.h
x64-windows/include/geos/operation/overlayng/Edge.h
x64-windows/include/geos/operation/overlayng/EdgeKey.h
x64-windows/include/geos/operation/overlayng/EdgeMerger.h
x64-windows/include/geos/operation/overlayng/EdgeNodingBuilder.h
x64-windows/include/geos/operation/overlayng/EdgeSourceInfo.h
x64-windows/include/geos/operation/overlayng/ElevationModel.h
x64-windows/include/geos/operation/overlayng/IndexedPointOnLineLocator.h
x64-windows/include/geos/operation/overlayng/InputGeometry.h
x64-windows/include/geos/operation/overlayng/IntersectionPointBuilder.h
x64-windows/include/geos/operation/overlayng/LineBuilder.h
x64-windows/include/geos/operation/overlayng/LineLimiter.h
x64-windows/include/geos/operation/overlayng/MaximalEdgeRing.h
x64-windows/include/geos/operation/overlayng/OverlayEdge.h
x64-windows/include/geos/operation/overlayng/OverlayEdgeRing.h
x64-windows/include/geos/operation/overlayng/OverlayGraph.h
x64-windows/include/geos/operation/overlayng/OverlayLabel.h
x64-windows/include/geos/operation/overlayng/OverlayLabeller.h
x64-windows/include/geos/operation/overlayng/OverlayMixedPoints.h
x64-windows/include/geos/operation/overlayng/OverlayNG.h
x64-windows/include/geos/operation/overlayng/OverlayNGRobust.h
x64-windows/include/geos/operation/overlayng/OverlayPoints.h
x64-windows/include/geos/operation/overlayng/OverlayUtil.h
x64-windows/include/geos/operation/overlayng/PolygonBuilder.h
x64-windows/include/geos/operation/overlayng/PrecisionReducer.h
x64-windows/include/geos/operation/overlayng/PrecisionUtil.h
x64-windows/include/geos/operation/overlayng/RingClipper.h
x64-windows/include/geos/operation/overlayng/RobustClipEnvelopeComputer.h
x64-windows/include/geos/operation/overlayng/UnaryUnionNG.h
x64-windows/include/geos/operation/polygonize/
x64-windows/include/geos/operation/polygonize/BuildArea.h
x64-windows/include/geos/operation/polygonize/EdgeRing.h
x64-windows/include/geos/operation/polygonize/HoleAssigner.h
x64-windows/include/geos/operation/polygonize/PolygonizeDirectedEdge.h
x64-windows/include/geos/operation/polygonize/PolygonizeEdge.h
x64-windows/include/geos/operation/polygonize/PolygonizeGraph.h
x64-windows/include/geos/operation/polygonize/Polygonizer.h
x64-windows/include/geos/operation/predicate/
x64-windows/include/geos/operation/predicate/RectangleContains.h
x64-windows/include/geos/operation/predicate/RectangleIntersects.h
x64-windows/include/geos/operation/predicate/SegmentIntersectionTester.h
x64-windows/include/geos/operation/relate/
x64-windows/include/geos/operation/relate/EdgeEndBuilder.h
x64-windows/include/geos/operation/relate/EdgeEndBundle.h
x64-windows/include/geos/operation/relate/EdgeEndBundleStar.h
x64-windows/include/geos/operation/relate/RelateComputer.h
x64-windows/include/geos/operation/relate/RelateNode.h
x64-windows/include/geos/operation/relate/RelateNodeFactory.h
x64-windows/include/geos/operation/relate/RelateNodeGraph.h
x64-windows/include/geos/operation/relate/RelateOp.h
x64-windows/include/geos/operation/relateng/
x64-windows/include/geos/operation/relateng/AdjacentEdgeLocator.h
x64-windows/include/geos/operation/relateng/BasicPredicate.h
x64-windows/include/geos/operation/relateng/DimensionLocation.h
x64-windows/include/geos/operation/relateng/EdgeSegmentIntersector.h
x64-windows/include/geos/operation/relateng/EdgeSegmentOverlapAction.h
x64-windows/include/geos/operation/relateng/EdgeSetIntersector.h
x64-windows/include/geos/operation/relateng/IMPatternMatcher.h
x64-windows/include/geos/operation/relateng/IMPredicate.h
x64-windows/include/geos/operation/relateng/IntersectionMatrixPattern.h
x64-windows/include/geos/operation/relateng/LineStringExtracter.h
x64-windows/include/geos/operation/relateng/LinearBoundary.h
x64-windows/include/geos/operation/relateng/NodeSection.h
x64-windows/include/geos/operation/relateng/NodeSections.h
x64-windows/include/geos/operation/relateng/PolygonNodeConverter.h
x64-windows/include/geos/operation/relateng/RelateEdge.h
x64-windows/include/geos/operation/relateng/RelateGeometry.h
x64-windows/include/geos/operation/relateng/RelateMatrixPredicate.h
x64-windows/include/geos/operation/relateng/RelateNG.h
x64-windows/include/geos/operation/relateng/RelateNode.h
x64-windows/include/geos/operation/relateng/RelatePointLocator.h
x64-windows/include/geos/operation/relateng/RelatePredicate.h
x64-windows/include/geos/operation/relateng/RelateSegmentString.h
x64-windows/include/geos/operation/relateng/TopologyComputer.h
x64-windows/include/geos/operation/relateng/TopologyPredicate.h
x64-windows/include/geos/operation/sharedpaths/
x64-windows/include/geos/operation/sharedpaths/SharedPathsOp.h
x64-windows/include/geos/operation/union/
x64-windows/include/geos/operation/union/CascadedPolygonUnion.h
x64-windows/include/geos/operation/union/CoverageUnion.h
x64-windows/include/geos/operation/union/DisjointSubsetUnion.h
x64-windows/include/geos/operation/union/OverlapUnion.h
x64-windows/include/geos/operation/union/PointGeometryUnion.h
x64-windows/include/geos/operation/union/UnaryUnionOp.h
x64-windows/include/geos/operation/union/UnionStrategy.h
x64-windows/include/geos/operation/valid/
x64-windows/include/geos/operation/valid/ConsistentAreaTester.h
x64-windows/include/geos/operation/valid/IndexedNestedHoleTester.h
x64-windows/include/geos/operation/valid/IndexedNestedPolygonTester.h
x64-windows/include/geos/operation/valid/IsSimpleOp.h
x64-windows/include/geos/operation/valid/IsValidOp.h
x64-windows/include/geos/operation/valid/MakeValid.h
x64-windows/include/geos/operation/valid/PolygonIntersectionAnalyzer.h
x64-windows/include/geos/operation/valid/PolygonRing.h
x64-windows/include/geos/operation/valid/PolygonRingSelfNode.h
x64-windows/include/geos/operation/valid/PolygonRingTouch.h
x64-windows/include/geos/operation/valid/PolygonTopologyAnalyzer.h
x64-windows/include/geos/operation/valid/RepeatedPointRemover.h
x64-windows/include/geos/operation/valid/RepeatedPointTester.h
x64-windows/include/geos/operation/valid/TopologyValidationError.h
x64-windows/include/geos/planargraph/
x64-windows/include/geos/planargraph/DirectedEdge.h
x64-windows/include/geos/planargraph/DirectedEdgeStar.h
x64-windows/include/geos/planargraph/Edge.h
x64-windows/include/geos/planargraph/GraphComponent.h
x64-windows/include/geos/planargraph/Node.h
x64-windows/include/geos/planargraph/NodeMap.h
x64-windows/include/geos/planargraph/PlanarGraph.h
x64-windows/include/geos/planargraph/Subgraph.h
x64-windows/include/geos/planargraph/algorithm/
x64-windows/include/geos/planargraph/algorithm/ConnectedSubgraphFinder.h
x64-windows/include/geos/precision/
x64-windows/include/geos/precision/CommonBits.h
x64-windows/include/geos/precision/CommonBitsOp.h
x64-windows/include/geos/precision/CommonBitsRemover.h
x64-windows/include/geos/precision/EnhancedPrecisionOp.h
x64-windows/include/geos/precision/GeometryPrecisionReducer.h
x64-windows/include/geos/precision/MinimumClearance.h
x64-windows/include/geos/precision/PointwisePrecisionReducerTransformer.h
x64-windows/include/geos/precision/PrecisionReducerCoordinateOperation.h
x64-windows/include/geos/precision/PrecisionReducerTransformer.h
x64-windows/include/geos/precision/SimpleGeometryPrecisionReducer.h
x64-windows/include/geos/profiler.h
x64-windows/include/geos/shape/
x64-windows/include/geos/shape/fractal/
x64-windows/include/geos/shape/fractal/HilbertCode.h
x64-windows/include/geos/shape/fractal/HilbertEncoder.h
x64-windows/include/geos/shape/fractal/MortonCode.h
x64-windows/include/geos/simplify/
x64-windows/include/geos/simplify/ComponentJumpChecker.h
x64-windows/include/geos/simplify/DouglasPeuckerLineSimplifier.h
x64-windows/include/geos/simplify/DouglasPeuckerSimplifier.h
x64-windows/include/geos/simplify/LineSegmentIndex.h
x64-windows/include/geos/simplify/LinkedLine.h
x64-windows/include/geos/simplify/LinkedRing.h
x64-windows/include/geos/simplify/PolygonHullSimplifier.h
x64-windows/include/geos/simplify/RingHull.h
x64-windows/include/geos/simplify/RingHullIndex.h
x64-windows/include/geos/simplify/TaggedLineSegment.h
x64-windows/include/geos/simplify/TaggedLineString.h
x64-windows/include/geos/simplify/TaggedLineStringSimplifier.h
x64-windows/include/geos/simplify/TaggedLinesSimplifier.h
x64-windows/include/geos/simplify/TopologyPreservingSimplifier.h
x64-windows/include/geos/triangulate/
x64-windows/include/geos/triangulate/DelaunayTriangulationBuilder.h
x64-windows/include/geos/triangulate/IncrementalDelaunayTriangulator.h
x64-windows/include/geos/triangulate/VoronoiDiagramBuilder.h
x64-windows/include/geos/triangulate/polygon/
x64-windows/include/geos/triangulate/polygon/ConstrainedDelaunayTriangulator.h
x64-windows/include/geos/triangulate/polygon/PolygonEarClipper.h
x64-windows/include/geos/triangulate/polygon/PolygonHoleJoiner.h
x64-windows/include/geos/triangulate/polygon/PolygonNoder.h
x64-windows/include/geos/triangulate/polygon/PolygonTriangulator.h
x64-windows/include/geos/triangulate/polygon/TriDelaunayImprover.h
x64-windows/include/geos/triangulate/quadedge/
x64-windows/include/geos/triangulate/quadedge/LastFoundQuadEdgeLocator.h
x64-windows/include/geos/triangulate/quadedge/LocateFailureException.h
x64-windows/include/geos/triangulate/quadedge/QuadEdge.h
x64-windows/include/geos/triangulate/quadedge/QuadEdgeLocator.h
x64-windows/include/geos/triangulate/quadedge/QuadEdgeQuartet.h
x64-windows/include/geos/triangulate/quadedge/QuadEdgeSubdivision.h
x64-windows/include/geos/triangulate/quadedge/TrianglePredicate.h
x64-windows/include/geos/triangulate/quadedge/TriangleVisitor.h
x64-windows/include/geos/triangulate/quadedge/Vertex.h
x64-windows/include/geos/triangulate/tri/
x64-windows/include/geos/triangulate/tri/Tri.h
x64-windows/include/geos/triangulate/tri/TriEdge.h
x64-windows/include/geos/triangulate/tri/TriList.h
x64-windows/include/geos/triangulate/tri/TriangulationBuilder.h
x64-windows/include/geos/unload.h
x64-windows/include/geos/util.h
x64-windows/include/geos/util/
x64-windows/include/geos/util/Assert.h
x64-windows/include/geos/util/AssertionFailedException.h
x64-windows/include/geos/util/CoordinateArrayFilter.h
x64-windows/include/geos/util/GEOSException.h
x64-windows/include/geos/util/GeometricShapeFactory.h
x64-windows/include/geos/util/IllegalArgumentException.h
x64-windows/include/geos/util/IllegalStateException.h
x64-windows/include/geos/util/Interrupt.h
x64-windows/include/geos/util/Machine.h
x64-windows/include/geos/util/TopologyException.h
x64-windows/include/geos/util/UniqueCoordinateArrayFilter.h
x64-windows/include/geos/util/UnsupportedOperationException.h
x64-windows/include/geos/util/math.h
x64-windows/include/geos/util/string.h
x64-windows/include/geos/vend/
x64-windows/include/geos/vend/include_nlohmann_json.hpp
x64-windows/include/geos/vend/json.hpp
x64-windows/include/geos/version.h
x64-windows/include/geos_c.h
x64-windows/lib/
x64-windows/lib/geos.lib
x64-windows/lib/geos_c.lib
x64-windows/lib/pkgconfig/
x64-windows/lib/pkgconfig/geos.pc
x64-windows/share/
x64-windows/share/geos/
x64-windows/share/geos/copyright
x64-windows/share/geos/geos-config-version.cmake
x64-windows/share/geos/geos-config.cmake
x64-windows/share/geos/geos-targets-debug.cmake
x64-windows/share/geos/geos-targets-release.cmake
x64-windows/share/geos/geos-targets.cmake
x64-windows/share/geos/usage
x64-windows/share/geos/vcpkg.spdx.json
x64-windows/share/geos/vcpkg_abi_info.txt
