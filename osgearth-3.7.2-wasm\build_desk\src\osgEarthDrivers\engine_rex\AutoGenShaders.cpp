// ***DO NOT EDIT THIS FILE - IT IS AUTOMATICALLY GENERATED BY CMAKE***

#include <osgEarthDrivers/engine_rex/Shaders>

using namespace osgEarth::REX;

ShadersGL3::ShadersGL3()
{
    ENGINE_VERT = "RexEngine.vert.glsl";
    _sources[ENGINE_VERT] = 
R"(#pragma vp_name REX Engine - Init Model Space
#pragma vp_function oe_rex_init_model, vertex_model, first
// uniforms
uniform vec4 oe_terrain_color;
uniform vec4 oe_tile_key_u;
// outputs
out vec4 vp_Color;
out vec4 oe_layer_tilec;
out vec4 oe_terrain_tessLevel;
out float oe_rex_morphFactor;
flat out int oe_terrain_vertexMarker;
// stage globals
vec4 oe_tile_key;
void oe_rex_init_model(inout vec4 vertexModel)
{
    // Texture coordinate for the tile (always 0..1)
    oe_layer_tilec = gl_MultiTexCoord0;
    // Extract the vertex type marker
    //oe_terrain_vertexMarker = int(oe_layer_tilec.z);
    // Doing this instead of the commented-out line above is a workaround
    // for the Mesa driver bug described here:
    // https://gitlab.freedesktop.org/mesa/mesa/-/issues/10482
    oe_terrain_vertexMarker = int(gl_MultiTexCoord0.z);
    // Color of the underlying map geometry (untextured)
    vp_Color = oe_terrain_color;
    // initialize:
    oe_rex_morphFactor = 0.0;
    // tile key
    oe_tile_key = oe_tile_key_u;
    
    // Default tessellation level (where applicable)
    oe_terrain_tessLevel = vec4(1);
}
[break]
#pragma vp_name REX Engine - Init View Space
#pragma vp_function oe_rex_init_view, vertex_view, first
// outputs
out vec3 vp_Normal;
out vec3 oe_UpVectorView;
void oe_rex_init_view(inout vec4 vert_view)
{
    // "up" vector at this vertex in view space, which we will later
    // need in order to elevate the terrain. vp_Normal can change later
    // but UpVectorView will stay the same.
    oe_UpVectorView = vp_Normal;
}
)";

	ENGINE_ELEVATION = "RexEngine.elevation.glsl";
    _sources[ENGINE_ELEVATION] = 
R"(#pragma vp_name REX Engine - Elevation
#pragma vp_function oe_rex_applyElevation, vertex_view, 0.1
#pragma import_defines(OE_TERRAIN_RENDER_ELEVATION)
// Vertex Markers:
#define VERTEX_VISIBLE  1
#define VERTEX_BOUNDARY 2
#define VERTEX_HAS_ELEVATION 4
#define VERTEX_SKIRT 8
#define VERTEX_CONSTRAINT 16
// stage
out vec4 oe_layer_tilec;
out vec3 oe_UpVectorView;
flat out int oe_terrain_vertexMarker;
uniform float oe_terrain_altitude;
// SDK functions:
float oe_terrain_getElevation();
void oe_rex_applyElevation(inout vec4 vertex)
{
#ifdef OE_TERRAIN_RENDER_ELEVATION
    bool elevate =
        ((oe_terrain_vertexMarker & VERTEX_VISIBLE) != 0) &&
        ((oe_terrain_vertexMarker & VERTEX_HAS_ELEVATION) == 0);
    float elev = elevate ? oe_terrain_getElevation() : 0.0;
    vertex.xyz += oe_UpVectorView * elev;
#endif
    vertex.xyz += oe_UpVectorView * oe_terrain_altitude;
}
)";

    ENGINE_MORPHING = "RexEngine.Morphing.glsl";
    _sources[ENGINE_MORPHING] = 
R"(#pragma vp_name       REX Engine - Morphing
#pragma vp_entryPoint oe_rex_morph
#pragma vp_location   vertex_model
#pragma vp_order      0.5
#pragma import_defines(OE_TERRAIN_MORPH_GEOMETRY)
#pragma import_defines(OE_TERRAIN_RENDER_ELEVATION)
#pragma import_defines(OE_IS_DEPTH_CAMERA)
#pragma import_defines(OE_TILE_SIZE)
out vec3 vp_Normal;
out vec4 oe_layer_tilec;
out float oe_rex_morphFactor;
flat out int oe_terrain_vertexMarker;
uniform vec2 oe_tile_morph;
uniform vec2 oe_tile_elevTexelCoeff;
#ifdef OE_IS_DEPTH_CAMERA
uniform mat4 oe_shadowToPrimaryMatrix;
#endif
// SDK functions:
float oe_terrain_getElevation(in vec2 uv);
// Vertex Markers:
#define VERTEX_VISIBLE  1
#define VERTEX_BOUNDARY 2
#define VERTEX_HAS_ELEVATION 4
#define VERTEX_SKIRT 8
#define VERTEX_CONSTRAINT 16
void moveToConstraint(in vec4 vertex, in vec4 layer_tilec, out vec4 newVertex, out vec4 new_layer_tilec)
{
    newVertex = vertex;
    new_layer_tilec = layer_tilec;
}
// Compute a morphing factor based on model-space inputs:
float oe_rex_ComputeMorphFactor(in vec4 position, in vec3 up)
{
    // Find the "would be" position of the vertex (the position the vertex would
    // assume with no morphing)
	vec4 wouldBePosition = position;
#ifdef OE_TERRAIN_RENDER_ELEVATION
        float elev = oe_terrain_getElevation( oe_layer_tilec.st );
		wouldBePosition.xyz += up*elev;
#endif
    vec4 wouldBePositionView = gl_ModelViewMatrix * wouldBePosition;
#ifdef OE_IS_DEPTH_CAMERA
    // For a depth camera, we have to compute the morphed position
    // from the perspective of the primary camera so they match up:
    wouldBePositionView = oe_shadowToPrimaryMatrix * wouldBePositionView;
#endif
    float fDistanceToEye = length(wouldBePositionView.xyz); // or just -z.
    float fMorphLerpK  = 1.0 - clamp( oe_tile_morph[0] - fDistanceToEye * oe_tile_morph[1], 0.0, 1.0 );
    return fMorphLerpK;
}
void oe_rex_morph(inout vec4 vertexModel)
{
    // compute the morphing factor to send down the pipe.
    // we need this even if vertex-morphing is off since we use it for
    // other things (like image blending)
    if ((oe_terrain_vertexMarker & VERTEX_CONSTRAINT) == 0)
    {
        oe_rex_morphFactor = oe_rex_ComputeMorphFactor(vertexModel, vp_Normal);
#ifdef OE_TERRAIN_MORPH_GEOMETRY
        vec4 neighborVertexModel = vec4(gl_MultiTexCoord1.xyz, 1.0);
        vec3 neighborNormal = gl_MultiTexCoord2.xyz;
        const float halfSize = (0.5*OE_TILE_SIZE)-0.5;
        const float twoOverHalfSize = 2.0/(OE_TILE_SIZE-1.0);
        // Either 0 if point should not be morphed (in (x, y)), or the
        // delta to the neighbor point.
        vec2 fractionalPart = fract(oe_layer_tilec.st * halfSize) * twoOverHalfSize;
        vec4 neighbor_tilec = oe_layer_tilec;
        neighbor_tilec.st = clamp(oe_layer_tilec.st - fractionalPart, 0.0, 1.0);
        // morph the vertex:
        vertexModel.xyz = mix(vertexModel.xyz, neighborVertexModel.xyz, oe_rex_morphFactor);
        // morph the normal:
        vp_Normal = normalize(mix(vp_Normal, neighborNormal, oe_rex_morphFactor));
        oe_layer_tilec.st = mix(oe_layer_tilec.st, neighbor_tilec.st, oe_rex_morphFactor);
#endif
    }
    else
    {
        oe_rex_morphFactor = 0.0;
    }
}
)";

    ENGINE_IMAGELAYER = "RexEngine.ImageLayer.glsl";
    _sources[ENGINE_IMAGELAYER] = 
R"(#pragma vp_name       REX Engine - ImageLayer/VS
#pragma vp_entryPoint oe_rex_imageLayer_VS
#pragma vp_location   vertex_view
#pragma vp_order      0.4
// Stage globals
vec4 oe_layer_tilec;
vec2 oe_layer_texc;
vec2 oe_layer_texcParent;
uniform mat4 oe_layer_texMatrix;
uniform mat4 oe_layer_texParentMatrix;
void oe_rex_imageLayer_VS(inout vec4 vertexView)
{
    // calculate the texture coordinates:
    oe_layer_texc = (oe_layer_texMatrix * oe_layer_tilec).st;
    oe_layer_texcParent = (oe_layer_texParentMatrix * oe_layer_tilec).st;
}
[break]
#pragma vp_name       REX Engine - Fragment
#pragma vp_entryPoint oe_rex_imageLayer_FS
#pragma vp_location   fragment_coloring
#pragma vp_order      0.5
#pragma import_defines(OE_TERRAIN_RENDER_IMAGERY)
#pragma import_defines(OE_TERRAIN_MORPH_IMAGERY)
#pragma import_defines(OE_TERRAIN_BLEND_IMAGERY)
#pragma import_defines(OE_TERRAIN_CAST_SHADOWS)
#pragma import_defines(OE_IS_PICK_CAMERA)
#pragma import_defines(OE_IS_SHADOW_CAMERA)
#pragma import_defines(OE_IS_DEPTH_CAMERA)
uniform sampler2D oe_layer_tex;
uniform int       oe_layer_uid;
uniform int       oe_layer_order;
#ifdef OE_TERRAIN_MORPH_IMAGERY
uniform sampler2D oe_layer_texParent;
uniform float oe_layer_texParentExists;
in vec2 oe_layer_texcParent;
in float oe_rex_morphFactor;
#endif
in vec2 oe_layer_texc;
in vec4 oe_layer_tilec;
in float oe_layer_opacity;
// Vertex Markers:
#define VERTEX_VISIBLE  1
#define VERTEX_BOUNDARY 2
#define VERTEX_HAS_ELEVATION 4
#define VERTEX_SKIRT 8
flat in int oe_terrain_vertexMarker;
void oe_rex_imageLayer_FS(inout vec4 color)
{
    // if the provoking vertex is marked for discard, skip it:
    if ((oe_terrain_vertexMarker & VERTEX_VISIBLE) == 0)
    {
        discard;
        return;
    }
    // If this is a shadow camera and the terrain doesn't cast shadows, no render:
#if defined(OE_IS_SHADOW_CAMERA) && !defined(OE_TERRAIN_CAST_SHADOWS)
    discard;
    return;
#endif
    // If this is a depth-only camera, skip terrain skirt geometry:
#if defined(OE_IS_DEPTH_CAMERA)
    if ((oe_terrain_vertexMarker & VERTEX_SKIRT) != 0)
    {
        discard;
        return;
    }
#endif // OE_IS_DEPTH_CAMERA
    // if this is a picking camera, reset the color to all zeros:
#ifdef OE_IS_PICK_CAMERA
    color = vec4(0);
#else
    // If imagery rendering is disabled, we're done:
#ifndef OE_TERRAIN_RENDER_IMAGERY
    return;
#endif
    // whether this layer contains texel color (UID<0 means no texture)
    bool isTexelLayer = oe_layer_uid >= 0;
    // whether this is the first layer to render:
    bool isFirstLayer = oe_layer_order == 0;
    vec4 texel = color;
    if (isTexelLayer)
    {
        texel = texture(oe_layer_tex, oe_layer_texc);
#ifdef OE_TERRAIN_MORPH_IMAGERY
        // sample the main texture:
        // sample the parent texture:
        vec4 texelParent = texture(oe_layer_texParent, oe_layer_texcParent);
        // if the parent texture does not exist, use the current texture with alpha=0 as the parent
        // so we can "fade in" an image layer that starts at LOD > 0:
        texelParent = mix(vec4(texel.rgb, 0.0), texelParent, oe_layer_texParentExists);
        // Resolve the final texel color.
        // We have to clamp oe_rex_morphFactor here even though it's clamped in the 
        // vertex shader. Reason unknown.
        texel = mix(texel, texelParent, clamp(oe_rex_morphFactor, 0.0, 1.0));
#endif
        // intergrate thelayer opacity:
        texel.a = texel.a * oe_layer_opacity;
        color.a = 1.0;
    }
#ifdef OE_TERRAIN_BLEND_IMAGERY
    // If this is a first image layer, blend with the incoming terrain color.
    // Otherwise, apply directly and let GL blending do the rest.
    if (isTexelLayer && isFirstLayer)
    {
        color.rgb = texel.rgb*texel.a + color.rgb*(1.0 - texel.a);
    }
    else
    {
        color = texel;
    }
#else
    // No blending? The output is just the texel value.
    color = texel;
#endif // OE_TERRAIN_BLEND_IMAGERY
#endif // OE_IS_PICK_CAMERA
})";

    ENGINE_NORMAL_MAP = "RexEngine.NormalMap.glsl";
    _sources[ENGINE_NORMAL_MAP] = 
R"(#pragma vp_entryPoint oe_rex_normalMapVS
#pragma vp_location   vertex_view
#pragma vp_order      0.5
#pragma import_defines(OE_TERRAIN_RENDER_NORMAL_MAP)
uniform mat4 oe_tile_normalTexMatrix;
uniform vec2 oe_tile_elevTexelCoeff;
uniform mat4 oe_tile_elevationTexMatrix;
// stage globals
out vec4 oe_layer_tilec;
out vec2 oe_normal_uv;
out vec3 oe_normal_binormal;
void oe_rex_normalMapVS(inout vec4 unused)
{
#ifndef OE_TERRAIN_RENDER_NORMAL_MAP
    return;
#endif
    // calculate the sampling coordinates for the normal texture
    //oe_normalMapCoords = (oe_tile_normalTexMatrix * oe_layer_tilec).st;
    //oe_normalMapCoords = oe_layer_tilec.st
    //    * oe_tile_elevTexelCoeff.x * oe_tile_normalTexMatrix[0][0]
    //    + oe_tile_elevTexelCoeff.x * oe_tile_normalTexMatrix[3].st
    //    + oe_tile_elevTexelCoeff.y;
    oe_normal_uv = oe_layer_tilec.st
        * oe_tile_elevTexelCoeff.x * oe_tile_elevationTexMatrix[0][0]
        + oe_tile_elevTexelCoeff.x * oe_tile_elevationTexMatrix[3].st
        + oe_tile_elevTexelCoeff.y;
    // send the bi-normal to the fragment shader
    oe_normal_binormal = normalize(gl_NormalMatrix * vec3(0, 1, 0));
}
[break]
#pragma vp_entryPoint oe_rex_normalMapFS
#pragma vp_location   fragment_coloring
#pragma vp_order      0.1
#pragma import_defines(OE_TERRAIN_RENDER_NORMAL_MAP)
// import terrain SDK
vec4 oe_terrain_getNormalAndCurvature(in vec2);
uniform sampler2D oe_tile_normalTex;
in vec3 vp_Normal;
in vec3 oe_UpVectorView;
in vec2 oe_normal_uv;
in vec3 oe_normal_binormal;
// global
mat3 oe_normalMapTBN;
void oe_rex_normalMapFS(inout vec4 color)
{
#ifndef OE_TERRAIN_RENDER_NORMAL_MAP
    return;
#endif
    vec4 N = oe_terrain_getNormalAndCurvature(oe_normal_uv);
    vec3 tangent = normalize(cross(oe_normal_binormal, oe_UpVectorView));
    oe_normalMapTBN = mat3(tangent, oe_normal_binormal, oe_UpVectorView);
    vp_Normal = normalize(oe_normalMapTBN*N.xyz);
})";

    ENGINE_GEOM = "RexEngine.gs.glsl";
    _sources[ENGINE_GEOM] = 
R"(#pragma vp_name       REX Engine - GS
#pragma vp_entryPoint oe_rex_GS
#pragma vp_location   geometry
layout(triangles)in;
layout(triangle_strip, max_vertices=3) out;
void VP_LoadVertex(in int);
void VP_EmitModelVertex();
void oe_rex_GS(void)
{
    for(int i=0; i < 3; ++i )
    {
        VP_LoadVertex(i);
        gl_Position = gl_in[i].gl_Position;
        VP_EmitModelVertex();
    }
    EndPrimitive();
}
)";

    ENGINE_TESSELLATION = "RexEngine.Tessellation.glsl";
    _sources[ENGINE_TESSELLATION] = 
R"(#pragma vp_name REX Engine TCS
#pragma vp_function oe_rex_TCS, tess_control, last
layout(vertices=3) out;
uniform float oe_terrain_tess;
uniform float oe_terrain_tess_range;
#pragma oe_use_shared_layer(LIFEMAP_TEX, LIFEMAP_MAT)
varying vec4 oe_layer_tilec;
varying vec4 vp_Vertex;
varying vec3 vp_Normal;
void VP_LoadVertex(in int);
float oe_terrain_getElevation();
float remap_unit(in float value, in float lo, in float hi)
{
    return clamp((value - lo) / (hi - lo), 0.0, 1.0);
}
void oe_rex_TCS()
{
    if (gl_InvocationID == 0)
    {
        // iterator backward so we end up loading vertex 0
        float d[3];
        vec3 v[3];
        for (int i = 2; i >= 0; --i)
        {
            VP_LoadVertex(i);
            v[i] = (gl_ModelViewMatrix * (vp_Vertex + vec4(vp_Normal * oe_terrain_getElevation(), 0.0))).xyz;
            d[i] = 1.0;
#ifdef LIFEMAP_TEX
            d[i] = texture(LIFEMAP_TEX, (LIFEMAP_MAT * oe_layer_tilec).st).r; // more rugged = more tessellated
#endif
            d[i] = oe_terrain_tess * d[i];
        }
        float max_dist = oe_terrain_tess_range;
        float min_dist = oe_terrain_tess_range / 6.0;
        vec3 m12 = 0.5*(v[1] + v[2]);
        vec3 m20 = 0.5*(v[2] + v[0]);
        vec3 m01 = 0.5*(v[0] + v[1]);
        float f12 = remap_unit(-m12.z, max_dist, min_dist);
        float f20 = remap_unit(-m20.z, max_dist, min_dist);
        float f01 = remap_unit(-m01.z, max_dist, min_dist);
        float e0 = max(1.0, max(d[1], d[2]) * f12);
        float e1 = max(1.0, max(d[2], d[0]) * f20);
        float e2 = max(1.0, max(d[0], d[1]) * f01);
        float e3 = max(e0, max(e1, e2));
        gl_TessLevelOuter[0] = e0;
        gl_TessLevelOuter[1] = e1;
        gl_TessLevelOuter[2] = e2;
        gl_TessLevelInner[0] = e3;
    }
}
[break]
#pragma vp_name REX Engine TES
#pragma vp_function oe_rex_TES, tess_eval
// osgEarth terrain is always CCW winding
layout(triangles, equal_spacing, ccw) in;
// Internal helpers:
void VP_Interpolate3();
void VP_EmitVertex();
float VP_Interpolate3(float a, float b, float c) 
{
    return dot(gl_TessCoord.xyz, vec3(a,b,c));
}
vec2 VP_Interpolate3(vec2 a, vec2 b, vec2 c) 
{
    return vec2(
        dot(gl_TessCoord.xyz, vec3(a.x,b.x,c.x)),
        dot(gl_TessCoord.xyz, vec3(a.y,b.y,c.y)));
}
vec3 VP_Interpolate3(vec3 a, vec3 b, vec3 c) 
{
    return vec3(
        dot(gl_TessCoord.xyz, vec3(a.x,b.x,c.x)),
        dot(gl_TessCoord.xyz, vec3(a.y,b.y,c.y)),
        dot(gl_TessCoord.xyz, vec3(a.z,b.z,c.z)));
}
vec4 VP_Interpolate3(vec4 a, vec4 b, vec4 c) 
{
    return vec4(
        dot(gl_TessCoord.xyz, vec3(a.x,b.x,c.x)),
        dot(gl_TessCoord.xyz, vec3(a.y,b.y,c.y)),
        dot(gl_TessCoord.xyz, vec3(a.z,b.z,c.z)),
        dot(gl_TessCoord.xyz, vec3(a.w,b.w,c.w)));
}
void oe_rex_TES()
{
    VP_Interpolate3();
    VP_EmitVertex();
}
)";

    ENGINE_SDK = "RexEngine.SDK.glsl";
    _sources[ENGINE_SDK] = 
R"(#pragma vp_name Rex Terrain SDK
#pragma import_defines(OE_MESA_23_WORKAROUND)
#ifdef OE_MESA_23_WORKAROUND
#define TILE_COORDS gl_MultiTexCoord0
#else
#define TILE_COORDS oe_layer_tilec
#endif
uniform sampler2D oe_tile_elevationTex;
uniform mat4 oe_tile_elevationTexMatrix;
uniform sampler2D oe_tile_normalTex;
uniform mat4 oe_tile_normalTexMatrix;
// SDK functions for the Rex engine.
// Declare and call these from any shader that runs on the terrain.
// uniforms from terrain engine
uniform vec2 oe_tile_elevTexelCoeff;
// Stage global
vec4 oe_layer_tilec;
vec4 oe_tile_key;
// Sample the elevation data at a UV tile coordinate.
float oe_terrain_getElevation(in vec2 uv)
{
    // Texel-level scale and bias allow us to sample the elevation texture
    // on texel center instead of edge.
    vec2 uv_scaledBiased = uv
        * oe_tile_elevTexelCoeff.x * oe_tile_elevationTexMatrix[0][0]     // scale
        + oe_tile_elevTexelCoeff.x * oe_tile_elevationTexMatrix[3].st     // bias
        + oe_tile_elevTexelCoeff.y;
    return texture(oe_tile_elevationTex, uv_scaledBiased).r;
}
// Read the elevation at the build-in tile coordinates (convenience)
float oe_terrain_getElevation()
{
    return oe_terrain_getElevation(TILE_COORDS.st);
}
// Read the normal vector and curvature at resolved UV tile coordinates.
vec4 oe_terrain_getNormalAndCurvature(in vec2 uv_scaledBiased)
{
    vec4 n = texture(oe_tile_normalTex, uv_scaledBiased);
    n.xyz = n.xyz*2.0-1.0;
    float curv = n.z;
    n.z = 1.0 - abs(n.x) - abs(n.y);
    // unnecessary since Z is never < 0:
    //float t = clamp(-n.z, 0, 1);
    //n.x += (n.x > 0)? -t : t;
    //n.y += (n.y > 0)? -t : t;
    return vec4(normalize(n.xyz), curv);
}
vec4 oe_terrain_getNormalAndCurvature()
{
    vec2 uv_scaledBiased = TILE_COORDS.st
        * oe_tile_elevTexelCoeff.x * oe_tile_normalTexMatrix[0][0]
        + oe_tile_elevTexelCoeff.x * oe_tile_normalTexMatrix[3].st
        + oe_tile_elevTexelCoeff.y;
    return oe_terrain_getNormalAndCurvature(uv_scaledBiased);
}
/**
 * Scales repeating texture coordinate such that they are [0..1]
 * at a specific reference tile LOD. 
 */
vec2 oe_terrain_scaleCoordsToRefLOD(in vec2 tc, in float refLOD)
{
    float dL = oe_tile_key.z - refLOD;
    float factor = exp2(dL);
    float invFactor = 1.0/factor;
    vec2 result = tc * vec2(invFactor);
    vec2 a = floor(oe_tile_key.xy * invFactor);
    vec2 b = a * factor;
    vec2 c = b + factor;
    float m = floor(clamp(factor,0.0,1.0)); // if factor>=1.0
    result += m*(oe_tile_key.xy-b)/(c-b);
    return result;
}
/**
 * Scales repeating texture coordinate such that they are [0..1]
 * at a specific reference tile LOD.
 */
vec4 oe_terrain_scaleCoordsAndTileKeyToRefLOD(in vec2 tc, in float refLOD)
{
    float dL = oe_tile_key.z - refLOD;
    float factor = exp2(dL);
    float invFactor = 1.0 / factor;
    vec2 result = tc * vec2(invFactor);
    vec2 a = floor(oe_tile_key.xy * invFactor);
    vec2 b = a * factor;
    vec2 c = b + factor;
    float m = floor(clamp(factor, 0.0, 1.0)); // if factor>=1.0
    result += m * (oe_tile_key.xy - b) / (c - b);
    return vec4(result, a);
}
)";
}

ShadersGL4::ShadersGL4()
{
    ENGINE_VERT = "RexEngine.vert.GL4.glsl";
    _sources[ENGINE_VERT] = 
R"(#pragma include RexEngine.GL4.glsl
#pragma vp_name REX Custom M2V Transform
#pragma vp_function oe_XformModelToView, vertex_transform_model_to_view
out vec4 vp_Vertex;
out vec3 vp_Normal;
void oe_XformModelToView()
{
    mat4 mvm = oe_tile[oe_tileID].modelViewMatrix;
    vp_Vertex = mvm * vp_Vertex;
    vp_Normal = normalize(mat3(mvm) * vp_Normal);
}
[break]
#pragma include RexEngine.GL4.glsl
#pragma vp_name REX Engine - Init Model Space
#pragma vp_function oe_rex_init_model, vertex_model, first
#pragma import_defines(OE_TERRAIN_RENDER_ELEVATION)
#pragma import_defines(OE_TERRAIN_MORPH_GEOMETRY)
#pragma import_defines(OE_TERRAIN_MORPH_IMAGERY)
#pragma import_defines(OE_IS_SHADOW_CAMERA)
#pragma import_defines(OE_TILE_SIZE)
// SDK functions:
float oe_terrain_getElevation(in vec2 uv);
// attributes
layout(location = 0) in vec3 a_position;
layout(location = 1) in vec3 a_normal;
layout(location = 2) in vec3 a_uv;
layout(location = 3) in vec3 a_neighbor;
layout(location = 4) in vec3 a_neighborNormal;
#define VERTEX_CONSTRAINT 16
// uniforms
uniform vec4 oe_terrain_color;
#ifdef OE_IS_SHADOW_CAMERA
uniform mat4 oe_shadowToPrimaryMatrix;
#endif
// model stage only
flat out vec4 oe_tile_key;
out vec3 vp_Normal;
out vec4 vp_Vertex;
out vec3 vp_VertexView;
out vec4 vp_Color;
//flat out mat4 oe_tile_mvm;
out vec4 oe_layer_tilec;
out vec3 oe_UpVectorView;
out float oe_rex_morphFactor;
out vec4 oe_terrain_tessLevel;
flat out int oe_terrain_vertexMarker;
void oe_rex_morph_model();
void oe_rex_init_model(inout vec4 out_model_vertex)
{
    vp_Vertex = vec4(a_position, 1);
    vp_Normal = a_normal;
    // instance ID from the DrawElementsIndirect cmd
    oe_tileID = gl_DrawID;
    // Color of the underlying map geometry (untextured)
    vp_Color = oe_terrain_color;
    // initialize:
    oe_rex_morphFactor = 0.0;
    // Default tessellation level (where applicable)
    oe_terrain_tessLevel = vec4(1);
    // assign vertex marker flags
    oe_terrain_vertexMarker = int(a_uv.z);
    // extract tile UV (global data)
    oe_layer_tilec = vec4(a_uv.xy, 0, 1);
    // the tile key
    oe_tile_key = oe_tile[oe_tileID].tileKey;
#if defined(OE_TERRAIN_MORPH_GEOMETRY) || defined(OE_TERRAIN_MORPH_IMAGERY)
    if ((oe_terrain_vertexMarker & VERTEX_CONSTRAINT) == 0)
        oe_rex_morph_model();
#endif
    // assign to output.
    out_model_vertex = vp_Vertex;
}
// Uses neighbor data to morph across LODs
void oe_rex_morph_model()
{
    mat4 mvm = oe_tile[oe_tileID].modelViewMatrix;
    // Compute the morphing factor. We need the distance to
    // the "final" vertex (elevation applied) to compute it
#ifdef OE_TERRAIN_RENDER_ELEVATION
    float elev = oe_terrain_getElevation(oe_layer_tilec.st);
    vec4 vertex_view = mvm * vec4(a_position + vp_Normal * elev, 1);
#else
    vec4 vertex_view = mvm * vec4(a_position, 1);
#endif
#ifdef OE_IS_SHADOW_CAMERA
    // For a depth camera, we have to compute the morphed position
    // from the perspective of the primary camera so they match up:
    vertex_view = oe_shadowToPrimaryMatrix * vertex_view;
#endif
    int lod = int(oe_tile_key.z);
    float dist_to_eye = length(vertex_view.xyz);
    vec2 mc = oe_shared.morphConstants[lod];
    oe_rex_morphFactor = 1.0 - clamp(mc[0] - dist_to_eye * mc[1], 0.0, 1.0);
    // if just morphing imagery, we're done - otherwise we also need
    // to morph the vertex/normal/uv:
#ifdef OE_TERRAIN_MORPH_GEOMETRY
    // Compute the delta to the neighbor point.
    const float halfSize = (0.5*OE_TILE_SIZE) - 0.5;
    const float twoOverHalfSize = 2.0 / (OE_TILE_SIZE - 1.0);
    vec2 fractionalPart = fract(oe_layer_tilec.st * halfSize) * twoOverHalfSize;
    vec2 neighbor_tilec = clamp(oe_layer_tilec.st - fractionalPart, 0.0, 1.0);
    // morph the vertex, normal, and uvs.
    vp_Vertex.xyz = mix(a_position, a_neighbor, oe_rex_morphFactor);
    vp_Normal = normalize(mix(vp_Normal, a_neighborNormal, oe_rex_morphFactor));
    oe_layer_tilec.st = mix(oe_layer_tilec.st, neighbor_tilec, oe_rex_morphFactor);
#endif
}
[break]
#pragma include RexEngine.GL4.glsl
#pragma vp_name REX Engine - Init View Space
#pragma vp_function oe_rex_init_view, vertex_view, first
// outputs
out vec3 vp_Normal;
out vec3 oe_UpVectorView;
void oe_rex_init_view(inout vec4 ignore)
{
    oe_UpVectorView = vp_Normal;
}
)";

	ENGINE_ELEVATION = "RexEngine.elevation.glsl";
    _sources[ENGINE_ELEVATION] = 
R"(#pragma vp_name REX Engine - Elevation
#pragma vp_function oe_rex_applyElevation, vertex_view, 0.1
#pragma import_defines(OE_TERRAIN_RENDER_ELEVATION)
// Vertex Markers:
#define VERTEX_VISIBLE  1
#define VERTEX_BOUNDARY 2
#define VERTEX_HAS_ELEVATION 4
#define VERTEX_SKIRT 8
#define VERTEX_CONSTRAINT 16
// stage
out vec4 oe_layer_tilec;
out vec3 oe_UpVectorView;
flat out int oe_terrain_vertexMarker;
uniform float oe_terrain_altitude;
// SDK functions:
float oe_terrain_getElevation();
void oe_rex_applyElevation(inout vec4 vertex)
{
#ifdef OE_TERRAIN_RENDER_ELEVATION
    bool elevate =
        ((oe_terrain_vertexMarker & VERTEX_VISIBLE) != 0) &&
        ((oe_terrain_vertexMarker & VERTEX_HAS_ELEVATION) == 0);
    float elev = elevate ? oe_terrain_getElevation() : 0.0;
    vertex.xyz += oe_UpVectorView * elev;
#endif
    vertex.xyz += oe_UpVectorView * oe_terrain_altitude;
}
)";

    ENGINE_IMAGELAYER = "RexEngine.ImageLayer.GL4.glsl";
    _sources[ENGINE_IMAGELAYER] = 
R"(#pragma include RexEngine.GL4.glsl
#pragma vp_name REX Engine - ImageLayer/VS
#pragma vp_function oe_rex_imageLayer_VS, vertex_view, 0.4
// Stage globals
vec4 oe_layer_tilec;
// outputs
out vec2 oe_color_uv;
out vec2 oe_parent_uv;
flat out uint64_t oe_color_handle;
flat out uint64_t oe_parent_handle;
flat out int oe_draw_order;
void oe_rex_imageLayer_VS(inout vec4 vertexView)
{
    oe_color_uv = (oe_tile[oe_tileID].colorMat * oe_layer_tilec).st;
    int colorIndex = oe_tile[oe_tileID].colorIndex;
    oe_color_handle = (colorIndex >= 0) ? oe_terrain_tex[colorIndex] : 0;
    oe_parent_uv = (oe_tile[oe_tileID].parentMat * oe_layer_tilec).st;
    int parentIndex = oe_tile[oe_tileID].parentIndex;
    oe_parent_handle = (parentIndex >= 0) ? oe_terrain_tex[parentIndex] : 0;
    oe_draw_order = oe_tile[oe_tileID].drawOrder;
}
[break]
#pragma include RexEngine.GL4.glsl
#pragma vp_name REX Engine - Fragment
#pragma vp_function oe_rex_imageLayer_FS, fragment_coloring, 0.5
#pragma import_defines(OE_TERRAIN_RENDER_IMAGERY)
#pragma import_defines(OE_TERRAIN_MORPH_IMAGERY)
#pragma import_defines(OE_TERRAIN_BLEND_IMAGERY)
#pragma import_defines(OE_TERRAIN_CAST_SHADOWS)
#pragma import_defines(OE_IS_PICK_CAMERA)
#pragma import_defines(OE_IS_SHADOW_CAMERA)
#pragma import_defines(OE_IS_DEPTH_CAMERA)
//uniform sampler2D oe_layer_tex;
uniform int oe_layer_uid;
uniform int oe_layer_order;
#ifdef OE_TERRAIN_MORPH_IMAGERY
in vec2 oe_parent_uv;
flat in uint64_t oe_parent_handle;
in float oe_rex_morphFactor;
#endif
// inputs
in vec2 oe_color_uv;
flat in uint64_t oe_color_handle;
in vec4 oe_layer_tilec;
in float oe_layer_opacity;
flat in int oe_terrain_vertexMarker;
flat in int oe_draw_order;
#define VERTEX_VISIBLE  1
#define VERTEX_BOUNDARY 2
#define VERTEX_HAS_ELEVATION 4
#define VERTEX_SKIRT 8
void oe_rex_imageLayer_FS(inout vec4 color)
{
    // if the provoking vertex is marked for discard, skip it:
    if ((oe_terrain_vertexMarker & VERTEX_VISIBLE) == 0)
    {
        discard;
        return;
    }
    // If this is a shadow camera and the terrain doesn't cast shadows, no render:
#if defined(OE_IS_SHADOW_CAMERA) && !defined(OE_TERRAIN_CAST_SHADOWS)
    discard;
    return;
#endif
    // If this is a depth-only camera, skip terrain skirt geometry:
#if defined(OE_IS_DEPTH_CAMERA)
    if ((oe_terrain_vertexMarker & VERTEX_SKIRT) != 0)
    {
        discard;
        return;
    }
#endif // OE_IS_DEPTH_CAMERA
    // if this is a picking camera, reset the color to all zeros:
#ifdef OE_IS_PICK_CAMERA
    color = vec4(0);
    return;
#endif
    // If imagery rendering is disabled, we're done:
#ifndef OE_TERRAIN_RENDER_IMAGERY
    return;
#endif
    // whether this layer contains texel color (UID<0 means no texture)
    bool isTexelLayer = oe_color_handle > 0UL;
    vec4 texel = color;
    if (isTexelLayer)
    {
        texel = texture(sampler2D(oe_color_handle), oe_color_uv);
#ifdef OE_TERRAIN_MORPH_IMAGERY
        if (oe_parent_handle != 0UL)
        {
            // sample the parent texture and blend for the morphing.
            // We have to clamp oe_rex_morphFactor here even though it's clamped in the 
            // vertex shader. Reason unknown.
            vec4 texelParent = texture(sampler2D(oe_parent_handle), oe_parent_uv);
            texel = mix(texel, texelParent, clamp(oe_rex_morphFactor, 0.0, 1.0));
        }
#endif
        // intergrate the layer opacity:
        texel.a = texel.a * oe_layer_opacity;
        color.a = 1.0;
    }
#ifdef OE_TERRAIN_BLEND_IMAGERY
    // If this is a first image layer, blend with the incoming terrain color.
    // Otherwise, apply directly and let GL blending do the rest.
    if (isTexelLayer && (oe_draw_order == 0))
    {
        color.rgb = texel.rgb*texel.a + color.rgb*(1.0 - texel.a);
    }
    else
    {
        color = texel;
    }
#else
    // No blending? The output is just the texel value.
    color = texel;
#endif // OE_TERRAIN_BLEND_IMAGERY
}
)";

    ENGINE_NORMAL_MAP = "RexEngine.NormalMap.GL4.glsl";
    _sources[ENGINE_NORMAL_MAP] = 
R"(#pragma include RexEngine.GL4.glsl
#pragma vp_function oe_rex_normalMapVS, vertex_view, 0.5
#pragma import_defines(OE_TERRAIN_RENDER_NORMAL_MAP)
//out vec3 oe_normal_binormal;
#ifdef OE_TERRAIN_RENDER_NORMAL_MAP
vec2 oe_terrain_getNormalCoords();
flat out uint64_t oe_normal_handle;
out vec2 oe_normal_uv;
#endif
void oe_rex_normalMapVS(inout vec4 unused)
{
#ifdef OE_TERRAIN_RENDER_NORMAL_MAP
    oe_normal_handle = 0;
    int index = oe_tile[oe_tileID].normalIndex;
    if (index >= 0)
    {
        oe_normal_uv = oe_terrain_getNormalCoords();
        oe_normal_handle = oe_terrain_tex[index];
    }
#endif
}
[break]
#pragma include RexEngine.GL4.glsl
#pragma vp_function oe_rex_normalMapFS, fragment_coloring, 0.1
#pragma import_defines(OE_TERRAIN_RENDER_NORMAL_MAP)
in vec3 vp_Normal;
in vec3 oe_UpVectorView;
vec4 oe_terrain_getNormalAndCurvature(in uint64_t, in vec2); // SDK
#ifdef OE_TERRAIN_RENDER_NORMAL_MAP
flat in uint64_t oe_normal_handle;
in vec2 oe_normal_uv;
#endif
// stage global
mat3 oe_normalMapTBN;
void oe_rex_normalMapFS(inout vec4 color)
{
    vp_Normal = oe_UpVectorView;
    vec3 binormal = normalize(gl_NormalMatrix * vec3(0, 1, 0));
    vec3 tangent = normalize(cross(binormal, oe_UpVectorView));
    oe_normalMapTBN = mat3(tangent, binormal, oe_UpVectorView);
#ifdef OE_TERRAIN_RENDER_NORMAL_MAP
    if (oe_normal_handle > 0)
    {
        vec4 N = oe_terrain_getNormalAndCurvature(oe_normal_handle, oe_normal_uv);
        vp_Normal = normalize( oe_normalMapTBN*N.xyz );
    }
#endif
}
)";

    ENGINE_TESSELLATION = "RexEngine.Tessellation.GL4.glsl";
    _sources[ENGINE_TESSELLATION] = 
R"(#pragma vp_name       REX Engine TCS
#pragma vp_entryPoint oe_rex_TCS
#pragma vp_location   tess_control
#pragma vp_order      last
#pragma include RexEngine.GL4.glsl
layout(vertices=3) out;
uniform float oe_terrain_tess;
uniform float oe_terrain_tess_range;
#pragma oe_use_shared_layer(LIFEMAP_TEX, LIFEMAP_MAT)
varying vec4 oe_layer_tilec;
varying vec4 vp_Vertex;
varying vec3 vp_Normal;
void VP_LoadVertex(in int);
float oe_terrain_getElevation();
float remap_unit(in float value, in float lo, in float hi)
{
    return clamp((value - lo) / (hi - lo), 0.0, 1.0);
}
// note: we are in MODEL space
void oe_rex_TCS()
{
    if (gl_InvocationID == 0)
    {
        // iterator backward so we end up loading vertex 0
        float d[3];
        vec3 v[3];
        mat4 mvm = oe_tile[oe_tileID].modelViewMatrix;
        for (int i = 2; i >= 0; --i)
        {
            VP_LoadVertex(i);
            v[i] = (mvm * (vp_Vertex + vec4(vp_Normal * oe_terrain_getElevation(), 0.0))).xyz;
            d[i] = 1.0;
#ifdef LIFEMAP_TEX
            d[i] = texture(LIFEMAP_TEX, (LIFEMAP_MAT *oe_layer_tilec).st).r; // more rugged = more tessellated
#endif
            d[i] = oe_terrain_tess * d[i];
        }
        float max_dist = oe_terrain_tess_range;
        float min_dist = oe_terrain_tess_range / 6.0;
        vec3 m12 = 0.5*(v[1] + v[2]);
        vec3 m20 = 0.5*(v[2] + v[0]);
        vec3 m01 = 0.5*(v[0] + v[1]);
        float f12 = remap_unit(-m12.z, max_dist, min_dist);
        float f20 = remap_unit(-m20.z, max_dist, min_dist);
        float f01 = remap_unit(-m01.z, max_dist, min_dist);
        float e0 = max(1.0, max(d[1], d[2]) * f12);
        float e1 = max(1.0, max(d[2], d[0]) * f20);
        float e2 = max(1.0, max(d[0], d[1]) * f01);
        float e3 = max(e0, max(e1, e2));
        gl_TessLevelOuter[0] = e0;
        gl_TessLevelOuter[1] = e1;
        gl_TessLevelOuter[2] = e2;
        gl_TessLevelInner[0] = e3;
    }
}
[break]
#pragma vp_name       REX Engine TES
#pragma vp_entryPoint oe_rex_TES
#pragma vp_location   tess_eval
// osgEarth terrain is always CCW winding
layout(triangles, equal_spacing, ccw) in;
// Internal helpers:
void VP_Interpolate3();
void VP_EmitVertex();
float VP_Interpolate3(float a, float b, float c) 
{
    return dot(gl_TessCoord.xyz, vec3(a,b,c));
}
vec2 VP_Interpolate3(vec2 a, vec2 b, vec2 c) 
{
    return vec2(
        dot(gl_TessCoord.xyz, vec3(a.x,b.x,c.x)),
        dot(gl_TessCoord.xyz, vec3(a.y,b.y,c.y)));
}
vec3 VP_Interpolate3(vec3 a, vec3 b, vec3 c) 
{
    return vec3(
        dot(gl_TessCoord.xyz, vec3(a.x,b.x,c.x)),
        dot(gl_TessCoord.xyz, vec3(a.y,b.y,c.y)),
        dot(gl_TessCoord.xyz, vec3(a.z,b.z,c.z)));
}
vec4 VP_Interpolate3(vec4 a, vec4 b, vec4 c) 
{
    return vec4(
        dot(gl_TessCoord.xyz, vec3(a.x,b.x,c.x)),
        dot(gl_TessCoord.xyz, vec3(a.y,b.y,c.y)),
        dot(gl_TessCoord.xyz, vec3(a.z,b.z,c.z)),
        dot(gl_TessCoord.xyz, vec3(a.w,b.w,c.w)));
}
varying vec3 oe_UpVectorView;
varying vec3 vp_Normal;
void oe_rex_TES()
{
    VP_Interpolate3();
    VP_EmitVertex();
}
)";

    ENGINE_SDK = "RexEngine.SDK.GL4.glsl";
    _sources[ENGINE_SDK] = 
R"(#pragma include RexEngine.GL4.glsl
#pragma vp_name Rex Terrain SDK
/**
 * SDK functions for the Rex engine.
 * Declare and call these from any shader that runs on the terrain.
 */
// Stage global
vec4 oe_layer_tilec;
vec4 oe_tile_key;
#if !defined(VP_STAGE_FRAGMENT)
uniform vec2 oe_tile_elevTexelCoeff;
// Gets the coordinate to use for elevation sampling.
vec2 oe_terrain_getElevationCoord(in vec2 uv)
{
    return uv
        * oe_tile_elevTexelCoeff.x * oe_tile[oe_tileID].elevMat[0][0]     // scale
        + oe_tile_elevTexelCoeff.x * oe_tile[oe_tileID].elevMat[3].st     // bias
        + oe_tile_elevTexelCoeff.y;
}
// Gets the handle to use for elevation sampling
uint64_t oe_terrain_getElevationHandle()
{
    int index = oe_tile[oe_tileID].elevIndex;
    return (index >= 0) ? oe_terrain_tex[index] : 0;
}
// Sample the elevation data at a UV tile coordinate.
float oe_terrain_getElevation(in vec2 uv)
{
    // Texel-level scale and bias allow us to sample the elevation texture
    // on texel center instead of edge.
    int index = oe_tile[oe_tileID].elevIndex;
    if (index >= 0)
    {
        vec2 uv_scaledBiased = oe_terrain_getElevationCoord(uv);
        return texture(sampler2D(oe_terrain_tex[index]), uv_scaledBiased).r;
    }
    return 0.0;
}
// Read the elevation at the build-in tile coordinates (convenience)
float oe_terrain_getElevation()
{
    return oe_terrain_getElevation(oe_layer_tilec.st);
}
// Read the normal vector and curvature at resolved UV tile coordinates.
vec4 oe_terrain_getNormalAndCurvature(in vec2 uv_scaledBiased)
{
    int index = oe_tile[oe_tileID].normalIndex;
    if (index >= 0)
    {
        vec4 n = texture(sampler2D(oe_terrain_tex[index]), uv_scaledBiased);
        n.xyz = n.xyz * 2.0 - 1.0;
        float curv = n.z;
        n.z = 1.0 - abs(n.x) - abs(n.y);
        // unnecessary since Z is never < 0:
        //float t = clamp(-n.z, 0, 1);
        //n.x += (n.x > 0)? -t : t;
        //n.y += (n.y > 0)? -t : t;
        return vec4(normalize(n.xyz), curv);
    }
    else return vec4(0.0, 0.0, 1.0, 0.0);
}
vec4 oe_terrain_getNormalAndCurvature()
{
    vec2 uv_scaledBiased = oe_layer_tilec.st
        * oe_tile_elevTexelCoeff.x * oe_tile[oe_tileID].normalMat[0][0]
        + oe_tile_elevTexelCoeff.x * oe_tile[oe_tileID].normalMat[3].st
        + oe_tile_elevTexelCoeff.y;
    return oe_terrain_getNormalAndCurvature(uv_scaledBiased);
}
uint64_t oe_terrain_getNormalHandle()
{
    int index = oe_tile[oe_tileID].normalIndex;
    return (index >= 0) ? oe_terrain_tex[index] : 0;
}
vec2 oe_terrain_getNormalCoords()
{
    return oe_layer_tilec.st
        * oe_tile_elevTexelCoeff.x * oe_tile[oe_tileID].normalMat[0][0]
        + oe_tile_elevTexelCoeff.x * oe_tile[oe_tileID].normalMat[3].st
        + oe_tile_elevTexelCoeff.y;
}
#endif // !VP_STAGE_FRAGMENT
vec4 oe_terrain_getNormalAndCurvature(in uint64_t handle, in vec2 uv)
{
    vec4 n = texture(sampler2D(handle), uv);
    n.xyz = n.xyz*2.0 - 1.0;
    float curv = n.z;
    n.z = 1.0 - abs(n.x) - abs(n.y);
    // unnecessary since Z is never < 0:
    //float t = clamp(-n.z, 0, 1);
    //n.x += (n.x > 0)? -t : t;
    //n.y += (n.y > 0)? -t : t;
    return vec4(normalize(n.xyz), curv);
}
/**
 * Scales repeating texture coordinate such that they are [0..1]
 * at a specific reference tile LOD. 
 */
vec2 oe_terrain_scaleCoordsToRefLOD(in vec2 tc, in float refLOD)
{
    float dL = oe_tile_key.z - refLOD;
    float factor = exp2(dL);
    float invFactor = 1.0/factor;
    vec2 result = tc * vec2(invFactor);
    vec2 a = floor(oe_tile_key.xy * invFactor);
    vec2 b = a * factor;
    vec2 c = b + factor;
    float m = floor(clamp(factor,0.0,1.0)); // if factor>=1.0
    result += m*(oe_tile_key.xy-b)/(c-b);
    return result;
}
/**
 * Scales repeating texture coordinate such that they are [0..1]
 * at a specific reference tile LOD.
 */
vec4 oe_terrain_scaleCoordsAndTileKeyToRefLOD(in vec2 tc, in float refLOD)
{
    float dL = oe_tile_key.z - refLOD;
    float factor = exp2(dL);
    float invFactor = 1.0 / factor;
    vec2 result = tc * vec2(invFactor);
    vec2 a = floor(oe_tile_key.xy * invFactor);
    vec2 b = a * factor;
    vec2 c = b + factor;
    float m = floor(clamp(factor, 0.0, 1.0)); // if factor>=1.0
    result += m * (oe_tile_key.xy - b) / (c - b);
    return vec4(result, a);
}
)";

    ENGINE_TYPES = "RexEngine.GL4.glsl";
    _sources[ENGINE_TYPES] = 
R"(#define MAX_NUM_SHARED_SAMPLERS 16
struct oe_rex_Shared {
    vec2 morphConstants[49];
    float padding[2];
};
struct oe_rex_Tile {
    vec4 tileKey;
    mat4 modelViewMatrix;
    mat4 colorMat;
    mat4 parentMat;
    mat4 elevMat;
    mat4 normalMat;
    mat4 landcoverMat;
    mat4 sharedMat[MAX_NUM_SHARED_SAMPLERS];
    int colorIndex;
    int parentIndex;
    int elevIndex;
    int normalIndex;
    int landcoverIndex;
    int sharedIndex[MAX_NUM_SHARED_SAMPLERS];
    int drawOrder;
    float padding[2];
};
layout(binding = 29, std430) readonly buffer RexTextureArena {
    uint64_t oe_terrain_tex[];
};
layout(binding = 30, std430) readonly buffer RexSharedDataBuffer {
    oe_rex_Shared oe_shared;
};
layout(binding = 31, std430) readonly buffer RexTileBuffer {
    oe_rex_Tile oe_tile[];
};
#if defined(VP_STAGE_VERTEX) || defined(VP_STAGE_TESSEVALUATION)
flat out int oe_tileID;
#elif defined(VP_STAGE_FRAGMENT)
flat in int oe_tileID;
#else
int oe_tileID;
#endif
)";
}

ShadersGL3 REXShadersFactory::s_gl3;
ShadersGL4 REXShadersFactory::s_gl4;
