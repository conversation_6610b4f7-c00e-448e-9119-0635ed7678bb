/*-------------------------------------------------------------------------
 *
 * pg_rewrite_d.h
 *    Macro definitions for pg_rewrite
 *
 * Portions Copyright (c) 1996-2023, PostgreSQL Global Development Group
 * Portions Copyright (c) 1994, Regents of the University of California
 *
 * NOTES
 *  ******************************
 *  *** DO NOT EDIT THIS FILE! ***
 *  ******************************
 *
 *  It has been GENERATED by src/backend/catalog/genbki.pl
 *
 *-------------------------------------------------------------------------
 */
#ifndef PG_REWRITE_D_H
#define PG_REWRITE_D_H

#define RewriteRelationId 2618
#define RewriteOidIndexId 2692
#define RewriteRelRulenameIndexId 2693

#define Anum_pg_rewrite_oid 1
#define Anum_pg_rewrite_rulename 2
#define Anum_pg_rewrite_ev_class 3
#define Anum_pg_rewrite_ev_type 4
#define Anum_pg_rewrite_ev_enabled 5
#define Anum_pg_rewrite_is_instead 6
#define Anum_pg_rewrite_ev_qual 7
#define Anum_pg_rewrite_ev_action 8

#define Natts_pg_rewrite 8


#endif							/* PG_REWRITE_D_H */
