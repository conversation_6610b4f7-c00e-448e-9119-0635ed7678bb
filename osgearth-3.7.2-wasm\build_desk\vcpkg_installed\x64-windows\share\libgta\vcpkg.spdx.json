{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/libgta-x64-windows-1.0.8#5-a18197e1-62ff-4013-8b83-5096b8157fd8", "name": "libgta:x64-windows@1.0.8#5 b98644cae0fe1a3a4f001eb8f2439f63df2773369974c687d2998082549634c2", "creationInfo": {"creators": ["Tool: vcpkg-2025-06-20-ef7c0d541124bbdd334a03467e7edb6c3364d199"], "created": "2025-07-03T02:34:56Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "libgta", "SPDXID": "SPDXRef-port", "versionInfo": "1.0.8#5", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/libgta", "homepage": "https://download.savannah.nongnu.org/releases/gta", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "Libgta is a portable library that implements the Generic Tagged Array (GTA) file format.", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "libgta:x64-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "b98644cae0fe1a3a4f001eb8f2439f63df2773369974c687d2998082549634c2", "downloadLocation": "NONE", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "libgta-${LIBGTA_VERSION}.tar.xz", "packageFileName": "libgta-${LIBGTA_VERSION}.tar.xz", "downloadLocation": "http://download.savannah.nongnu.org/releases/gta/libgta-1.0.8.tar.xz", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "${libgta_hash}"}]}], "files": [{"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "9cee793269e7e4cd620f985cc9d273b2b0666b8b25de4af61ceb38cbd55f779d"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "da22f82c8f12851a12046a859c173f31624e1414796dae0a540d1e3864d62edb"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}