/*
 * This file was generated to inject vcpkg feature selections into the installed copy of
 * sqlite so that consumers need not get the values from pkgconfig or CMake configs.
 *
 * No include guard: intentionally reuses the include guard from sqlite3.h.
 */

#ifndef SQLITE_API
#define SQLITE_API __declspec(dllimport)
#endif

#define SQLITE_ENABLE_UNLOCK_NOTIFY 1
/* #undef SQLITE_ENABLE_FTS3 */
/* #undef SQLITE_ENABLE_FTS4 */
/* #undef SQLITE_ENABLE_FTS5 */
/* #undef SQLITE_ENABLE_MEMSYS3 */
/* #undef SQLITE_ENABLE_MEMSYS5 */
/* #undef SQLITE_ENABLE_MATH_FUNCTIONS */
/* #undef SQLITE_ENABLE_UPDATE_DELETE_LIMIT */
/* #undef SQLITE_ENABLE_DBPAGE_VTAB */
#define SQLITE_ENABLE_RTREE
/* #undef SQLITE_ENABLE_SESSION */
/* #undef SQLITE_ENABLE_SNAPSHOT */
/* #undef SQLITE_ENABLE_PREUPDATE_HOOK */
/* #undef SQLITE_OMIT_LOAD_EXTENSION */
/* #undef SQLITE_ENABLE_GEOPOLY */
/* #undef SQLITE_OMIT_JSON */
#define SQLITE_OS_WIN 1
/* #undef SQLITE_OS_WINRT */
#define SQLITE_ENABLE_COLUMN_METADATA 1
/* #undef SQLITE_OS_UNIX */
/* #undef SQLITE_ENABLE_DBSTAT_VTAB */
/* #undef SQLITE_ENABLE_ICU */
