add-options-for-exes-docs-headers.patch 9ea177bfe907156718a9cb04a100bdfa1ab13117715d4e3931e864cfe2b485c3
cmake 3.30.1
features core
portfile.cmake 64d908b71049d7a80a701086c1752a21fa182d5f66591e23762f3bb798adf9c1
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
powershell 7.5.1
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-9322494aa025c04635f453cbc392b0ca170d297c
usage 431d1fd99d23de6d01df171baa9ab0edcccea16484d7c4ba0691d4ebc474de2d
vcpkg-cmake fa0a421976433b6b0635f7ccfd6e08f02a20aef877e749d6792bc1d9ae8233af
vcpkg-cmake-config 8d261494f5cab6cecd7851edc7eb14f27f3c82cd259e8a56095998d1199973a0
vcpkg-cmake-wrapper.cmake d257c0a541dd60c4b6eb6f46a5e9f128a64a6ec40f3b8b5bc8ab753e7a935c61
vcpkg.json 7788caf0f92dc67795c7fe786f266eb5076c52f069cd9f78a9579305421e24f6
vcpkg_check_features 943b217e0968d64cf2cb9c272608e6a0b497377e792034f819809a79e1502c2b
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_find_acquire_program 03722d714d388b19731f5c0be35996c25a47d5b35dd6a09669ea1e599a56e005
vcpkg_find_acquire_program(NASM) ff801b330d90fb0a43f4588e55c37df774e2cf2304e784dc09e5388a9a17dfba
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
vcpkg_replace_string b450deb79207478b37119743e00808ebc42de0628e7b98c14ab24728bd5c78b8
workaround_cmake_system_processor.patch 1fd13c06d27a2fcef233fe209ba0c856ed76311be59ae72e15d1c58fe36eab04
