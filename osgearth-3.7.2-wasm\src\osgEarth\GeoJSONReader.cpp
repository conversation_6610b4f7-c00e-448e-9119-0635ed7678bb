/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#include "GeoJSONReader.h"
#include <osgEarth/Notify>
#include <osgEarth/StringUtils>
#include <osgEarth/FileUtils>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <cctype>

using namespace osgEarth;

#define LC "[GeoJSONReader] "

// Static constants
const std::vector<std::string> GeoJSONReader::GEOMETRY_TYPES = {
    "Point", "LineString", "Polygon", "MultiPoint", "MultiLineString", "MultiPolygon", "GeometryCollection"
};

const std::vector<std::string> GeoJSONReader::FEATURE_TYPES = {
    "Feature", "FeatureCollection"
};

GeoJSONReader::GeoJSONReader() : _currentPos(0)
{
}

GeoJSONReader::~GeoJSONReader()
{
}

bool GeoJSONReader::readFeatures(const std::string& filename, std::vector<osg::ref_ptr<Feature>>& features)
{
    clearError();
    features.clear();
    
    // Read file content
    std::ifstream file(filename);
    if (!file.is_open())
    {
        setError("Cannot open file: " + filename);
        return false;
    }
    
    std::stringstream buffer;
    buffer << file.rdbuf();
    std::string content = buffer.str();
    file.close();
    
    return readFeaturesFromString(content, features);
}

bool GeoJSONReader::readFeaturesFromString(const std::string& content, std::vector<osg::ref_ptr<Feature>>& features)
{
    clearError();
    features.clear();
    
    if (content.empty())
    {
        setError("Empty content");
        return false;
    }
    
    _currentJson = trim(content);
    
    if (!isValidGeoJSON(_currentJson))
    {
        setError("Invalid GeoJSON format");
        return false;
    }
    
    if (isFeatureCollection(_currentJson))
    {
        // Extract features from FeatureCollection
        std::vector<std::string> featureStrings = GeoJSONUtils::extractFeatures(_currentJson);
        
        for (const auto& featureStr : featureStrings)
        {
            osg::ref_ptr<Feature> feature = parseFeature(featureStr);
            if (feature.valid())
            {
                features.push_back(feature);
            }
        }
    }
    else if (isFeature(_currentJson))
    {
        // Single feature
        osg::ref_ptr<Feature> feature = parseFeature(_currentJson);
        if (feature.valid())
        {
            features.push_back(feature);
        }
    }
    else if (isGeometry(_currentJson))
    {
        // Single geometry - create feature with no properties
        Geometry* geometry = parseGeometry(_currentJson);
        if (geometry)
        {
            osg::ref_ptr<Feature> feature = new Feature(geometry, SpatialReference::get("wgs84"));
            features.push_back(feature);
        }
    }
    else
    {
        setError("Unrecognized GeoJSON structure");
        return false;
    }
    
    return !features.empty();
}

osg::ref_ptr<Feature> GeoJSONReader::parseFeature(const std::string& featureJson)
{
    std::string geometryStr = extractObject(featureJson, "geometry");
    std::string propertiesStr = extractObject(featureJson, "properties");
    
    Geometry* geometry = nullptr;
    if (!geometryStr.empty())
    {
        geometry = parseGeometry(geometryStr);
    }
    
    osg::ref_ptr<Feature> feature = new Feature(geometry, SpatialReference::get("wgs84"));
    
    // Parse properties
    if (!propertiesStr.empty())
    {
        AttributeTable attributes;
        parseProperties(propertiesStr, attributes);
        feature->setAttrs(attributes);
    }
    
    return feature;
}

Geometry* GeoJSONReader::parseGeometry(const std::string& geometryJson)
{
    std::string type = removeQuotes(extractValue(geometryJson, "type"));
    std::string coordinates = extractValue(geometryJson, "coordinates");
    
    if (type == "Point")
    {
        return parsePoint(coordinates);
    }
    else if (type == "LineString")
    {
        return parseLineString(coordinates);
    }
    else if (type == "Polygon")
    {
        return parsePolygon(coordinates);
    }
    else if (type == "MultiPoint")
    {
        return parseMultiPoint(coordinates);
    }
    else if (type == "MultiLineString")
    {
        return parseMultiLineString(coordinates);
    }
    else if (type == "MultiPolygon")
    {
        return parseMultiPolygon(coordinates);
    }
    else
    {
        setError("Unsupported geometry type: " + type);
        return nullptr;
    }
}

Point* GeoJSONReader::parsePoint(const std::string& coordinatesJson)
{
    osg::Vec3d coord = parseCoordinate(coordinatesJson);
    return new Point(coord);
}

LineString* GeoJSONReader::parseLineString(const std::string& coordinatesJson)
{
    std::vector<osg::Vec3d> coords = parseCoordinateArray(coordinatesJson);
    
    LineString* lineString = new LineString();
    for (const auto& coord : coords)
    {
        lineString->push_back(coord);
    }
    
    return lineString;
}

Polygon* GeoJSONReader::parsePolygon(const std::string& coordinatesJson)
{
    std::vector<std::vector<osg::Vec3d>> rings = parseCoordinateArrayArray(coordinatesJson);
    
    if (rings.empty())
        return nullptr;
    
    Polygon* polygon = new Polygon();
    
    // First ring is exterior
    for (const auto& coord : rings[0])
    {
        polygon->push_back(coord);
    }
    
    // Additional rings are holes
    for (size_t i = 1; i < rings.size(); ++i)
    {
        Ring* hole = new Ring();
        for (const auto& coord : rings[i])
        {
            hole->push_back(coord);
        }
        polygon->getHoles().push_back(hole);
    }
    
    return polygon;
}

MultiGeometry* GeoJSONReader::parseMultiPoint(const std::string& coordinatesJson)
{
    std::vector<osg::Vec3d> coords = parseCoordinateArray(coordinatesJson);
    
    MultiGeometry* multiGeom = new MultiGeometry();
    for (const auto& coord : coords)
    {
        multiGeom->getComponents().push_back(new Point(coord));
    }
    
    return multiGeom;
}

MultiGeometry* GeoJSONReader::parseMultiLineString(const std::string& coordinatesJson)
{
    std::vector<std::vector<osg::Vec3d>> lineStrings = parseCoordinateArrayArray(coordinatesJson);
    
    MultiGeometry* multiGeom = new MultiGeometry();
    for (const auto& coords : lineStrings)
    {
        LineString* lineString = new LineString();
        for (const auto& coord : coords)
        {
            lineString->push_back(coord);
        }
        multiGeom->getComponents().push_back(lineString);
    }
    
    return multiGeom;
}

MultiGeometry* GeoJSONReader::parseMultiPolygon(const std::string& coordinatesJson)
{
    // This is a simplified implementation
    // Full implementation would need to handle nested arrays properly
    MultiGeometry* multiGeom = new MultiGeometry();
    return multiGeom;
}

std::vector<osg::Vec3d> GeoJSONReader::parseCoordinateArray(const std::string& coordsJson)
{
    std::vector<osg::Vec3d> coords;
    
    std::string cleaned = trim(coordsJson);
    if (cleaned.front() == '[' && cleaned.back() == ']')
    {
        cleaned = cleaned.substr(1, cleaned.length() - 2);
    }
    
    // Simple parsing - split by '],' to find coordinate pairs
    size_t pos = 0;
    while (pos < cleaned.length())
    {
        size_t start = cleaned.find('[', pos);
        if (start == std::string::npos)
            break;
            
        size_t end = cleaned.find(']', start);
        if (end == std::string::npos)
            break;
            
        std::string coordStr = cleaned.substr(start + 1, end - start - 1);
        osg::Vec3d coord = parseCoordinate(coordStr);
        coords.push_back(coord);
        
        pos = end + 1;
    }
    
    return coords;
}

std::vector<std::vector<osg::Vec3d>> GeoJSONReader::parseCoordinateArrayArray(const std::string& coordsJson)
{
    std::vector<std::vector<osg::Vec3d>> result;
    
    // Simplified implementation
    std::vector<osg::Vec3d> coords = parseCoordinateArray(coordsJson);
    if (!coords.empty())
    {
        result.push_back(coords);
    }
    
    return result;
}

osg::Vec3d GeoJSONReader::parseCoordinate(const std::string& coordJson)
{
    std::vector<double> numbers = GeoJSONUtils::parseNumberArray("[" + coordJson + "]");
    
    if (numbers.size() >= 2)
    {
        double x = numbers[0];
        double y = numbers[1];
        double z = (numbers.size() > 2) ? numbers[2] : 0.0;
        return osg::Vec3d(x, y, z);
    }
    
    return osg::Vec3d(0, 0, 0);
}

void GeoJSONReader::parseProperties(const std::string& propertiesJson, AttributeTable& attributes)
{
    // Simplified property parsing
    // In a full implementation, this would parse JSON key-value pairs
    
    std::string cleaned = trim(propertiesJson);
    if (cleaned == "null" || cleaned.empty())
        return;
    
    // Basic parsing for simple string properties
    // This is a placeholder implementation
}

// Utility functions
std::string GeoJSONReader::trim(const std::string& str)
{
    size_t start = str.find_first_not_of(" \t\n\r");
    if (start == std::string::npos)
        return "";
    
    size_t end = str.find_last_not_of(" \t\n\r");
    return str.substr(start, end - start + 1);
}

std::string GeoJSONReader::removeQuotes(const std::string& str)
{
    std::string result = trim(str);
    if (result.length() >= 2 && result.front() == '"' && result.back() == '"')
    {
        return result.substr(1, result.length() - 2);
    }
    return result;
}

bool GeoJSONReader::isNumber(const std::string& str)
{
    std::string trimmed = trim(str);
    if (trimmed.empty())
        return false;
    
    char* end;
    strtod(trimmed.c_str(), &end);
    return *end == '\0';
}

double GeoJSONReader::parseNumber(const std::string& str)
{
    return std::stod(trim(str));
}

std::string GeoJSONReader::extractValue(const std::string& json, const std::string& key)
{
    std::string searchKey = "\"" + key + "\"";
    size_t keyPos = json.find(searchKey);
    if (keyPos == std::string::npos)
        return "";
    
    size_t colonPos = json.find(':', keyPos);
    if (colonPos == std::string::npos)
        return "";
    
    size_t valueStart = colonPos + 1;
    while (valueStart < json.length() && std::isspace(json[valueStart]))
        valueStart++;
    
    if (valueStart >= json.length())
        return "";
    
    // Find the end of the value
    size_t valueEnd = valueStart;
    if (json[valueStart] == '"')
    {
        // String value
        valueEnd = json.find('"', valueStart + 1);
        if (valueEnd != std::string::npos)
            valueEnd++;
    }
    else if (json[valueStart] == '[')
    {
        // Array value
        valueEnd = GeoJSONUtils::findMatchingBrace(json, valueStart, '[', ']');
        if (valueEnd != std::string::npos)
            valueEnd++;
    }
    else if (json[valueStart] == '{')
    {
        // Object value
        valueEnd = GeoJSONUtils::findMatchingBrace(json, valueStart, '{', '}');
        if (valueEnd != std::string::npos)
            valueEnd++;
    }
    else
    {
        // Primitive value (number, boolean, null)
        valueEnd = json.find_first_of(",}", valueStart);
        if (valueEnd == std::string::npos)
            valueEnd = json.length();
    }
    
    if (valueEnd > valueStart)
    {
        return trim(json.substr(valueStart, valueEnd - valueStart));
    }
    
    return "";
}

std::string GeoJSONReader::extractObject(const std::string& json, const std::string& key)
{
    std::string value = extractValue(json, key);
    if (value.empty() || value == "null")
        return "";
    
    return value;
}

bool GeoJSONReader::isValidGeoJSON(const std::string& json)
{
    return GeoJSONUtils::isValidJSON(json);
}

bool GeoJSONReader::isFeatureCollection(const std::string& json)
{
    std::string type = removeQuotes(extractValue(json, "type"));
    return type == "FeatureCollection";
}

bool GeoJSONReader::isFeature(const std::string& json)
{
    std::string type = removeQuotes(extractValue(json, "type"));
    return type == "Feature";
}

bool GeoJSONReader::isGeometry(const std::string& json)
{
    std::string type = removeQuotes(extractValue(json, "type"));
    return std::find(GEOMETRY_TYPES.begin(), GEOMETRY_TYPES.end(), type) != GEOMETRY_TYPES.end();
}

void GeoJSONReader::setError(const std::string& error)
{
    _lastError = error;
    OE_WARN << LC << error << std::endl;
}

void GeoJSONReader::clearError()
{
    _lastError.clear();
}

// GeoJSONUtils implementation
namespace osgEarth { namespace GeoJSONUtils {

bool isValidJSON(const std::string& json)
{
    std::string trimmed = trim(json);
    return !trimmed.empty() && 
           ((trimmed.front() == '{' && trimmed.back() == '}') ||
            (trimmed.front() == '[' && trimmed.back() == ']'));
}

size_t findMatchingBrace(const std::string& json, size_t startPos, char openChar, char closeChar)
{
    if (startPos >= json.length() || json[startPos] != openChar)
        return std::string::npos;
    
    int depth = 1;
    size_t pos = startPos + 1;
    
    while (pos < json.length() && depth > 0)
    {
        if (json[pos] == openChar)
            depth++;
        else if (json[pos] == closeChar)
            depth--;
        pos++;
    }
    
    return (depth == 0) ? pos - 1 : std::string::npos;
}

std::vector<std::string> extractFeatures(const std::string& featureCollectionJson)
{
    std::vector<std::string> features;
    
    // Find the "features" array
    std::string featuresArray = extractValue(featureCollectionJson, "features");
    if (featuresArray.empty())
        return features;
    
    // Parse the array - simplified implementation
    // In a full implementation, this would properly parse the JSON array
    
    return features;
}

std::vector<double> parseNumberArray(const std::string& arrayJson)
{
    std::vector<double> numbers;
    
    std::string cleaned = trim(arrayJson);
    if (cleaned.front() == '[' && cleaned.back() == ']')
    {
        cleaned = cleaned.substr(1, cleaned.length() - 2);
    }
    
    std::stringstream ss(cleaned);
    std::string item;
    
    while (std::getline(ss, item, ','))
    {
        item = trim(item);
        if (!item.empty())
        {
            try
            {
                double num = std::stod(item);
                numbers.push_back(num);
            }
            catch (...)
            {
                // Skip invalid numbers
            }
        }
    }
    
    return numbers;
}

std::string trim(const std::string& str)
{
    size_t start = str.find_first_not_of(" \t\n\r");
    if (start == std::string::npos)
        return "";
    
    size_t end = str.find_last_not_of(" \t\n\r");
    return str.substr(start, end - start + 1);
}

std::string extractValue(const std::string& json, const std::string& key)
{
    std::string searchKey = "\"" + key + "\"";
    size_t keyPos = json.find(searchKey);
    if (keyPos == std::string::npos)
        return "";
    
    size_t colonPos = json.find(':', keyPos);
    if (colonPos == std::string::npos)
        return "";
    
    size_t valueStart = colonPos + 1;
    while (valueStart < json.length() && std::isspace(json[valueStart]))
        valueStart++;
    
    if (valueStart >= json.length())
        return "";
    
    // Find the end of the value
    size_t valueEnd = valueStart;
    if (json[valueStart] == '"')
    {
        // String value
        valueEnd = json.find('"', valueStart + 1);
        if (valueEnd != std::string::npos)
            valueEnd++;
    }
    else if (json[valueStart] == '[')
    {
        // Array value
        valueEnd = findMatchingBrace(json, valueStart, '[', ']');
        if (valueEnd != std::string::npos)
            valueEnd++;
    }
    else if (json[valueStart] == '{')
    {
        // Object value
        valueEnd = findMatchingBrace(json, valueStart, '{', '}');
        if (valueEnd != std::string::npos)
            valueEnd++;
    }
    else
    {
        // Primitive value
        valueEnd = json.find_first_of(",}", valueStart);
        if (valueEnd == std::string::npos)
            valueEnd = json.length();
    }
    
    if (valueEnd > valueStart)
    {
        return trim(json.substr(valueStart, valueEnd - valueStart));
    }
    
    return "";
}

}} // namespace osgEarth::GeoJSONUtils
