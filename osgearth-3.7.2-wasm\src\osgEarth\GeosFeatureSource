/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/FeatureSource>
#include <osgEarth/GEOS>
#include <queue>
#include <thread>

namespace osgEarth
{
    /**
     * Feature Layer that accesses features via GEOS library for vector data processing.
     * Supports Shapefile and GeoJSON formats without GDAL dependency.
     */
    class OSGEARTH_EXPORT GeosFeatureSource : public FeatureSource
    {
    public: // serialization
        class OSGEARTH_EXPORT Options : public FeatureSource::Options
        {
        public:
            META_LayerOptions(osgEarth, Options, FeatureSource::Options);
            OE_OPTION(URI, url);
            OE_OPTION(std::string, connection);
            OE_OPTION(std::string, format); // "shapefile" or "geojson"
            OE_OPTION(bool, buildSpatialIndex);
            OE_OPTION(bool, forceRebuildSpatialIndex);
            OE_OPTION(Config, geometryConfig);
            OE_OPTION(URI, geometryUrl);
            OE_OPTION(std::string, layer);
            OE_OPTION(Query, query);
            virtual Config getConfig() const;

        private:
            void fromConfig(const Config &conf);
        };

    public:
        META_Layer(osgEarth, GeosFeatureSource, Options, FeatureSource, GeosFeatures);

        //! Location of the data resource
        void setURL(const URI &value);
        const URI &getURL() const;

        //! Database connection string (alterative to URL)
        void setConnection(const std::string &value);
        const std::string &getConnection() const;

        //! File format (shapefile or geojson)
        void setFormat(const std::string &value);
        const std::string &getFormat() const;

        //! Whether to build a spatial index after opening the resource (if supported)
        void setBuildSpatialIndex(const bool &value);
        const bool &getBuildSpatialIndex() const;

        //! Geometry configuration for explicit geometry
        void setGeometryConfig(const Config &value);
        const Config &getGeometryConfig() const;

        //! URL to geometry data
        void setGeometryUrl(const URI &value);
        const URI &getGeometryUrl() const;

        //! Layer name (for multi-layer formats)
        void setLayer(const std::string &value);
        const std::string &getLayer() const;

        //! Query to apply to the data
        void setQuery(const Query &value);
        const Query &getQuery() const;

        //! Explicit geometry to use instead of loading from URL
        void setGeometry(Geometry *geom);
        const Geometry *getGeometry() const;

    public: // Layer
        Status openImplementation() override;

        Status closeImplementation() override;

    public: // FeatureSource
        FeatureCursor *createFeatureCursorImplementation(const Query &query, ProgressCallback *progress) const override;

        bool deleteFeature(FeatureID fid) override;

        int getFeatureCount() const override;

        bool supportsGetFeature() const override;

        Feature *getFeature(FeatureID fid) override;

        bool isWritable() const override;

        const FeatureSchema &getSchema() const override;

        bool insertFeature(Feature *feature) override;

        Geometry *createGeometry(const Config &geomConf);

        virtual std::string getFeatureUrl();

    protected: // Layer
        void init() override;

    private:
        void buildSpatialIndex();

        void readFeatures();

        void readShapefile();

        void readGeoJSON();

        bool parseGeometry(const std::string &wkt, Feature *feature);

        void applyOverrides(Feature *feature);

        void applyOverrides(AttributeTable &table);

        void refreshGeometry(const Config &conf);

        void establishProfile();

        void computeDataExtents();

        void computeDataExtentsFromGeometry();

        void computeDataExtentsFromFeatures();

        void computeDataExtentsFromURL();

        void computeDataExtentsFromConnection();

        void computeDataExtentsFromQuery();

        void computeDataExtentsFromLayer();

        void computeDataExtentsFromFormat();

        void computeDataExtentsFromSpatialIndex();

        void computeDataExtentsFromRebuildSpatialIndex();

        void computeDataExtentsFromGeometryConfig();

        void computeDataExtentsFromGeometryUrl();

        void computeDataExtentsFromGeometry();

        void computeDataExtentsFromFeatureCount();

        void computeDataExtentsFromSchema();

        void computeDataExtentsFromWritable();

        void computeDataExtentsFromSupportsGetFeature();

        void computeDataExtentsFromDeleteFeature();

        void computeDataExtentsFromInsertFeature();

        void computeDataExtentsFromCreateGeometry();

        void computeDataExtentsFromGetFeatureUrl();

        void computeDataExtentsFromBuildSpatialIndex();

        void computeDataExtentsFromReadFeatures();

        void computeDataExtentsFromReadShapefile();

        void computeDataExtentsFromReadGeoJSON();

        void computeDataExtentsFromParseGeometry();

        void computeDataExtentsFromApplyOverrides();

        void computeDataExtentsFromRefreshGeometry();

        void computeDataExtentsFromEstablishProfile();

    private:
        mutable Threading::Mutex _mutex;
        mutable FeatureList _features;
        mutable FeatureSchema _schema;
        mutable GeoExtent _extent;
        mutable bool _extentComputed;
        mutable bool _featuresLoaded;
        osg::ref_ptr<Geometry> _geometry;
        std::string _geometryUrlString;
        std::string _connectionString;
        std::string _layerName;
        std::string _formatString;
        Query _query;
        bool _buildSpatialIndex;
        bool _forceRebuildSpatialIndex;
        Config _geometryConfig;
        URI _geometryUrl;
        URI _url;
        std::string _connection;
        std::string _format;
        std::string _layer;
        bool _isWritable;
        int _featureCount;
        bool _supportsGetFeature;
        bool _deleteFeature;
        bool _insertFeature;
        bool _createGeometry;
        std::string _featureUrl;
        bool _readFeatures;
        bool _readShapefile;
        bool _readGeoJSON;
        bool _parseGeometry;
        bool _applyOverrides;
        bool _refreshGeometry;
        bool _establishProfile;
    };

    REGISTER_OSGEARTH_LAYER(GeosFeatures, GeosFeatureSource);

} // namespace osgEarth
