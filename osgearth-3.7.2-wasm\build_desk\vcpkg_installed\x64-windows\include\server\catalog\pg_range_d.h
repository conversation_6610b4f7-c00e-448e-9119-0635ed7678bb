/*-------------------------------------------------------------------------
 *
 * pg_range_d.h
 *    Macro definitions for pg_range
 *
 * Portions Copyright (c) 1996-2023, PostgreSQL Global Development Group
 * Portions Copyright (c) 1994, Regents of the University of California
 *
 * NOTES
 *  ******************************
 *  *** DO NOT EDIT THIS FILE! ***
 *  ******************************
 *
 *  It has been GENERATED by src/backend/catalog/genbki.pl
 *
 *-------------------------------------------------------------------------
 */
#ifndef PG_RANGE_D_H
#define PG_RANGE_D_H

#define RangeRelationId 3541
#define RangeTypidIndexId 3542
#define RangeMultirangeTypidIndexId 2228

#define Anum_pg_range_rngtypid 1
#define Anum_pg_range_rngsubtype 2
#define Anum_pg_range_rngmultitypid 3
#define Anum_pg_range_rngcollation 4
#define Anum_pg_range_rngsubopc 5
#define Anum_pg_range_rngcanonical 6
#define Anum_pg_range_rngsubdiff 7

#define Natts_pg_range 7


#endif							/* PG_RANGE_D_H */
