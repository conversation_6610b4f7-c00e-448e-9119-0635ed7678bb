{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/meshoptimizer-x64-windows-0.24-bbbb5975-6a27-4fb8-83d4-b33a4786a70d", "name": "meshoptimizer:x64-windows@0.24 877df2fa7d4974a92c62be8caf751416e1e5b995d840b59659839285cdeabb40", "creationInfo": {"creators": ["Tool: vcpkg-2025-06-20-ef7c0d541124bbdd334a03467e7edb6c3364d199"], "created": "2025-07-03T02:22:50Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-2"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "meshoptimizer", "SPDXID": "SPDXRef-port", "versionInfo": "0.24", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/meshoptimizer", "homepage": "https://github.com/zeux/meshoptimizer", "licenseConcluded": "MIT", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "Mesh optimization library that makes meshes smaller and faster to render", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "meshoptimizer:x64-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "877df2fa7d4974a92c62be8caf751416e1e5b995d840b59659839285cdeabb40", "downloadLocation": "NONE", "licenseConcluded": "MIT", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "zeux/meshoptimizer", "downloadLocation": "git+https://github.com/zeux/meshoptimizer@v0.24", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "d8342aadd48c0f51aa014ba56ff4c97f4780194eabf78d8a867876fb49f5d103597748ec4aa3613e236e2c42c5ca58d46a9ad3b7c458de5e1f01546d9951bfb4"}]}, {"SPDXID": "SPDXRef-resource-1", "name": "zeux/basis_universal", "downloadLocation": "git+https://github.com/zeux/basis_universal@88e813c46b3ff42e56ef947b3fa11eeee7a504b0", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "cc9e4dbaed556fac30f4426ead9c8fb018ca8540bd1188849a90396192e410a5fdc6f38edbad07d2615583fbdfe01a79989d426caed7614d38b93731bbe3d4ae"}]}], "files": [{"fileName": "./fix-cmake.patch", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "6f183053f1a421036b86e394759c6c05384176bb99d80a201d24f83bb75440b3"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "2a5ca40374dde84b2a80a90a24714e231c39b22fc48520365b1852856187bf22"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-2", "checksums": [{"algorithm": "SHA256", "checksumValue": "9e7952a5ffb50a1c515c19dabe8c39516517fed0ad1adfd2d5120ae31e3fcad6"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}