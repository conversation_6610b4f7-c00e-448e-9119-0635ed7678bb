{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/geos-x64-windows-3.13.0#1-a0a0b819-cf63-4f57-9e54-7c96c3a0064b", "name": "geos:x64-windows@3.13.0#1 c7446fcbaa65dcda4fdcba310e0ff175f25cf7f4b30dbf5a640aec2898dec309", "creationInfo": {"creators": ["Tool: vcpkg-2025-06-20-ef7c0d541124bbdd334a03467e7edb6c3364d199"], "created": "2025-07-02T08:20:25Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-2"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-3"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-3", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-3", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "geos", "SPDXID": "SPDXRef-port", "versionInfo": "3.13.0#1", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/geos", "homepage": "https://libgeos.org/", "licenseConcluded": "LGPL-2.1-only", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "Geometry Engine Open Source", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "geos:x64-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "c7446fcbaa65dcda4fdcba310e0ff175f25cf7f4b30dbf5a640aec2898dec309", "downloadLocation": "NONE", "licenseConcluded": "LGPL-2.1-only", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "geos-3.13.0.tar.bz2", "packageFileName": "geos-3.13.0.tar.bz2", "downloadLocation": "https://download.osgeo.org/geos/geos-3.13.0.tar.bz2", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "8ffaa3f49a8365db693ac948e9d66cf55321eb12151734c7da2775070b7804ffa607de2474b7019d6ea2a99d5e037fb1e8561bf9025e65ddd4bd1ba049382b28"}]}, {"SPDXID": "SPDXRef-resource-1", "name": "geos-3.13.0-msvc-2017.diff", "packageFileName": "geos-3.13.0-msvc-2017.diff", "downloadLocation": "https://github.com/libgeos/geos/commit/46e9f158073ebf0d4ec8b7dde37c155d097bc0d7.diff?full_index=1", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "9fa1ccc4c66e8268c59bcac218015c2b10ee594bece837e6d0fc78fe700233abd1b2df7aa396c00786ffb170fbfbb0ab530f5007ba10376a2366ee3472d8b02a"}]}], "files": [{"fileName": "./fix-exported-config.patch", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "7e13066e3def3ee38790932aae2df6ddca11453781c1ce3573af255fa1ef449b"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "459f983d78780d5ce1b48a2e485aaf3d80b9725ddf53ce7047b6506b02c26c6e"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./usage", "SPDXID": "SPDXRef-file-2", "checksums": [{"algorithm": "SHA256", "checksumValue": "f9e9f717fa672051e196a319d7722a7405f8781222515818fd1c7814dc278bf3"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-3", "checksums": [{"algorithm": "SHA256", "checksumValue": "ad8c55566cfa7e126fbd132975d5763e8fdf2fb17071646fa835617f37fe7787"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}