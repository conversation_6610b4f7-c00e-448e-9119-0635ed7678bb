/*-------------------------------------------------------------------------
 *
 * pg_shseclabel_d.h
 *    Macro definitions for pg_shseclabel
 *
 * Portions Copyright (c) 1996-2023, PostgreSQL Global Development Group
 * Portions Copyright (c) 1994, Regents of the University of California
 *
 * NOTES
 *  ******************************
 *  *** DO NOT EDIT THIS FILE! ***
 *  ******************************
 *
 *  It has been GENERATED by src/backend/catalog/genbki.pl
 *
 *-------------------------------------------------------------------------
 */
#ifndef PG_SHSECLABEL_D_H
#define PG_SHSECLABEL_D_H

#define SharedSecLabelRelationId 3592
#define SharedSecLabelRelation_Rowtype_Id 4066
#define PgShseclabelToastTable 4060
#define PgShseclabelToastIndex 4061
#define SharedSecLabelObjectIndexId 3593

#define Anum_pg_shseclabel_objoid 1
#define Anum_pg_shseclabel_classoid 2
#define Anum_pg_shseclabel_provider 3
#define Anum_pg_shseclabel_label 4

#define Natts_pg_shseclabel 4


#endif							/* PG_SHSECLABEL_D_H */
