/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#ifndef OSGEARTH_TIME_SERIES_IMAGE_H
#define OSGEARTH_TIME_SERIES_IMAGE_H

#include <osgEarth/Common>
#include <osgEarth/DateTime>
#include <osg/Image>
#include <map>

namespace osgEarth
{
    /**
     * An image that contains multiple images, each corresponding to a DateTime.
     */
    class OSGEARTH_EXPORT TimeSeriesImage : public osg::Image
    {
    public:
        //! Construct a new empty time series image
        TimeSeriesImage();

        //! Insert a new timestamped iunto the temporal image
        void insert(const DateTime& dt, const osg::Image* image);
        void insert(const DateTime& dt, osg::ref_ptr<osg::Image> image);

        //! Set the active datetime to use
        void setDateTime(const DateTime& dt);

        //! Gets the extent
        const DateTimeExtent& getDateTimeExtent() const;

    public: // osg::Image

        //! indicate that we require an update traversal
        bool requiresUpdateCall() const override { return true; }

        //! update traversal
        void update(osg::NodeVisitor* nv) override;

    private:
        using Table = std::map<TimeStamp, osg::ref_ptr<const osg::Image>>;

        Table _images;
        Table::iterator _ptr;
        DateTimeExtent _extent;
    };

} // namespace osgEarth

#endif // OSGEARTH_TIME_SERIES_IMAGE_H
