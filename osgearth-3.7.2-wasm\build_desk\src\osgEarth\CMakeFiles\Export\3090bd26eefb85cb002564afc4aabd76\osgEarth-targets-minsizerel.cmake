#----------------------------------------------------------------
# Generated CMake target import file for configuration "MinSizeRel".
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "osgEarth::osgEarth" for configuration "MinSizeRel"
set_property(TARGET osgEarth::osgEarth APPEND PROPERTY IMPORTED_CONFIGURATIONS MINSIZEREL)
set_target_properties(osgEarth::osgEarth PROPERTIES
  IMPORTED_IMPLIB_MINSIZEREL "${_IMPORT_PREFIX}/lib/osgEarths.lib"
  IMPORTED_LINK_DEPENDENT_LIBRARIES_MINSIZEREL "spdlog::spdlog;GEOS::geos_c;GeographicLib::GeographicLib_SHARED;blend2d::blend2d;meshoptimizer::meshoptimizer;CURL::libcurl_shared"
  IMPORTED_LOCATION_MINSIZEREL "${_IMPORT_PREFIX}/bin/osgEarths.dll"
  )

list(APPEND _cmake_import_check_targets osgEarth::osgEarth )
list(APPEND _cmake_import_check_files_for_osgEarth::osgEarth "${_IMPORT_PREFIX}/lib/osgEarths.lib" "${_IMPORT_PREFIX}/bin/osgEarths.dll" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
