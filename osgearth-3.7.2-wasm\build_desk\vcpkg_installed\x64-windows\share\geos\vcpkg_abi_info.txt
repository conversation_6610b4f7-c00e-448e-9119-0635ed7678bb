cmake 3.30.1
features core
fix-exported-config.patch 7e13066e3def3ee38790932aae2df6ddca11453781c1ce3573af255fa1ef449b
portfile.cmake 459f983d78780d5ce1b48a2e485aaf3d80b9725ddf53ce7047b6506b02c26c6e
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
powershell 7.5.1
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-9322494aa025c04635f453cbc392b0ca170d297c
usage f9e9f717fa672051e196a319d7722a7405f8781222515818fd1c7814dc278bf3
vcpkg-cmake fa0a421976433b6b0635f7ccfd6e08f02a20aef877e749d6792bc1d9ae8233af
vcpkg-cmake-config 8d261494f5cab6cecd7851edc7eb14f27f3c82cd259e8a56095998d1199973a0
vcpkg.json ad8c55566cfa7e126fbd132975d5763e8fdf2fb17071646fa835617f37fe7787
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_download_distfile 5b22c0d0a397f4fdd0aa1c054afc84c20c91591d669027688b7c11334c3e4d0e
vcpkg_extract_source_archive 9c733832f8b58b05e6c329ae8b6ad1cfa858224fdc06d4a2d08b428e31de1a51
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
