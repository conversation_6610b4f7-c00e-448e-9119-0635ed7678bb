﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\sky_simple\SimpleSkyExtension.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\sky_simple\SimpleSkyNode.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\sky_simple\BrunetonImpl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\sky_simple\eb_atmosphere_model.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\sky_simple\eb_texture_buffer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\sky_simple\eb_utility.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\sky_simple\eb_ogl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\sky_simple\eb_shaders.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\src\osgEarthDrivers\sky_simple\AutoGenShaders.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\sky_simple\SimpleSkyShaders.cpp.in">
      <Filter>Template Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\CMakeFiles\04007df8121efd1443175670f94bba66\AutoGenShaders.cpp.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\sky_simple\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\sky_simple\SimpleSkyOptions">
      <Filter>Header Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\sky_simple\SimpleSky.Atmosphere.frag.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\sky_simple\SimpleSky.Atmosphere.vert.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\sky_simple\SimpleSky.Ground.ONeil.frag.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\sky_simple\SimpleSky.Ground.ONeil.vert.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\sky_simple\SimpleSky.Moon.frag.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\sky_simple\SimpleSky.Moon.vert.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\sky_simple\SimpleSky.Stars.frag.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\sky_simple\SimpleSky.Stars.vert.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\sky_simple\SimpleSky.Stars.GLES.frag.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\sky_simple\SimpleSky.Stars.GLES.vert.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\sky_simple\SimpleSky.Sun.frag.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\sky_simple\SimpleSky.Sun.vert.glsl">
      <Filter>Shader Files</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{54198C7B-7380-397A-9D96-335F5A2AEB54}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{1756C0FE-72DA-3F6B-A66E-FF01CEE968FE}</UniqueIdentifier>
    </Filter>
    <Filter Include="Shader Files">
      <UniqueIdentifier>{83459A2F-02AA-345A-8032-712D886979B4}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{68052041-0245-3641-9AD3-F5A7AC56E6F6}</UniqueIdentifier>
    </Filter>
    <Filter Include="Template Files">
      <UniqueIdentifier>{F03E10C6-7D85-3D8B-A504-3731A0C4FF5F}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
