/*-------------------------------------------------------------------------
 *
 * geqo_selection.h
 *	  prototypes for selection routines in optimizer/geqo
 *
 * Portions Copyright (c) 1996-2023, PostgreSQL Global Development Group
 * Portions Copyright (c) 1994, Regents of the University of California
 *
 * src/include/optimizer/geqo_selection.h
 *
 *-------------------------------------------------------------------------
 */

/* contributed by:
   =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
   *  <PERSON>				 * Institute of Automatic Control	   *
   =							 = University of Mining and Technology =
   *  <EMAIL>  * <PERSON>, Germany				   *
   =*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=*=
 */


#ifndef GEQO_SELECTION_H
#define GEQO_SELECTION_H

#include "optimizer/geqo.h"


extern void geqo_selection(PlannerInfo *root,
						   Chromosome *momma, Chromosome *daddy,
						   Pool *pool, double bias);

#endif							/* GEQO_SELECTION_H */
