/*-------------------------------------------------------------------------
 *
 * pg_foreign_table_d.h
 *    Macro definitions for pg_foreign_table
 *
 * Portions Copyright (c) 1996-2023, PostgreSQL Global Development Group
 * Portions Copyright (c) 1994, Regents of the University of California
 *
 * NOTES
 *  ******************************
 *  *** DO NOT EDIT THIS FILE! ***
 *  ******************************
 *
 *  It has been GENERATED by src/backend/catalog/genbki.pl
 *
 *-------------------------------------------------------------------------
 */
#ifndef PG_FOREIGN_TABLE_D_H
#define PG_FOREIGN_TABLE_D_H

#define ForeignTableRelationId 3118
#define ForeignTableRelidIndexId 3119

#define Anum_pg_foreign_table_ftrelid 1
#define Anum_pg_foreign_table_ftserver 2
#define Anum_pg_foreign_table_ftoptions 3

#define Natts_pg_foreign_table 3


#endif							/* PG_FOREIGN_TABLE_D_H */
