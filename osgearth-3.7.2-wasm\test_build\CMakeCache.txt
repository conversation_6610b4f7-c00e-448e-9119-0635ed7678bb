# This is the CMakeCache file.
# For build in directory: f:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/test_build
# It was generated by CMake: C:/Program Files/CMake/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//No help, variable specified on the command line.
CMAKE_BUILD_TYPE:UNINITIALIZED=Release

//Semicolon separated list of supported configuration types, only
// supports Debug, Release, MinSizeRel, and RelWithDebInfo, anything
// else will be ignored.
CMAKE_CONFIGURATION_TYPES:STRING=Debug;Release;MinSizeRel;RelWithDebInfo

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/test_build/CMakeFiles/pkgRedirects

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=osgEarth SDK

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=https://github.com/gwaldron/osgearth

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=OSGEARTH

//No help, variable specified on the command line.
CMAKE_TOOLCHAIN_FILE:UNINITIALIZED=test_CMakeLists

//Value Computed by CMake
OSGEARTH_BINARY_DIR:STATIC=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/test_build

//Value Computed by CMake
OSGEARTH_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
OSGEARTH_SOURCE_DIR:STATIC=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm


########################
# INTERNAL cache entries
########################

//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=f:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/test_build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=26
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=4
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cpack.exe
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=C:/Program Files/CMake/bin/ctest.exe
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Visual Studio 17 2022
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=C:/Program Files/Microsoft Visual Studio/2022/Enterprise
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=C:/Program Files/CMake/share/cmake-3.26

