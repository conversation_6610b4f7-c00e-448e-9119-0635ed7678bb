/*-------------------------------------------------------------------------
 *
 * pg_ts_config_d.h
 *    Macro definitions for pg_ts_config
 *
 * Portions Copyright (c) 1996-2023, PostgreSQL Global Development Group
 * Portions Copyright (c) 1994, Regents of the University of California
 *
 * NOTES
 *  ******************************
 *  *** DO NOT EDIT THIS FILE! ***
 *  ******************************
 *
 *  It has been GENERATED by src/backend/catalog/genbki.pl
 *
 *-------------------------------------------------------------------------
 */
#ifndef PG_TS_CONFIG_D_H
#define PG_TS_CONFIG_D_H

#define TSConfigRelationId 3602
#define TSConfigNameNspIndexId 3608
#define TSConfigOidIndexId 3712

#define Anum_pg_ts_config_oid 1
#define Anum_pg_ts_config_cfgname 2
#define Anum_pg_ts_config_cfgnamespace 3
#define Anum_pg_ts_config_cfgowner 4
#define Anum_pg_ts_config_cfgparser 5

#define Natts_pg_ts_config 5


#endif							/* PG_TS_CONFIG_D_H */
