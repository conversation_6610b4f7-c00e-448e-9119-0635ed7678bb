// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: glyphs.proto
// Protobuf C++ Version: 5.29.3

#ifndef glyphs_2eproto_2epb_2eh
#define glyphs_2eproto_2epb_2eh

#include <limits>
#include <string>
#include <type_traits>
#include <utility>

#include "google/protobuf/runtime_version.h"
#if PROTOBUF_VERSION != 5029003
#error "Protobuf C++ gencode is built with an incompatible version of"
#error "Protobuf C++ headers/runtime. See"
#error "https://protobuf.dev/support/cross-version-runtime-guarantee/#cpp"
#endif
#include "google/protobuf/io/coded_stream.h"
#include "google/protobuf/arena.h"
#include "google/protobuf/arenastring.h"
#include "google/protobuf/generated_message_tctable_decl.h"
#include "google/protobuf/generated_message_util.h"
#include "google/protobuf/metadata_lite.h"
#include "google/protobuf/message_lite.h"
#include "google/protobuf/repeated_field.h"  // IWYU pragma: export
#include "google/protobuf/extension_set.h"  // IWYU pragma: export
// @@protoc_insertion_point(includes)

// Must be included last.
#include "google/protobuf/port_def.inc"

#define PROTOBUF_INTERNAL_EXPORT_glyphs_2eproto

namespace google {
namespace protobuf {
namespace internal {
template <typename T>
::absl::string_view GetAnyMessageName();
}  // namespace internal
}  // namespace protobuf
}  // namespace google

// Internal implementation detail -- do not use these members.
struct TableStruct_glyphs_2eproto {
  static const ::uint32_t offsets[];
};
namespace mapboxgl {
namespace glyphs {
class fontstack;
struct fontstackDefaultTypeInternal;
extern fontstackDefaultTypeInternal _fontstack_default_instance_;
class glyph;
struct glyphDefaultTypeInternal;
extern glyphDefaultTypeInternal _glyph_default_instance_;
class glyphs;
struct glyphsDefaultTypeInternal;
extern glyphsDefaultTypeInternal _glyphs_default_instance_;
}  // namespace glyphs
}  // namespace mapboxgl
namespace google {
namespace protobuf {
}  // namespace protobuf
}  // namespace google

namespace mapboxgl {
namespace glyphs {

// ===================================================================


// -------------------------------------------------------------------

class glyph final : public ::google::protobuf::MessageLite
/* @@protoc_insertion_point(class_definition:mapboxgl.glyphs.glyph) */ {
 public:
  inline glyph() : glyph(nullptr) {}
  ~glyph() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(glyph* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(glyph));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR glyph(
      ::google::protobuf::internal::ConstantInitialized);

  inline glyph(const glyph& from) : glyph(nullptr, from) {}
  inline glyph(glyph&& from) noexcept
      : glyph(nullptr, std::move(from)) {}
  inline glyph& operator=(const glyph& from) {
    CopyFrom(from);
    return *this;
  }
  inline glyph& operator=(glyph&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const glyph& default_instance() {
    return *internal_default_instance();
  }
  static inline const glyph* internal_default_instance() {
    return reinterpret_cast<const glyph*>(
        &_glyph_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 0;
  friend void swap(glyph& a, glyph& b) { a.Swap(&b); }
  inline void Swap(glyph* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(glyph* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  glyph* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::MessageLite::DefaultConstruct<glyph>(arena);
  }
  void CopyFrom(const glyph& from);
  void MergeFrom(const glyph& from) { glyph::MergeImpl(*this, from); }

  private:
  static void MergeImpl(::google::protobuf::MessageLite& to_msg,
                        const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return IsInitializedImpl(*this);
  }

  private:
  static bool IsInitializedImpl(const MessageLite& msg);

  public:
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(glyph* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "mapboxgl.glyphs.glyph"; }

 protected:
  explicit glyph(::google::protobuf::Arena* arena);
  glyph(::google::protobuf::Arena* arena, const glyph& from);
  glyph(::google::protobuf::Arena* arena, glyph&& from) noexcept
      : glyph(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataLite<22> _class_data_;

 public:
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kBitmapFieldNumber = 2,
    kIdFieldNumber = 1,
    kWidthFieldNumber = 3,
    kHeightFieldNumber = 4,
    kLeftFieldNumber = 5,
    kTopFieldNumber = 6,
    kAdvanceFieldNumber = 7,
  };
  // optional bytes bitmap = 2;
  bool has_bitmap() const;
  void clear_bitmap() ;
  const std::string& bitmap() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_bitmap(Arg_&& arg, Args_... args);
  std::string* mutable_bitmap();
  PROTOBUF_NODISCARD std::string* release_bitmap();
  void set_allocated_bitmap(std::string* value);

  private:
  const std::string& _internal_bitmap() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_bitmap(
      const std::string& value);
  std::string* _internal_mutable_bitmap();

  public:
  // required uint32 id = 1;
  bool has_id() const;
  void clear_id() ;
  ::uint32_t id() const;
  void set_id(::uint32_t value);

  private:
  ::uint32_t _internal_id() const;
  void _internal_set_id(::uint32_t value);

  public:
  // required uint32 width = 3;
  bool has_width() const;
  void clear_width() ;
  ::uint32_t width() const;
  void set_width(::uint32_t value);

  private:
  ::uint32_t _internal_width() const;
  void _internal_set_width(::uint32_t value);

  public:
  // required uint32 height = 4;
  bool has_height() const;
  void clear_height() ;
  ::uint32_t height() const;
  void set_height(::uint32_t value);

  private:
  ::uint32_t _internal_height() const;
  void _internal_set_height(::uint32_t value);

  public:
  // required sint32 left = 5;
  bool has_left() const;
  void clear_left() ;
  ::int32_t left() const;
  void set_left(::int32_t value);

  private:
  ::int32_t _internal_left() const;
  void _internal_set_left(::int32_t value);

  public:
  // required sint32 top = 6;
  bool has_top() const;
  void clear_top() ;
  ::int32_t top() const;
  void set_top(::int32_t value);

  private:
  ::int32_t _internal_top() const;
  void _internal_set_top(::int32_t value);

  public:
  // required uint32 advance = 7;
  bool has_advance() const;
  void clear_advance() ;
  ::uint32_t advance() const;
  void set_advance(::uint32_t value);

  private:
  ::uint32_t _internal_advance() const;
  void _internal_set_advance(::uint32_t value);

  public:
  // @@protoc_insertion_point(class_scope:mapboxgl.glyphs.glyph)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      3, 7, 0,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const glyph& from_msg);
    ::google::protobuf::internal::HasBits<1> _has_bits_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    ::google::protobuf::internal::ArenaStringPtr bitmap_;
    ::uint32_t id_;
    ::uint32_t width_;
    ::uint32_t height_;
    ::int32_t left_;
    ::int32_t top_;
    ::uint32_t advance_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_glyphs_2eproto;
};
// -------------------------------------------------------------------

class fontstack final : public ::google::protobuf::MessageLite
/* @@protoc_insertion_point(class_definition:mapboxgl.glyphs.fontstack) */ {
 public:
  inline fontstack() : fontstack(nullptr) {}
  ~fontstack() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(fontstack* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(fontstack));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR fontstack(
      ::google::protobuf::internal::ConstantInitialized);

  inline fontstack(const fontstack& from) : fontstack(nullptr, from) {}
  inline fontstack(fontstack&& from) noexcept
      : fontstack(nullptr, std::move(from)) {}
  inline fontstack& operator=(const fontstack& from) {
    CopyFrom(from);
    return *this;
  }
  inline fontstack& operator=(fontstack&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const fontstack& default_instance() {
    return *internal_default_instance();
  }
  static inline const fontstack* internal_default_instance() {
    return reinterpret_cast<const fontstack*>(
        &_fontstack_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 1;
  friend void swap(fontstack& a, fontstack& b) { a.Swap(&b); }
  inline void Swap(fontstack* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(fontstack* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  fontstack* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::MessageLite::DefaultConstruct<fontstack>(arena);
  }
  void CopyFrom(const fontstack& from);
  void MergeFrom(const fontstack& from) { fontstack::MergeImpl(*this, from); }

  private:
  static void MergeImpl(::google::protobuf::MessageLite& to_msg,
                        const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return IsInitializedImpl(*this);
  }

  private:
  static bool IsInitializedImpl(const MessageLite& msg);

  public:
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(fontstack* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "mapboxgl.glyphs.fontstack"; }

 protected:
  explicit fontstack(::google::protobuf::Arena* arena);
  fontstack(::google::protobuf::Arena* arena, const fontstack& from);
  fontstack(::google::protobuf::Arena* arena, fontstack&& from) noexcept
      : fontstack(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataLite<26> _class_data_;

 public:
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kGlyphsFieldNumber = 3,
    kNameFieldNumber = 1,
    kRangeFieldNumber = 2,
  };
  // repeated .mapboxgl.glyphs.glyph glyphs = 3;
  int glyphs_size() const;
  private:
  int _internal_glyphs_size() const;

  public:
  void clear_glyphs() ;
  ::mapboxgl::glyphs::glyph* mutable_glyphs(int index);
  ::google::protobuf::RepeatedPtrField<::mapboxgl::glyphs::glyph>* mutable_glyphs();

  private:
  const ::google::protobuf::RepeatedPtrField<::mapboxgl::glyphs::glyph>& _internal_glyphs() const;
  ::google::protobuf::RepeatedPtrField<::mapboxgl::glyphs::glyph>* _internal_mutable_glyphs();
  public:
  const ::mapboxgl::glyphs::glyph& glyphs(int index) const;
  ::mapboxgl::glyphs::glyph* add_glyphs();
  const ::google::protobuf::RepeatedPtrField<::mapboxgl::glyphs::glyph>& glyphs() const;
  // required string name = 1;
  bool has_name() const;
  void clear_name() ;
  const std::string& name() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_name(Arg_&& arg, Args_... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* value);

  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(
      const std::string& value);
  std::string* _internal_mutable_name();

  public:
  // required string range = 2;
  bool has_range() const;
  void clear_range() ;
  const std::string& range() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_range(Arg_&& arg, Args_... args);
  std::string* mutable_range();
  PROTOBUF_NODISCARD std::string* release_range();
  void set_allocated_range(std::string* value);

  private:
  const std::string& _internal_range() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_range(
      const std::string& value);
  std::string* _internal_mutable_range();

  public:
  // @@protoc_insertion_point(class_scope:mapboxgl.glyphs.fontstack)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      2, 3, 1,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const fontstack& from_msg);
    ::google::protobuf::internal::HasBits<1> _has_bits_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    ::google::protobuf::RepeatedPtrField< ::mapboxgl::glyphs::glyph > glyphs_;
    ::google::protobuf::internal::ArenaStringPtr name_;
    ::google::protobuf::internal::ArenaStringPtr range_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_glyphs_2eproto;
};
// -------------------------------------------------------------------

class glyphs final : public ::google::protobuf::MessageLite
/* @@protoc_insertion_point(class_definition:mapboxgl.glyphs.glyphs) */ {
 public:
  inline glyphs() : glyphs(nullptr) {}
  ~glyphs() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(glyphs* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(glyphs));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR glyphs(
      ::google::protobuf::internal::ConstantInitialized);

  inline glyphs(const glyphs& from) : glyphs(nullptr, from) {}
  inline glyphs(glyphs&& from) noexcept
      : glyphs(nullptr, std::move(from)) {}
  inline glyphs& operator=(const glyphs& from) {
    CopyFrom(from);
    return *this;
  }
  inline glyphs& operator=(glyphs&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const glyphs& default_instance() {
    return *internal_default_instance();
  }
  static inline const glyphs* internal_default_instance() {
    return reinterpret_cast<const glyphs*>(
        &_glyphs_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 2;
  friend void swap(glyphs& a, glyphs& b) { a.Swap(&b); }
  inline void Swap(glyphs* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(glyphs* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  glyphs* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::MessageLite::DefaultConstruct<glyphs>(arena);
  }
  void CopyFrom(const glyphs& from);
  void MergeFrom(const glyphs& from) { glyphs::MergeImpl(*this, from); }

  private:
  static void MergeImpl(::google::protobuf::MessageLite& to_msg,
                        const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return IsInitializedImpl(*this);
  }

  private:
  static bool IsInitializedImpl(const MessageLite& msg);

  public:
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(glyphs* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "mapboxgl.glyphs.glyphs"; }

 protected:
  explicit glyphs(::google::protobuf::Arena* arena);
  glyphs(::google::protobuf::Arena* arena, const glyphs& from);
  glyphs(::google::protobuf::Arena* arena, glyphs&& from) noexcept
      : glyphs(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataLite<23> _class_data_;

 public:
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kStacksFieldNumber = 1,
  };
  // repeated .mapboxgl.glyphs.fontstack stacks = 1;
  int stacks_size() const;
  private:
  int _internal_stacks_size() const;

  public:
  void clear_stacks() ;
  ::mapboxgl::glyphs::fontstack* mutable_stacks(int index);
  ::google::protobuf::RepeatedPtrField<::mapboxgl::glyphs::fontstack>* mutable_stacks();

  private:
  const ::google::protobuf::RepeatedPtrField<::mapboxgl::glyphs::fontstack>& _internal_stacks() const;
  ::google::protobuf::RepeatedPtrField<::mapboxgl::glyphs::fontstack>* _internal_mutable_stacks();
  public:
  const ::mapboxgl::glyphs::fontstack& stacks(int index) const;
  ::mapboxgl::glyphs::fontstack* add_stacks();
  const ::google::protobuf::RepeatedPtrField<::mapboxgl::glyphs::fontstack>& stacks() const;
  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed,
            typename = typename _proto_TypeTraits::Singular>
  inline bool HasExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<glyphs, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) const {
    return _impl_._extensions_.Has(id.number());
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline void ClearExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<glyphs, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) {
    _impl_._extensions_.ClearExtension(id.number());
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed,
            typename = typename _proto_TypeTraits::Repeated>
  inline int ExtensionSize(
      const ::google::protobuf::internal::ExtensionIdentifier<glyphs, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) const {
    return _impl_._extensions_.ExtensionSize(id.number());
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed,
            std::enable_if_t<!_proto_TypeTraits::kLifetimeBound, int> = 0>
  inline typename _proto_TypeTraits::Singular::ConstType GetExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<glyphs, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) const {
    return _proto_TypeTraits::Get(id.number(), _impl_._extensions_, id.default_value());
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed,
            std::enable_if_t<_proto_TypeTraits::kLifetimeBound, int> = 0>
  inline typename _proto_TypeTraits::Singular::ConstType GetExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<glyphs, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _proto_TypeTraits::Get(id.number(), _impl_._extensions_, id.default_value());
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Singular::MutableType MutableExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<glyphs, _proto_TypeTraits,
                                       _field_type, _is_packed>& id)
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _proto_TypeTraits::Mutable(id.number(), _field_type, &_impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline void SetExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<glyphs, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      typename _proto_TypeTraits::Singular::ConstType value) {
    _proto_TypeTraits::Set(id.number(), _field_type, value, &_impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline void SetAllocatedExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<glyphs, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      typename _proto_TypeTraits::Singular::MutableType value) {
    _proto_TypeTraits::SetAllocated(id.number(), _field_type, value,
                                    &_impl_._extensions_);
  }
  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline void UnsafeArenaSetAllocatedExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<glyphs, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      typename _proto_TypeTraits::Singular::MutableType value) {
    _proto_TypeTraits::UnsafeArenaSetAllocated(id.number(), _field_type,
                                               value, &_impl_._extensions_);
  }
  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  PROTOBUF_NODISCARD inline
      typename _proto_TypeTraits::Singular::MutableType
      ReleaseExtension(
          const ::google::protobuf::internal::ExtensionIdentifier<glyphs, _proto_TypeTraits,
                                           _field_type, _is_packed>& id) {
    return _proto_TypeTraits::Release(id.number(), _field_type, &_impl_._extensions_);
  }
  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Singular::MutableType
  UnsafeArenaReleaseExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<glyphs, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) {
    return _proto_TypeTraits::UnsafeArenaRelease(id.number(), _field_type,
                                                 &_impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed,
            std::enable_if_t<!_proto_TypeTraits::kLifetimeBound, int> = 0>
  inline typename _proto_TypeTraits::Repeated::ConstType GetExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<glyphs, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      int index) const {
    return _proto_TypeTraits::Get(id.number(), _impl_._extensions_, index);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed,
            std::enable_if_t<_proto_TypeTraits::kLifetimeBound, int> = 0>
  inline typename _proto_TypeTraits::Repeated::ConstType GetExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<glyphs, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      int index) const ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _proto_TypeTraits::Get(id.number(), _impl_._extensions_, index);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Repeated::MutableType MutableExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<glyphs, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      int index) ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _proto_TypeTraits::Mutable(id.number(), index, &_impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline void SetExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<glyphs, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      int index, typename _proto_TypeTraits::Repeated::ConstType value) {
    _proto_TypeTraits::Set(id.number(), index, value, &_impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Repeated::MutableType AddExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<glyphs, _proto_TypeTraits,
                                       _field_type, _is_packed>& id)
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    typename _proto_TypeTraits::Repeated::MutableType to_add =
        _proto_TypeTraits::Add(id.number(), _field_type, &_impl_._extensions_);
    return to_add;
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline void AddExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<glyphs, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      typename _proto_TypeTraits::Repeated::ConstType value) {
    _proto_TypeTraits::Add(id.number(), _field_type, _is_packed, value,
                           &_impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline const typename _proto_TypeTraits::Repeated::RepeatedFieldType&
  GetRepeatedExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<glyphs, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _proto_TypeTraits::GetRepeated(id.number(), _impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Repeated::RepeatedFieldType*
  MutableRepeatedExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<glyphs, _proto_TypeTraits,
                                       _field_type, _is_packed>& id)
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _proto_TypeTraits::MutableRepeated(id.number(), _field_type,
                                              _is_packed, &_impl_._extensions_);
  }
  // @@protoc_insertion_point(class_scope:mapboxgl.glyphs.glyphs)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      0, 1, 1,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const glyphs& from_msg);
    ::google::protobuf::internal::ExtensionSet _extensions_;
    ::google::protobuf::RepeatedPtrField< ::mapboxgl::glyphs::fontstack > stacks_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_glyphs_2eproto;
};

// ===================================================================




// ===================================================================


#ifdef __GNUC__
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// -------------------------------------------------------------------

// glyph

// required uint32 id = 1;
inline bool glyph::has_id() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline void glyph::clear_id() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.id_ = 0u;
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline ::uint32_t glyph::id() const {
  // @@protoc_insertion_point(field_get:mapboxgl.glyphs.glyph.id)
  return _internal_id();
}
inline void glyph::set_id(::uint32_t value) {
  _internal_set_id(value);
  _impl_._has_bits_[0] |= 0x00000002u;
  // @@protoc_insertion_point(field_set:mapboxgl.glyphs.glyph.id)
}
inline ::uint32_t glyph::_internal_id() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.id_;
}
inline void glyph::_internal_set_id(::uint32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.id_ = value;
}

// optional bytes bitmap = 2;
inline bool glyph::has_bitmap() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline void glyph::clear_bitmap() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.bitmap_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const std::string& glyph::bitmap() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:mapboxgl.glyphs.glyph.bitmap)
  return _internal_bitmap();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void glyph::set_bitmap(Arg_&& arg,
                                                     Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.bitmap_.SetBytes(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:mapboxgl.glyphs.glyph.bitmap)
}
inline std::string* glyph::mutable_bitmap() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_bitmap();
  // @@protoc_insertion_point(field_mutable:mapboxgl.glyphs.glyph.bitmap)
  return _s;
}
inline const std::string& glyph::_internal_bitmap() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.bitmap_.Get();
}
inline void glyph::_internal_set_bitmap(const std::string& value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.bitmap_.Set(value, GetArena());
}
inline std::string* glyph::_internal_mutable_bitmap() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_._has_bits_[0] |= 0x00000001u;
  return _impl_.bitmap_.Mutable( GetArena());
}
inline std::string* glyph::release_bitmap() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:mapboxgl.glyphs.glyph.bitmap)
  if ((_impl_._has_bits_[0] & 0x00000001u) == 0) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000001u;
  auto* released = _impl_.bitmap_.Release();
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString()) {
    _impl_.bitmap_.Set("", GetArena());
  }
  return released;
}
inline void glyph::set_allocated_bitmap(std::string* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  _impl_.bitmap_.SetAllocated(value, GetArena());
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString() && _impl_.bitmap_.IsDefault()) {
    _impl_.bitmap_.Set("", GetArena());
  }
  // @@protoc_insertion_point(field_set_allocated:mapboxgl.glyphs.glyph.bitmap)
}

// required uint32 width = 3;
inline bool glyph::has_width() const {
  bool value = (_impl_._has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline void glyph::clear_width() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.width_ = 0u;
  _impl_._has_bits_[0] &= ~0x00000004u;
}
inline ::uint32_t glyph::width() const {
  // @@protoc_insertion_point(field_get:mapboxgl.glyphs.glyph.width)
  return _internal_width();
}
inline void glyph::set_width(::uint32_t value) {
  _internal_set_width(value);
  _impl_._has_bits_[0] |= 0x00000004u;
  // @@protoc_insertion_point(field_set:mapboxgl.glyphs.glyph.width)
}
inline ::uint32_t glyph::_internal_width() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.width_;
}
inline void glyph::_internal_set_width(::uint32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.width_ = value;
}

// required uint32 height = 4;
inline bool glyph::has_height() const {
  bool value = (_impl_._has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline void glyph::clear_height() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.height_ = 0u;
  _impl_._has_bits_[0] &= ~0x00000008u;
}
inline ::uint32_t glyph::height() const {
  // @@protoc_insertion_point(field_get:mapboxgl.glyphs.glyph.height)
  return _internal_height();
}
inline void glyph::set_height(::uint32_t value) {
  _internal_set_height(value);
  _impl_._has_bits_[0] |= 0x00000008u;
  // @@protoc_insertion_point(field_set:mapboxgl.glyphs.glyph.height)
}
inline ::uint32_t glyph::_internal_height() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.height_;
}
inline void glyph::_internal_set_height(::uint32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.height_ = value;
}

// required sint32 left = 5;
inline bool glyph::has_left() const {
  bool value = (_impl_._has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline void glyph::clear_left() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.left_ = 0;
  _impl_._has_bits_[0] &= ~0x00000010u;
}
inline ::int32_t glyph::left() const {
  // @@protoc_insertion_point(field_get:mapboxgl.glyphs.glyph.left)
  return _internal_left();
}
inline void glyph::set_left(::int32_t value) {
  _internal_set_left(value);
  _impl_._has_bits_[0] |= 0x00000010u;
  // @@protoc_insertion_point(field_set:mapboxgl.glyphs.glyph.left)
}
inline ::int32_t glyph::_internal_left() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.left_;
}
inline void glyph::_internal_set_left(::int32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.left_ = value;
}

// required sint32 top = 6;
inline bool glyph::has_top() const {
  bool value = (_impl_._has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline void glyph::clear_top() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.top_ = 0;
  _impl_._has_bits_[0] &= ~0x00000020u;
}
inline ::int32_t glyph::top() const {
  // @@protoc_insertion_point(field_get:mapboxgl.glyphs.glyph.top)
  return _internal_top();
}
inline void glyph::set_top(::int32_t value) {
  _internal_set_top(value);
  _impl_._has_bits_[0] |= 0x00000020u;
  // @@protoc_insertion_point(field_set:mapboxgl.glyphs.glyph.top)
}
inline ::int32_t glyph::_internal_top() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.top_;
}
inline void glyph::_internal_set_top(::int32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.top_ = value;
}

// required uint32 advance = 7;
inline bool glyph::has_advance() const {
  bool value = (_impl_._has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline void glyph::clear_advance() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.advance_ = 0u;
  _impl_._has_bits_[0] &= ~0x00000040u;
}
inline ::uint32_t glyph::advance() const {
  // @@protoc_insertion_point(field_get:mapboxgl.glyphs.glyph.advance)
  return _internal_advance();
}
inline void glyph::set_advance(::uint32_t value) {
  _internal_set_advance(value);
  _impl_._has_bits_[0] |= 0x00000040u;
  // @@protoc_insertion_point(field_set:mapboxgl.glyphs.glyph.advance)
}
inline ::uint32_t glyph::_internal_advance() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.advance_;
}
inline void glyph::_internal_set_advance(::uint32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.advance_ = value;
}

// -------------------------------------------------------------------

// fontstack

// required string name = 1;
inline bool fontstack::has_name() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline void fontstack::clear_name() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.name_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const std::string& fontstack::name() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:mapboxgl.glyphs.fontstack.name)
  return _internal_name();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void fontstack::set_name(Arg_&& arg,
                                                     Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.name_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:mapboxgl.glyphs.fontstack.name)
}
inline std::string* fontstack::mutable_name() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:mapboxgl.glyphs.fontstack.name)
  return _s;
}
inline const std::string& fontstack::_internal_name() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.name_.Get();
}
inline void fontstack::_internal_set_name(const std::string& value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.name_.Set(value, GetArena());
}
inline std::string* fontstack::_internal_mutable_name() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_._has_bits_[0] |= 0x00000001u;
  return _impl_.name_.Mutable( GetArena());
}
inline std::string* fontstack::release_name() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:mapboxgl.glyphs.fontstack.name)
  if ((_impl_._has_bits_[0] & 0x00000001u) == 0) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000001u;
  auto* released = _impl_.name_.Release();
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString()) {
    _impl_.name_.Set("", GetArena());
  }
  return released;
}
inline void fontstack::set_allocated_name(std::string* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  _impl_.name_.SetAllocated(value, GetArena());
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString() && _impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArena());
  }
  // @@protoc_insertion_point(field_set_allocated:mapboxgl.glyphs.fontstack.name)
}

// required string range = 2;
inline bool fontstack::has_range() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline void fontstack::clear_range() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.range_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline const std::string& fontstack::range() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:mapboxgl.glyphs.fontstack.range)
  return _internal_range();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void fontstack::set_range(Arg_&& arg,
                                                     Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_._has_bits_[0] |= 0x00000002u;
  _impl_.range_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:mapboxgl.glyphs.fontstack.range)
}
inline std::string* fontstack::mutable_range() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_range();
  // @@protoc_insertion_point(field_mutable:mapboxgl.glyphs.fontstack.range)
  return _s;
}
inline const std::string& fontstack::_internal_range() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.range_.Get();
}
inline void fontstack::_internal_set_range(const std::string& value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_._has_bits_[0] |= 0x00000002u;
  _impl_.range_.Set(value, GetArena());
}
inline std::string* fontstack::_internal_mutable_range() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_._has_bits_[0] |= 0x00000002u;
  return _impl_.range_.Mutable( GetArena());
}
inline std::string* fontstack::release_range() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:mapboxgl.glyphs.fontstack.range)
  if ((_impl_._has_bits_[0] & 0x00000002u) == 0) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000002u;
  auto* released = _impl_.range_.Release();
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString()) {
    _impl_.range_.Set("", GetArena());
  }
  return released;
}
inline void fontstack::set_allocated_range(std::string* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }
  _impl_.range_.SetAllocated(value, GetArena());
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString() && _impl_.range_.IsDefault()) {
    _impl_.range_.Set("", GetArena());
  }
  // @@protoc_insertion_point(field_set_allocated:mapboxgl.glyphs.fontstack.range)
}

// repeated .mapboxgl.glyphs.glyph glyphs = 3;
inline int fontstack::_internal_glyphs_size() const {
  return _internal_glyphs().size();
}
inline int fontstack::glyphs_size() const {
  return _internal_glyphs_size();
}
inline void fontstack::clear_glyphs() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.glyphs_.Clear();
}
inline ::mapboxgl::glyphs::glyph* fontstack::mutable_glyphs(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:mapboxgl.glyphs.fontstack.glyphs)
  return _internal_mutable_glyphs()->Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField<::mapboxgl::glyphs::glyph>* fontstack::mutable_glyphs()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:mapboxgl.glyphs.fontstack.glyphs)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _internal_mutable_glyphs();
}
inline const ::mapboxgl::glyphs::glyph& fontstack::glyphs(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:mapboxgl.glyphs.fontstack.glyphs)
  return _internal_glyphs().Get(index);
}
inline ::mapboxgl::glyphs::glyph* fontstack::add_glyphs() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::mapboxgl::glyphs::glyph* _add = _internal_mutable_glyphs()->Add();
  // @@protoc_insertion_point(field_add:mapboxgl.glyphs.fontstack.glyphs)
  return _add;
}
inline const ::google::protobuf::RepeatedPtrField<::mapboxgl::glyphs::glyph>& fontstack::glyphs() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:mapboxgl.glyphs.fontstack.glyphs)
  return _internal_glyphs();
}
inline const ::google::protobuf::RepeatedPtrField<::mapboxgl::glyphs::glyph>&
fontstack::_internal_glyphs() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.glyphs_;
}
inline ::google::protobuf::RepeatedPtrField<::mapboxgl::glyphs::glyph>*
fontstack::_internal_mutable_glyphs() {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return &_impl_.glyphs_;
}

// -------------------------------------------------------------------

// glyphs

// repeated .mapboxgl.glyphs.fontstack stacks = 1;
inline int glyphs::_internal_stacks_size() const {
  return _internal_stacks().size();
}
inline int glyphs::stacks_size() const {
  return _internal_stacks_size();
}
inline void glyphs::clear_stacks() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.stacks_.Clear();
}
inline ::mapboxgl::glyphs::fontstack* glyphs::mutable_stacks(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:mapboxgl.glyphs.glyphs.stacks)
  return _internal_mutable_stacks()->Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField<::mapboxgl::glyphs::fontstack>* glyphs::mutable_stacks()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:mapboxgl.glyphs.glyphs.stacks)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _internal_mutable_stacks();
}
inline const ::mapboxgl::glyphs::fontstack& glyphs::stacks(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:mapboxgl.glyphs.glyphs.stacks)
  return _internal_stacks().Get(index);
}
inline ::mapboxgl::glyphs::fontstack* glyphs::add_stacks() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::mapboxgl::glyphs::fontstack* _add = _internal_mutable_stacks()->Add();
  // @@protoc_insertion_point(field_add:mapboxgl.glyphs.glyphs.stacks)
  return _add;
}
inline const ::google::protobuf::RepeatedPtrField<::mapboxgl::glyphs::fontstack>& glyphs::stacks() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:mapboxgl.glyphs.glyphs.stacks)
  return _internal_stacks();
}
inline const ::google::protobuf::RepeatedPtrField<::mapboxgl::glyphs::fontstack>&
glyphs::_internal_stacks() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.stacks_;
}
inline ::google::protobuf::RepeatedPtrField<::mapboxgl::glyphs::fontstack>*
glyphs::_internal_mutable_stacks() {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return &_impl_.stacks_;
}

#ifdef __GNUC__
#pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)
}  // namespace glyphs
}  // namespace mapboxgl


// @@protoc_insertion_point(global_scope)

#include "google/protobuf/port_undef.inc"

#endif  // glyphs_2eproto_2epb_2eh
