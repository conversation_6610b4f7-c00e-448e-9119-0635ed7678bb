{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/giflib-x64-windows-5.2.2#2-4f14d146-7faa-4a11-bf3c-2690200d6ea0", "name": "giflib:x64-windows@5.2.2#2 1957d789238348081a1a5d4a9256057c245788464f2c2a6b87c41f8dfb7abe56", "creationInfo": {"creators": ["Tool: vcpkg-2025-06-20-ef7c0d541124bbdd334a03467e7edb6c3364d199"], "created": "2025-07-03T02:35:41Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-2"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-3"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-4"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-5"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-6"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-3", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-4", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-5", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-6", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-6", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "gif<PERSON>b", "SPDXID": "SPDXRef-port", "versionInfo": "5.2.2#2", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/giflib", "homepage": "https://sourceforge.net/projects/giflib/", "licenseConcluded": "MIT", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "A library for reading and writing gif images.", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "giflib:x64-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "1957d789238348081a1a5d4a9256057c245788464f2c2a6b87c41f8dfb7abe56", "downloadLocation": "NONE", "licenseConcluded": "MIT", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "giflib-5.2.2.tar.gz", "packageFileName": "giflib-5.2.2.tar.gz", "downloadLocation": "https://sourceforge.net/projects/giflib/files//giflib-5.2.2.tar.gz", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "0865ab2b1904fa14640c655fdb14bb54244ad18a66e358565c00287875d00912343f9be8bfac7658cc0146200d626f7ec9160d7a339f20ba3be6b9941d73975f"}]}], "files": [{"fileName": "./CMakeLists.txt", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "0966993582b486b896db04fb99404d2f6100fe538bccfdce2b6fd013454706df"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./exports.def", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "c6c3dee34dde15b1474e937b4f70ed7ca67847a826af4db749a8c9f414fae2b1"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./msvc.diff", "SPDXID": "SPDXRef-file-2", "checksums": [{"algorithm": "SHA256", "checksumValue": "494165202112a8158c2e4fce46d092ec8bca41dd96fed3f064071f6675aa5ec2"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-3", "checksums": [{"algorithm": "SHA256", "checksumValue": "bb768bdf0d1040de0bf0fe3742f74aee00b5d244533f8b23cf213cf05362270a"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./usage", "SPDXID": "SPDXRef-file-4", "checksums": [{"algorithm": "SHA256", "checksumValue": "76da1f15431142f10b07577a061a3028ad2e07090258533c24a59a8965e72abd"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg-cmake-wrapper.cmake", "SPDXID": "SPDXRef-file-5", "checksums": [{"algorithm": "SHA256", "checksumValue": "35c26f064da36e0e2da823d27249200b913196ee70a5f2559c9b00598d527a4e"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-6", "checksums": [{"algorithm": "SHA256", "checksumValue": "25c0bf45d9dbf08adc169743f4759e712a022b1dbf67d3fb3b8b5afae2a96767"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}