/* osgEarth
* Copyright 2025 Pelican Mapping
* MIT License
*/

#ifndef OSGEARTHFEATURES_TESSELLATE_OPERATOR_H
#define OSGEARTHFEATURES_TESSELLATE_OPERATOR_H 1

#include <osgEarth/Common>
#include <osgEarth/Feature>
#include <osgEarth/FilterContext>

namespace osgEarth { namespace Util
{
    /**
     * Tessellates linear data in a feature geometry by subdividing each
     * line segment a specified # of times.
     */
    class OSGEARTH_EXPORT TessellateOperator
    {
    public: // static methods

        /**
         * Tessellates a straight line into multiple segments.
         */
        static void tessellateLinear( 
            const osg::Vec3d&        p0, 
            const osg::Vec3d&        p1, 
            unsigned                 parts, 
            std::vector<osg::Vec3d>& out );

        /**
         * Tessellates a geographic line (great circle or rhumb) between
         * two geographic points (long,lat,alt) into multiple segments along
         * a spheroid.
         */
        static void tessellateGeo( 
            const osg::Vec3d&        p0, 
            const osg::Vec3d&        p1, 
            unsigned                 parts, 
            GeoInterpolation         interp,
            std::vector<osg::Vec3d>& out );

    public:
        /**
         * Constructs a new tessellation operator.
         */
        TessellateOperator();

        virtual ~TessellateOperator() { }

        /**
         * Sets the maximum size of each tessellated partition in the geometry.
         * The "numPartitions" is calculated from this value.
         */
        void setMaxPartitionSize( const Distance& value ) { _maxDistance = value; }

        void setNumPartitions( unsigned value ) { _numPartitions = value; }

        void setDefaultGeoInterp( GeoInterpolation value ) { _defaultInterp = value; }

    public:
        /**
         * Operate on a feature
         */
        void operator()( Feature* feature, FilterContext& context ) const;

        FilterContext push(FeatureList& input, FilterContext& context) const;

    protected:
        optional<Distance> _maxDistance;
        unsigned           _numPartitions;
        GeoInterpolation   _defaultInterp;
    };

} }

#endif // OSGEARTHFEATURES_TESSELLATE_OPERATOR_H
