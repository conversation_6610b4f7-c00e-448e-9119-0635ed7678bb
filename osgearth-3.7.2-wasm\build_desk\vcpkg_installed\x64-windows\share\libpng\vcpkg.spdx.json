{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/libpng-x64-windows-1.6.48-c0f9ab96-2adf-4455-9cf7-b913dc9eb069", "name": "libpng:x64-windows@1.6.48 eb802785482d290c987b807233ec7a89a4cc2feafc8898f132e9673a07be073d", "creationInfo": {"creators": ["Tool: vcpkg-2025-06-20-ef7c0d541124bbdd334a03467e7edb6c3364d199"], "created": "2025-07-02T08:16:00Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-2"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-3"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-4"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-5"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-3", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-4", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-5", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-5", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "libpng", "SPDXID": "SPDXRef-port", "versionInfo": "1.6.48", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/libpng", "homepage": "https://github.com/pnggroup/libpng", "licenseConcluded": "libpng-2.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "libpng is a library implementing an interface for reading and writing PNG (Portable Network Graphics) format files", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "libpng:x64-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "eb802785482d290c987b807233ec7a89a4cc2feafc8898f132e9673a07be073d", "downloadLocation": "NONE", "licenseConcluded": "libpng-2.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "pnggroup/libpng", "downloadLocation": "git+https://github.com/pnggroup/libpng@v1.6.48", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "3a0006256abc6f23f5be1d67b201303ceaaa58ffa901f4659ad95f025b08f5e1c30d374cc251196a2ff1ee3ef4b37bd4d61c7779eabd86922d3bdd047264d9c1"}]}, {"SPDXID": "SPDXRef-resource-1", "name": "${LIBPNG_APNG_PATCH_NAME}.gz", "packageFileName": "${LIBPNG_APNG_PATCH_NAME}.gz", "downloadLocation": "https://downloads.sourceforge.net/project/libpng-apng/libpng16/1.6.48/${LIBPNG_APNG_PATCH_NAME}.gz", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "957810c235647bceaacc9754dcb25fdd36177f0f8255ed3eef862d681a53e80e2fc461f8dc083da4a07728b14cf9d2941286c1d745acc9fb131ef767630532f3"}]}], "files": [{"fileName": "./cmake.patch", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "3a26a165e8bcd541d81722023c8574bf6100045b34fcde8cf990f04cc61f108f"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./libpng-config.cmake", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "24086e8ff194402a0f2d68e9f992ca7a6899a194883a9df0054818e42d41c246"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-2", "checksums": [{"algorithm": "SHA256", "checksumValue": "24d9ff727938e2fd27e4ce55ddbb1f528f5be7e9bec455842bb73064c7756f7c"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./usage", "SPDXID": "SPDXRef-file-3", "checksums": [{"algorithm": "SHA256", "checksumValue": "6fd48b8b809e924a7d632e656853c67b391b9dcfbb0e27e867254ae0755e363e"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg-cmake-wrapper.cmake", "SPDXID": "SPDXRef-file-4", "checksums": [{"algorithm": "SHA256", "checksumValue": "4267e69abf185f2228a4cc012e3fe87d5de21a187c75950b5a7615f41c3f6371"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-5", "checksums": [{"algorithm": "SHA256", "checksumValue": "0c60d9a97107a5a64788928c33d4e08c6a10bdf87e58a75039ba88e6fed656b8"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}