/*-------------------------------------------------------------------------
 *
 * pg_auth_members_d.h
 *    Macro definitions for pg_auth_members
 *
 * Portions Copyright (c) 1996-2023, PostgreSQL Global Development Group
 * Portions Copyright (c) 1994, Regents of the University of California
 *
 * NOTES
 *  ******************************
 *  *** DO NOT EDIT THIS FILE! ***
 *  ******************************
 *
 *  It has been GENERATED by src/backend/catalog/genbki.pl
 *
 *-------------------------------------------------------------------------
 */
#ifndef PG_AUTH_MEMBERS_D_H
#define PG_AUTH_MEMBERS_D_H

#define AuthMemRelationId 1261
#define AuthMemRelation_Rowtype_Id 2843
#define AuthMemOidIndexId 6303
#define AuthMemRoleMemIndexId 2694
#define AuthMemMemRoleIndexId 2695
#define AuthMemGrantorIndexId 6302

#define Anum_pg_auth_members_oid 1
#define Anum_pg_auth_members_roleid 2
#define Anum_pg_auth_members_member 3
#define Anum_pg_auth_members_grantor 4
#define Anum_pg_auth_members_admin_option 5
#define Anum_pg_auth_members_inherit_option 6
#define Anum_pg_auth_members_set_option 7

#define Natts_pg_auth_members 7


#endif							/* PG_AUTH_MEMBERS_D_H */
