/*-------------------------------------------------------------------------
 *
 * pg_extension_d.h
 *    Macro definitions for pg_extension
 *
 * Portions Copyright (c) 1996-2023, PostgreSQL Global Development Group
 * Portions Copyright (c) 1994, Regents of the University of California
 *
 * NOTES
 *  ******************************
 *  *** DO NOT EDIT THIS FILE! ***
 *  ******************************
 *
 *  It has been GENERATED by src/backend/catalog/genbki.pl
 *
 *-------------------------------------------------------------------------
 */
#ifndef PG_EXTENSION_D_H
#define PG_EXTENSION_D_H

#define ExtensionRelationId 3079
#define ExtensionOidIndexId 3080
#define ExtensionNameIndexId 3081

#define Anum_pg_extension_oid 1
#define Anum_pg_extension_extname 2
#define Anum_pg_extension_extowner 3
#define Anum_pg_extension_extnamespace 4
#define Anum_pg_extension_extrelocatable 5
#define Anum_pg_extension_extversion 6
#define Anum_pg_extension_extconfig 7
#define Anum_pg_extension_extcondition 8

#define Natts_pg_extension 8


#endif							/* PG_EXTENSION_D_H */
