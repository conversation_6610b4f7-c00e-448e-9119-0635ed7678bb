/*-------------------------------------------------------------------------
 *
 * pg_event_trigger_d.h
 *    Macro definitions for pg_event_trigger
 *
 * Portions Copyright (c) 1996-2023, PostgreSQL Global Development Group
 * Portions Copyright (c) 1994, Regents of the University of California
 *
 * NOTES
 *  ******************************
 *  *** DO NOT EDIT THIS FILE! ***
 *  ******************************
 *
 *  It has been GENERATED by src/backend/catalog/genbki.pl
 *
 *-------------------------------------------------------------------------
 */
#ifndef PG_EVENT_TRIGGER_D_H
#define PG_EVENT_TRIGGER_D_H

#define EventTriggerRelationId 3466
#define EventTriggerNameIndexId 3467
#define EventTriggerOidIndexId 3468

#define Anum_pg_event_trigger_oid 1
#define Anum_pg_event_trigger_evtname 2
#define Anum_pg_event_trigger_evtevent 3
#define Anum_pg_event_trigger_evtowner 4
#define Anum_pg_event_trigger_evtfoid 5
#define Anum_pg_event_trigger_evtenabled 6
#define Anum_pg_event_trigger_evttags 7

#define Natts_pg_event_trigger 7


#endif							/* PG_EVENT_TRIGGER_D_H */
