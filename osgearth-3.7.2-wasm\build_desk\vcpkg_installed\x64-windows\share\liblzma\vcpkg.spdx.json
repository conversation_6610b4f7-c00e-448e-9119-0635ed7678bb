{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/liblzma-x64-windows-5.8.1-a3b5d1b1-350c-4daa-8b46-6024de9adfa8", "name": "liblzma:x64-windows@5.8.1 e7f2ac12b18b7696dff8dd54fb3f50093d7f6e7a5846d7d7555ad1a1b8e8e08f", "creationInfo": {"creators": ["Tool: vcpkg-2025-06-20-ef7c0d541124bbdd334a03467e7edb6c3364d199"], "created": "2025-07-02T08:22:08Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-2"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-3"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-4"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-3", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-4", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-4", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "liblzma", "SPDXID": "SPDXRef-port", "versionInfo": "5.8.1", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/liblzma", "homepage": "https://tukaani.org/xz/", "licenseConcluded": "LicenseRef-vcpkg-null", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "Compression library with an API similar to that of zlib.", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "liblzma:x64-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "e7f2ac12b18b7696dff8dd54fb3f50093d7f6e7a5846d7d7555ad1a1b8e8e08f", "downloadLocation": "NONE", "licenseConcluded": "LicenseRef-vcpkg-null", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "tukaani-project/xz", "downloadLocation": "git+https://github.com/tukaani-project/xz@v5.8.1", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "2f51fb316adb2962e0f2ef6ccc8b544cdc45087b9ad26dcd33f2025784be56578ab937c618e5826b2220b49b79b8581dcb8c6d43cd50ded7ad9de9fe61610f46"}]}], "files": [{"fileName": "./build-tools.patch", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "a39d5e1cf991362bd503c9144d2d315d2756d642fdc542580386a1c6b9477df3"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "61340cceaa26537699ceec81bbe12ff042758e074cc85c4e0067af076c97748d"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./usage", "SPDXID": "SPDXRef-file-2", "checksums": [{"algorithm": "SHA256", "checksumValue": "8e856626fcd7a0110736379543122b37484d658078c12a55596b0664f455286d"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg-cmake-wrapper.cmake", "SPDXID": "SPDXRef-file-3", "checksums": [{"algorithm": "SHA256", "checksumValue": "a4611673d837ec6e94f0f0f9a85a1859b85ef169e762b48a99946b9436647b39"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-4", "checksums": [{"algorithm": "SHA256", "checksumValue": "9c30c9c048323d9b8cea63134aa6aef2959a4179579b1e5112848f0fc602ca30"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}