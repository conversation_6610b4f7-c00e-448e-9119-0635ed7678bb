find_path(GIF_INCLUDE_DIR NAMES gif_lib.h PATHS "${_VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/include" NO_DEFAULT_PATH)
find_library(GIF_LIBRARY_RELEASE NAMES gif PATHS "${_VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/lib" NO_DEFAULT_PATH)
find_library(GIF_LIBRARY_DEBUG   NAMES gif PATHS "${_VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/debug/lib" NO_DEFAULT_PATH)
include(SelectLibraryConfigurations)
select_library_configurations(GIF)
set(GIF_LIBRARY "${GIF_LIBRARY}" CACHE STRING "")
unset(GIF_LIBRARY)
unset(GIF_FOUND)
if(NOT TARGET GIF::GIF)
    add_library(GIF::GIF UNKNOWN IMPORTED)
    set_target_properties(GIF::GIF PROPERTIES
        INTERFACE_INCLUDE_DIRECTORIES "${GIF_INCLUDE_DIR}"
        IMPORTED_CONFIGURATIONS RELEASE
        IMPORTED_LINK_INTERFACE_LANGUAGES_RELEASE "C"
        IMPORTED_LOCATION_RELEASE "${GIF_LIBRARY_RELEASE}"
    )
    if(GIF_LIBRARY_DEBUG)
        set_property(TARGET GIF::GIF APPEND PROPERTY
            IMPORTED_CONFIGURATIONS DEBUG)
        set_target_properties(GIF::GIF PROPERTIES
            IMPORTED_LINK_INTERFACE_LANGUAGES_DEBUG "C"
            IMPORTED_LOCATION_DEBUG "${GIF_LIBRARY_DEBUG}")
    endif()
endif()
_find_package(${ARGS})
