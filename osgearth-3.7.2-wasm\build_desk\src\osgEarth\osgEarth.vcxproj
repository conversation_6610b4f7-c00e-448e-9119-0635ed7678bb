﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{88534FC6-C81A-3A31-8BFA-FF6DAD09198E}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <VcpkgEnabled>false</VcpkgEnabled>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>osgEarth</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="do_not_import_user.props" Condition="exists('do_not_import_user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\redist_desk\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">osgEarth.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">osgEarthd</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\redist_desk\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">osgEarth.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">osgEarth</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\redist_desk\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">osgEarth.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">osgEarths</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\redist_desk\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">osgEarth.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">osgEarth</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/vcpkg_installed/x64-windows/include" /external:I "F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/vcpkg_installed/x64-windows/include/geos" /bigobj /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4251;4275</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY_STATIC;WIN32_LEAN_AND_MEAN;NOMINMAX;OSGEARTH_LIBRARY;PROTOBUF_USE_DLLS;ABSL_CONSUME_DLL;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;GEOGRAPHICLIB_SHARED_LIB=1;MESHOPTIMIZER_API=__declspec(dllimport);CMAKE_INTDIR="Debug";osgEarth_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY_STATIC;WIN32_LEAN_AND_MEAN;NOMINMAX;OSGEARTH_LIBRARY;PROTOBUF_USE_DLLS;ABSL_CONSUME_DLL;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;GEOGRAPHICLIB_SHARED_LIB=1;MESHOPTIMIZER_API=__declspec(dllimport);CMAKE_INTDIR=\"Debug\";osgEarth_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include\geos;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include\geos;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/redist_desk/osgEarthd.dll -installedDir F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/vcpkg_installed/x64-windows/debug/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>..\..\vcpkg_installed\x64-windows\debug\lib\osgManipulatord.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgShadowd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgSimd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgViewerd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgGAd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgUtild.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgTextd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgDBd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\OpenThreadsd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\libprotobufd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\spdlogd.lib;..\..\vcpkg_installed\x64-windows\lib\sqlite3.lib;..\..\vcpkg_installed\x64-windows\debug\lib\geos_c.lib;..\..\vcpkg_installed\x64-windows\debug\lib\GeographicLib_d-i.lib;..\..\vcpkg_installed\x64-windows\debug\lib\blend2d.lib;..\..\vcpkg_installed\x64-windows\debug\lib\meshoptimizer.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgManipulatord.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgShadowd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgSimd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgViewerd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgGAd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgUtild.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgTextd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgDBd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\osgd.lib;..\..\vcpkg_installed\x64-windows\debug\lib\OpenThreadsd.lib;opengl32.lib;..\..\vcpkg_installed\x64-windows\debug\lib\libcurl-d.lib;..\..\vcpkg_installed\x64-windows\lib\sqlite3.lib;..\..\vcpkg_installed\x64-windows\debug\lib\abseil_dll.lib;..\..\vcpkg_installed\x64-windows\debug\lib\fmtd.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/redist_desk/osgEarthd.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/redist_desk/osgEarthd.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/vcpkg_installed/x64-windows/include" /external:I "F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/vcpkg_installed/x64-windows/include/geos" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DisableSpecificWarnings>4251;4275</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY_STATIC;WIN32_LEAN_AND_MEAN;NOMINMAX;OSGEARTH_LIBRARY;PROTOBUF_USE_DLLS;ABSL_CONSUME_DLL;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;GEOGRAPHICLIB_SHARED_LIB=1;MESHOPTIMIZER_API=__declspec(dllimport);CMAKE_INTDIR="Release";osgEarth_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY_STATIC;WIN32_LEAN_AND_MEAN;NOMINMAX;OSGEARTH_LIBRARY;PROTOBUF_USE_DLLS;ABSL_CONSUME_DLL;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;GEOGRAPHICLIB_SHARED_LIB=1;MESHOPTIMIZER_API=__declspec(dllimport);CMAKE_INTDIR=\"Release\";osgEarth_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include\geos;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include\geos;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/redist_desk/osgEarth.dll -installedDir F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/vcpkg_installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>..\..\vcpkg_installed\x64-windows\lib\osgManipulator.lib;..\..\vcpkg_installed\x64-windows\lib\osgShadow.lib;..\..\vcpkg_installed\x64-windows\lib\osgSim.lib;..\..\vcpkg_installed\x64-windows\lib\osgViewer.lib;..\..\vcpkg_installed\x64-windows\lib\osgGA.lib;..\..\vcpkg_installed\x64-windows\lib\osgUtil.lib;..\..\vcpkg_installed\x64-windows\lib\osgText.lib;..\..\vcpkg_installed\x64-windows\lib\osgDB.lib;..\..\vcpkg_installed\x64-windows\lib\osg.lib;..\..\vcpkg_installed\x64-windows\lib\OpenThreads.lib;..\..\vcpkg_installed\x64-windows\lib\libprotobuf.lib;..\..\vcpkg_installed\x64-windows\lib\spdlog.lib;..\..\vcpkg_installed\x64-windows\lib\sqlite3.lib;..\..\vcpkg_installed\x64-windows\lib\geos_c.lib;..\..\vcpkg_installed\x64-windows\lib\GeographicLib-i.lib;..\..\vcpkg_installed\x64-windows\lib\blend2d.lib;..\..\vcpkg_installed\x64-windows\lib\meshoptimizer.lib;..\..\vcpkg_installed\x64-windows\lib\osgManipulator.lib;..\..\vcpkg_installed\x64-windows\lib\osgShadow.lib;..\..\vcpkg_installed\x64-windows\lib\osgSim.lib;..\..\vcpkg_installed\x64-windows\lib\osgViewer.lib;..\..\vcpkg_installed\x64-windows\lib\osgGA.lib;..\..\vcpkg_installed\x64-windows\lib\osgUtil.lib;..\..\vcpkg_installed\x64-windows\lib\osgText.lib;..\..\vcpkg_installed\x64-windows\lib\osgDB.lib;..\..\vcpkg_installed\x64-windows\lib\osg.lib;..\..\vcpkg_installed\x64-windows\lib\OpenThreads.lib;opengl32.lib;..\..\vcpkg_installed\x64-windows\lib\libcurl.lib;..\..\vcpkg_installed\x64-windows\lib\sqlite3.lib;..\..\vcpkg_installed\x64-windows\lib\abseil_dll.lib;..\..\vcpkg_installed\x64-windows\lib\fmt.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/redist_desk/osgEarth.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/redist_desk/osgEarth.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/vcpkg_installed/x64-windows/include" /external:I "F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/vcpkg_installed/x64-windows/include/geos" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DisableSpecificWarnings>4251;4275</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY_STATIC;WIN32_LEAN_AND_MEAN;NOMINMAX;OSGEARTH_LIBRARY;PROTOBUF_USE_DLLS;ABSL_CONSUME_DLL;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;GEOGRAPHICLIB_SHARED_LIB=1;MESHOPTIMIZER_API=__declspec(dllimport);CMAKE_INTDIR="MinSizeRel";osgEarth_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY_STATIC;WIN32_LEAN_AND_MEAN;NOMINMAX;OSGEARTH_LIBRARY;PROTOBUF_USE_DLLS;ABSL_CONSUME_DLL;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;GEOGRAPHICLIB_SHARED_LIB=1;MESHOPTIMIZER_API=__declspec(dllimport);CMAKE_INTDIR=\"MinSizeRel\";osgEarth_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include\geos;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include\geos;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/redist_desk/osgEarths.dll -installedDir F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/vcpkg_installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>..\..\vcpkg_installed\x64-windows\lib\osgManipulator.lib;..\..\vcpkg_installed\x64-windows\lib\osgShadow.lib;..\..\vcpkg_installed\x64-windows\lib\osgSim.lib;..\..\vcpkg_installed\x64-windows\lib\osgViewer.lib;..\..\vcpkg_installed\x64-windows\lib\osgGA.lib;..\..\vcpkg_installed\x64-windows\lib\osgUtil.lib;..\..\vcpkg_installed\x64-windows\lib\osgText.lib;..\..\vcpkg_installed\x64-windows\lib\osgDB.lib;..\..\vcpkg_installed\x64-windows\lib\osg.lib;..\..\vcpkg_installed\x64-windows\lib\OpenThreads.lib;..\..\vcpkg_installed\x64-windows\lib\libprotobuf.lib;..\..\vcpkg_installed\x64-windows\lib\spdlog.lib;..\..\vcpkg_installed\x64-windows\lib\sqlite3.lib;..\..\vcpkg_installed\x64-windows\lib\geos_c.lib;..\..\vcpkg_installed\x64-windows\lib\GeographicLib-i.lib;..\..\vcpkg_installed\x64-windows\lib\blend2d.lib;..\..\vcpkg_installed\x64-windows\lib\meshoptimizer.lib;..\..\vcpkg_installed\x64-windows\lib\osgManipulator.lib;..\..\vcpkg_installed\x64-windows\lib\osgShadow.lib;..\..\vcpkg_installed\x64-windows\lib\osgSim.lib;..\..\vcpkg_installed\x64-windows\lib\osgViewer.lib;..\..\vcpkg_installed\x64-windows\lib\osgGA.lib;..\..\vcpkg_installed\x64-windows\lib\osgUtil.lib;..\..\vcpkg_installed\x64-windows\lib\osgText.lib;..\..\vcpkg_installed\x64-windows\lib\osgDB.lib;..\..\vcpkg_installed\x64-windows\lib\osg.lib;..\..\vcpkg_installed\x64-windows\lib\OpenThreads.lib;opengl32.lib;..\..\vcpkg_installed\x64-windows\lib\libcurl.lib;..\..\vcpkg_installed\x64-windows\lib\sqlite3.lib;..\..\vcpkg_installed\x64-windows\lib\abseil_dll.lib;..\..\vcpkg_installed\x64-windows\lib\fmt.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/redist_desk/osgEarths.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/redist_desk/osgEarths.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/vcpkg_installed/x64-windows/include" /external:I "F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/vcpkg_installed/x64-windows/include/geos" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4251;4275</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY_STATIC;WIN32_LEAN_AND_MEAN;NOMINMAX;OSGEARTH_LIBRARY;PROTOBUF_USE_DLLS;ABSL_CONSUME_DLL;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;GEOGRAPHICLIB_SHARED_LIB=1;MESHOPTIMIZER_API=__declspec(dllimport);CMAKE_INTDIR="RelWithDebInfo";osgEarth_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY_STATIC;WIN32_LEAN_AND_MEAN;NOMINMAX;OSGEARTH_LIBRARY;PROTOBUF_USE_DLLS;ABSL_CONSUME_DLL;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;GEOGRAPHICLIB_SHARED_LIB=1;MESHOPTIMIZER_API=__declspec(dllimport);CMAKE_INTDIR=\"RelWithDebInfo\";osgEarth_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include\geos;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include\geos;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/redist_desk/osgEarth.dll -installedDir F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/vcpkg_installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>..\..\vcpkg_installed\x64-windows\lib\osgManipulator.lib;..\..\vcpkg_installed\x64-windows\lib\osgShadow.lib;..\..\vcpkg_installed\x64-windows\lib\osgSim.lib;..\..\vcpkg_installed\x64-windows\lib\osgViewer.lib;..\..\vcpkg_installed\x64-windows\lib\osgGA.lib;..\..\vcpkg_installed\x64-windows\lib\osgUtil.lib;..\..\vcpkg_installed\x64-windows\lib\osgText.lib;..\..\vcpkg_installed\x64-windows\lib\osgDB.lib;..\..\vcpkg_installed\x64-windows\lib\osg.lib;..\..\vcpkg_installed\x64-windows\lib\OpenThreads.lib;..\..\vcpkg_installed\x64-windows\lib\libprotobuf.lib;..\..\vcpkg_installed\x64-windows\lib\spdlog.lib;..\..\vcpkg_installed\x64-windows\lib\sqlite3.lib;..\..\vcpkg_installed\x64-windows\lib\geos_c.lib;..\..\vcpkg_installed\x64-windows\lib\GeographicLib-i.lib;..\..\vcpkg_installed\x64-windows\lib\blend2d.lib;..\..\vcpkg_installed\x64-windows\lib\meshoptimizer.lib;..\..\vcpkg_installed\x64-windows\lib\osgManipulator.lib;..\..\vcpkg_installed\x64-windows\lib\osgShadow.lib;..\..\vcpkg_installed\x64-windows\lib\osgSim.lib;..\..\vcpkg_installed\x64-windows\lib\osgViewer.lib;..\..\vcpkg_installed\x64-windows\lib\osgGA.lib;..\..\vcpkg_installed\x64-windows\lib\osgUtil.lib;..\..\vcpkg_installed\x64-windows\lib\osgText.lib;..\..\vcpkg_installed\x64-windows\lib\osgDB.lib;..\..\vcpkg_installed\x64-windows\lib\osg.lib;..\..\vcpkg_installed\x64-windows\lib\OpenThreads.lib;opengl32.lib;..\..\vcpkg_installed\x64-windows\lib\libcurl.lib;..\..\vcpkg_installed\x64-windows\lib\sqlite3.lib;..\..\vcpkg_installed\x64-windows\lib\abseil_dll.lib;..\..\vcpkg_installed\x64-windows\lib\fmt.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/redist_desk/osgEarth.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/redist_desk/osgEarth.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\4a484bbde562c39e043aa51bdc145c9a\vector_tile.pb.h.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Running cpp protocol buffer compiler on vector_tile.proto</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
..\..\vcpkg_installed\x64-windows\tools\protobuf\protoc.exe --cpp_out :F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/src/osgEarth -I F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/osgEarth F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/osgEarth/vector_tile.proto
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\vector_tile.proto;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\vector_tile.pb.h;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\vector_tile.pb.cc</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Running cpp protocol buffer compiler on vector_tile.proto</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
..\..\vcpkg_installed\x64-windows\tools\protobuf\protoc.exe --cpp_out :F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/src/osgEarth -I F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/osgEarth F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/osgEarth/vector_tile.proto
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\vector_tile.proto;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\vector_tile.pb.h;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\vector_tile.pb.cc</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Running cpp protocol buffer compiler on vector_tile.proto</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
..\..\vcpkg_installed\x64-windows\tools\protobuf\protoc.exe --cpp_out :F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/src/osgEarth -I F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/osgEarth F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/osgEarth/vector_tile.proto
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\vector_tile.proto;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\vector_tile.pb.h;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\vector_tile.pb.cc</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Running cpp protocol buffer compiler on vector_tile.proto</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
..\..\vcpkg_installed\x64-windows\tools\protobuf\protoc.exe --cpp_out :F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/src/osgEarth -I F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/osgEarth F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/osgEarth/vector_tile.proto
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\vector_tile.proto;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\vector_tile.pb.h;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\vector_tile.pb.cc</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\4a484bbde562c39e043aa51bdc145c9a\glyphs.pb.h.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Running cpp protocol buffer compiler on glyphs.proto</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
..\..\vcpkg_installed\x64-windows\tools\protobuf\protoc.exe --cpp_out :F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/src/osgEarth -I F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/osgEarth F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/osgEarth/glyphs.proto
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\glyphs.proto;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\glyphs.pb.h;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\glyphs.pb.cc</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Running cpp protocol buffer compiler on glyphs.proto</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
..\..\vcpkg_installed\x64-windows\tools\protobuf\protoc.exe --cpp_out :F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/src/osgEarth -I F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/osgEarth F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/osgEarth/glyphs.proto
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\glyphs.proto;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\glyphs.pb.h;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\glyphs.pb.cc</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Running cpp protocol buffer compiler on glyphs.proto</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
..\..\vcpkg_installed\x64-windows\tools\protobuf\protoc.exe --cpp_out :F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/src/osgEarth -I F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/osgEarth F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/osgEarth/glyphs.proto
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\glyphs.proto;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\glyphs.pb.h;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\glyphs.pb.cc</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Running cpp protocol buffer compiler on glyphs.proto</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
..\..\vcpkg_installed\x64-windows\tools\protobuf\protoc.exe --cpp_out :F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/src/osgEarth -I F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/osgEarth F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/osgEarth/glyphs.proto
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\glyphs.proto;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\glyphs.pb.h;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\glyphs.pb.cc</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\4a484bbde562c39e043aa51bdc145c9a\AutoGenShaders.cpp.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Generating AutoGenShaders.cpp</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -P F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/src/osgEarth/ConfigureShaders.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CascadeDraping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Chonk.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Chonk.Culling.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DepthOffset.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Draping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DrawInstancedAttribute.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GPUClamping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GPUClamping.lib.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\HexTiling.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Instancing.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LineDrawable.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MetadataNode.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WireLines.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PhongLighting.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PointDrawable.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Text.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Text_legacy.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ContourMap.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeodeticGraticule.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LogDepthBuffer.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LogDepthBuffer.VertOnly.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShadowCaster.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SimpleOceanLayer.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\RTTPicker.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WindLayer.CS.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PBR.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Shaders.cpp.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\ConfigureShaders.cmake.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\AutoGenShaders.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Generating AutoGenShaders.cpp</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -P F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/src/osgEarth/ConfigureShaders.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CascadeDraping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Chonk.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Chonk.Culling.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DepthOffset.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Draping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DrawInstancedAttribute.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GPUClamping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GPUClamping.lib.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\HexTiling.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Instancing.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LineDrawable.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MetadataNode.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WireLines.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PhongLighting.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PointDrawable.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Text.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Text_legacy.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ContourMap.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeodeticGraticule.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LogDepthBuffer.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LogDepthBuffer.VertOnly.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShadowCaster.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SimpleOceanLayer.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\RTTPicker.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WindLayer.CS.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PBR.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Shaders.cpp.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\ConfigureShaders.cmake.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\AutoGenShaders.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Generating AutoGenShaders.cpp</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -P F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/src/osgEarth/ConfigureShaders.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CascadeDraping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Chonk.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Chonk.Culling.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DepthOffset.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Draping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DrawInstancedAttribute.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GPUClamping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GPUClamping.lib.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\HexTiling.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Instancing.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LineDrawable.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MetadataNode.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WireLines.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PhongLighting.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PointDrawable.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Text.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Text_legacy.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ContourMap.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeodeticGraticule.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LogDepthBuffer.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LogDepthBuffer.VertOnly.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShadowCaster.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SimpleOceanLayer.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\RTTPicker.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WindLayer.CS.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PBR.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Shaders.cpp.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\ConfigureShaders.cmake.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\AutoGenShaders.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Generating AutoGenShaders.cpp</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -P F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/src/osgEarth/ConfigureShaders.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CascadeDraping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Chonk.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Chonk.Culling.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DepthOffset.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Draping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DrawInstancedAttribute.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GPUClamping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GPUClamping.lib.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\HexTiling.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Instancing.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LineDrawable.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MetadataNode.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WireLines.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PhongLighting.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PointDrawable.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Text.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Text_legacy.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ContourMap.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeodeticGraticule.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LogDepthBuffer.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LogDepthBuffer.VertOnly.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShadowCaster.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SimpleOceanLayer.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\RTTPicker.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WindLayer.CS.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PBR.glsl;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Shaders.cpp.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\ConfigureShaders.cmake.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\AutoGenShaders.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/osgEarth/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm -BF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/src/osgEarth/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeParseArguments.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindSQLite3.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-generate.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-module.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-options.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\ConfigureShaders.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BuildConfig.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Version.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/osgEarth/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm -BF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/src/osgEarth/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeParseArguments.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindSQLite3.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-generate.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-module.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-options.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\ConfigureShaders.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BuildConfig.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Version.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/osgEarth/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm -BF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/src/osgEarth/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeParseArguments.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindSQLite3.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-generate.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-module.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-options.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\ConfigureShaders.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BuildConfig.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Version.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/osgEarth/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm -BF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/src/osgEarth/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeParseArguments.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindSQLite3.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-generate.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-module.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-options.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\ConfigureShaders.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BuildConfig.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Version.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AGG.h" />
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AltitudeFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AltitudeSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AnnotationData">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AnnotationLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AnnotationNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AnnotationRegistry">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AnnotationSettings">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AnnotationUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ArcGISServer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ArcGISTilePackage">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AtlasBuilder">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AttributesFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AutoClipPlaneHandler">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AutoScaleCallback">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AzureMaps">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BboxDrawable">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BBoxSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BillboardResource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BillboardSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Bing">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Bounds">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BufferFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BuildGeometryFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BuildTextFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Cache">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CacheBin">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CachePolicy">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CacheSeed">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Callbacks">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Callouts">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CameraUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Capabilities">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CascadeDrapingDecorator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CentroidFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CesiumIon">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Chonk">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CircleNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ClampableNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ClampCallback">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Clamping">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ClampingTechnique">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ClipSpace">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ClusterNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Color">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ColorFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Common">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Composite">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CompositeFeatureSource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CompressedArray">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CompositeTiledModelLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Config">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Containers">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ContourMap">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ConvertTypeFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Coverage">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CoverageLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CoverageSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CropFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CssUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Cube">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CullingUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DateTime">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DateTimeRange">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DebugImageLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DecalLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DepthOffset">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Draggers">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DrapeableNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DrapingCullSet">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DrapingTechnique">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DrawInstanced">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\EarthManipulator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ECEF">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Elevation">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ElevationLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ElevationLOD">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ElevationPool">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ElevationQuery">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ElevationRanges">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\EllipseNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Ellipsoid">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Endian">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Ephemeris">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ExampleResources">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Export">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Expression">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Extension">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ExtrudeGeometryFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ExtrusionSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FadeEffect">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Feature">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureCursor">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureDisplayLayout">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureElevationLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureImageLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureImageRTTLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureIndex">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureModelGraph">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureModelLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureModelSource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureRasterizer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureSDFLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureSource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureSourceIndexNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureStyleSorter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FileUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Fill">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Filter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FilterContext">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FilteredFeatureSource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FlatteningLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Formatter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FractalElevationLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FrameClock">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GARSGraticule">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoCommon">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoData">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeodeticGraticule">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeodeticLabelingEngine">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Geoid">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoMath">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Geometry">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeometryClamper">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeometryCloud">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeometryCompiler">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeometryFactory">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeometryRasterizer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeometryUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoPositionNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoPositionNodeAutoScaler">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GEOS">
    </None>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeographicLibAdapter.h" />
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeosFeatureSource">
    </None>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoJSONReader.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShapefileReader.h" />
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoTransform">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GLSLChunker">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GLUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GraticuleLabelingEngine">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\HeightFieldUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Horizon">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\HorizonClipPlane">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\HTM">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\HTTPClient">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\IconResource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\IconSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageMosaic">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageOverlay">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageOverlayEditor">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageToFeatureLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageToHeightFieldConverter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\InstanceBuilder">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\InstanceCloud">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\InstanceResource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\InstanceSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\IntersectionPicker">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\IOTypes">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\JoinLines">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\JsonUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LabelNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LandCover">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LandCoverLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LatLongFormatter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Layer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LayerReference">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LayerShader">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Lighting">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LinearLineOfSight">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LineDrawable">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LineFunctor">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LineOfSight">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LineSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LoadableNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LocalGeometryNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LocalTangentPlane">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Locators">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LODGenerator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LogarithmicDepthBuffer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Map">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MapboxGLGlyphManager">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MapboxGLImageLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MapCallback">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MapModelChange">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MapNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MapNodeObserver">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MaterialLoader">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Math">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MBTiles">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MeasureTool">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MemCache">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MemoryUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MeshConsolidator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MeshFlattener">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MeshSubdivider">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MetadataNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MetaTile">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Metrics">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MGRSFormatter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MGRSGraticule">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ModelLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ModelNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ModelResource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ModelSource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ModelSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MVT">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\NativeProgramAdapter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\NetworkMonitor">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\NodeUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\NoiseTextureFactory">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Notify">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ObjectIDPicker">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ObjectIndex">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\OGRFeatureSource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\OgrUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\optional">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\OverlayDecorator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PagedNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PatchLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PBRMaterial">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PhongLightingEffect">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Picker">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PlaceNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PluginLoader">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PointDrawable">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PointSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PolygonizeLines">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PolygonSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PowerlineLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PrimitiveIntersector">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Profile">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Progress">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Query">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\RadialLineOfSight">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Random">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\RectangleNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\RefinePolicy">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Registry">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\RenderSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ResampleFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Resource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ResourceCache">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ResourceLibrary">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Revisioning">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\RTTPicker">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScaleFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScatterFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SceneGraphCallback">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScreenSpaceLayout">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScreenSpaceLayoutCallout">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScreenSpaceLayoutDeclutter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScreenSpaceLayoutImpl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Script">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScriptEngine">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScriptFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SDF">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SelectExtentTool">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Session">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShaderFactory">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShaderGenerator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShaderLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShaderLoader">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShaderMerger">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Shaders">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShaderUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Shadowing">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SimpleOceanLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SimplePager">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SimplexNoise">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SimplifyFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Skins">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Sky">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SkyView">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SpatialReference">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StarData">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StateSetCache">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StateTransition">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Status">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Stroke">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Style">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StyleSelector">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StyleSheet">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SubstituteModelFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Symbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Tags">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TDTiles">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Terrain">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainConstraintLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainEffect">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainEngineNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainEngineRequirements">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainMeshLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainOptions">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainProfile">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainResources">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainTileModel">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainTileModelFactory">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainTileNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TessellateOperator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Tessellator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Text">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TextSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TextSymbolizer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TextureArena">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TextureBuffer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TFS">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TFSPackager">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Threading">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ThreeDTilesLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileCache">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TiledFeatureModelLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TiledModelLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileEstimator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileHandler">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileIndex">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileIndexBuilder">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileKey">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileMesher">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileRasterizer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileSource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileSourceElevationLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileSourceImageLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileVisitor">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TimeControl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TimeSeriesImage">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TMS">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TMSBackFiller">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TopologyGraph">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TrackNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TransformFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Units">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\URI">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Utils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\UTMGraticule">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\UTMLabelingEngine">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\VerticalDatum">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\VideoLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ViewFitter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Viewpoint">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\VirtualProgram">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\VisibleLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WFS">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WindLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WireLines">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WMS">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\XmlUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\XYZ">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\XYZFeatureSource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\XYZModelLayer">
    </None>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\rtree.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\weemesh.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\weejobs.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\tinyxml\tinyxml.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\tinyxml\tinystr.h" />
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include\osgEarth\BuildConfig">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include\osgEarth\Version">
    </None>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\vector_tile.pb.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\glyphs.pb.h" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AltitudeFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AltitudeSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AnnotationData.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AnnotationLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AnnotationNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AnnotationRegistry.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AnnotationSettings.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AnnotationUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ArcGISServer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ArcGISTilePackage.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AtlasBuilder.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AttributesFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AutoClipPlaneHandler.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AzureMaps.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BboxDrawable.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BBoxSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BillboardResource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BillboardSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Bing.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Bounds.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BufferFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BuildGeometryFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BuildTextFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Cache.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CacheBin.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CachePolicy.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CacheSeed.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Callouts.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CameraUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Capabilities.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CascadeDrapingDecorator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CentroidFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CesiumIon.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Chonk.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CircleNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ClampableNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ClampCallback.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Clamping.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ClampingTechnique.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ClipSpace.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ClusterNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Color.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ColorFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Composite.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CompositeFeatureSource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CompositeTiledModelLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Compressors.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CompressedArray.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Config.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ContourMap.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ConvertTypeFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CoverageLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CoverageSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CropFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CssUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Cube.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CullingUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DateTime.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DateTimeRange.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DebugImageLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DecalLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DepthOffset.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Draggers.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DrapeableNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DrapingCullSet.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DrapingTechnique.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DrawInstanced.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\EarthManipulator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ECEF.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Elevation.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ElevationLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ElevationLOD.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ElevationPool.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ElevationQuery.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ElevationRanges.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\EllipseNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Ellipsoid.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Ephemeris.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ExampleResources.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Expression.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Extension.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ExtrudeGeometryFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ExtrusionSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FadeEffect.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Feature.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureCursor.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureDisplayLayout.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureElevationLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureImageLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureImageRTTLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureModelGraph.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureModelLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureModelSource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureRasterizer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureSDFLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureSource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureSourceIndexNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureStyleSorter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FileUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Fill.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Filter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FilterContext.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FilteredFeatureSource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FlatteningLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FractalElevationLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FrameClock.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GARSGraticule.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoData.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeodeticGraticule.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeodeticLabelingEngine.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Geoid.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoMath.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Geometry.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeometryClamper.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeometryCloud.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeometryCompiler.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeometryFactory.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeometryRasterizer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeometryUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoPositionNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoPositionNodeAutoScaler.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GEOS.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeographicLibAdapter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeosFeatureSource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoJSONReader.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShapefileReader.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoTransform.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GLSLChunker.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GLUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GraticuleLabelingEngine.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\HeightFieldUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Horizon.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\HorizonClipPlane.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\HTM.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\HTTPClient.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\IconResource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\IconSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageMosaic.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageOverlay.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageOverlayEditor.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageToFeatureLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageToHeightFieldConverter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\InstanceBuilder.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\InstanceCloud.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\InstanceResource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\InstanceSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\IntersectionPicker.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\IOTypes.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\JoinLines.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\JsonUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LabelNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LandCover.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LandCoverLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LatLongFormatter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Layer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LayerShader.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Lighting.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LinearLineOfSight.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LineDrawable.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LineSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LocalGeometryNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LocalTangentPlane.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LODGenerator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LogarithmicDepthBuffer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Map.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MapboxGLGlyphManager.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MapboxGLImageLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MapCallback.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MapNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MaterialLoader.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Math.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MBTiles.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MeasureTool.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MemCache.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MemoryUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MeshConsolidator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MeshFlattener.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MeshSubdivider.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MetadataNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MetaTile.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Metrics.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MGRSFormatter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MGRSGraticule.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ModelLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ModelNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ModelResource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ModelSource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ModelSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MVT.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\NetworkMonitor.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\NodeUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\NoiseTextureFactory.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Notify.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ObjectIDPicker.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ObjectIndex.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\OgrUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\OverlayDecorator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PagedNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PatchLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PBRMaterial.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PhongLightingEffect.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PlaceNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PointDrawable.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PointSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PolygonizeLines.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PolygonSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PowerlineLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PrimitiveIntersector.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Profile.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Progress.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Query.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\RadialLineOfSight.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Random.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\RectangleNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Registry.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\RenderSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ResampleFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Resource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ResourceCache.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ResourceLibrary.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Revisioning.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\RTTPicker.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScaleFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScatterFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SceneGraphCallback.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScreenSpaceLayout.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScriptEngine.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScriptFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SDF.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SelectExtentTool.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Session.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShaderFactory.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShaderGenerator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShaderLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShaderLoader.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShaderMerger.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShaderUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Shadowing.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SimpleOceanLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SimplePager.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SimplexNoise.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SimplifyFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Skins.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Sky.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SkyView.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SpatialReference.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StateSetCache.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Status.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Stroke.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Style.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StyleSelector.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StyleSheet.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SubstituteModelFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Symbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TDTiles.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Terrain.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainConstraintLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainEngineNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainMeshLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainOptions.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainProfile.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainResources.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainTileModel.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainTileModelFactory.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TessellateOperator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Tessellator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Text.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TextSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TextSymbolizer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TextureArena.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TextureBuffer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TextureBufferSerializer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TFSPackager.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Threading.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ThreeDTilesLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileCache.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TiledFeatureModelLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TiledModelLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileEstimator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileHandler.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileKey.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileMesher.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileRasterizer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileSource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileSourceElevationLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileSourceImageLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileVisitor.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TimeControl.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TimeSeriesImage.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TMS.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TMSBackFiller.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TopologyGraph.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TrackNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TransformFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Units.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\URI.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Utils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\UTMGraticule.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\UTMLabelingEngine.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Version.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\VerticalDatum.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\VideoLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ViewFitter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Viewpoint.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\VirtualProgram.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\VisibleLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WindLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WireLines.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WMS.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\XmlUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\XYZ.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\XYZModelLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\tinyxml\tinyxml.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\tinyxml\tinyxmlerror.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\tinyxml\tinystr.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\tinyxml\tinyxmlparser.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\AutoGenShaders.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\vector_tile.pb.cc" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\glyphs.pb.cc" />
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CascadeDraping.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Chonk.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Chonk.Culling.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DepthOffset.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Draping.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DrawInstancedAttribute.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GPUClamping.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GPUClamping.lib.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\HexTiling.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Instancing.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LineDrawable.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MetadataNode.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WireLines.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PhongLighting.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PointDrawable.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Text.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Text_legacy.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ContourMap.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeodeticGraticule.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LogDepthBuffer.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LogDepthBuffer.VertOnly.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShadowCaster.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SimpleOceanLayer.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\RTTPicker.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WindLayer.CS.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PBR.glsl">
    </None>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BuildConfig.in" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Version.in" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Shaders.cpp.in" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\ZERO_CHECK.vcxproj">
      <Project>{7FEAE911-DF1C-3546-885F-77CF38BC3684}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>