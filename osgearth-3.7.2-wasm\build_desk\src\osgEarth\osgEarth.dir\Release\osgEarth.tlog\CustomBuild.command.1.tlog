^F:\CMO-DEV\MY_OSGEARTH_WEB\OSGEARTH_ORIGIN\OSGEARTH-3.7.2-WASM\BUILD_DESK\CMAKEFILES\4A484BBDE562C39E043AA51BDC145C9A\VECTOR_TILE.PB.H.RULE
setlocal
..\..\vcpkg_installed\x64-windows\tools\protobuf\protoc.exe --cpp_out :F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/src/osgEarth -I F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/osgEarth F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/osgEarth/vector_tile.proto
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^F:\CMO-DEV\MY_OSGEARTH_WEB\OSGEARTH_ORIGIN\OSGEARTH-3.7.2-WASM\BUILD_DESK\CMAKEFILES\4A484BBDE562C39E043AA51BDC145C9A\GLYPHS.PB.H.RULE
setlocal
..\..\vcpkg_installed\x64-windows\tools\protobuf\protoc.exe --cpp_out :F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/src/osgEarth -I F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/osgEarth F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/osgEarth/glyphs.proto
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^F:\CMO-DEV\MY_OSGEARTH_WEB\OSGEARTH_ORIGIN\OSGEARTH-3.7.2-WASM\BUILD_DESK\CMAKEFILES\4A484BBDE562C39E043AA51BDC145C9A\AUTOGENSHADERS.CPP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -P F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/src/osgEarth/ConfigureShaders.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^F:\CMO-DEV\MY_OSGEARTH_WEB\OSGEARTH_ORIGIN\OSGEARTH-3.7.2-WASM\SRC\OSGEARTH\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm -BF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/src/osgEarth/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
