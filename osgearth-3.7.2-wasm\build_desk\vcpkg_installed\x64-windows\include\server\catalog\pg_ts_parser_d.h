/*-------------------------------------------------------------------------
 *
 * pg_ts_parser_d.h
 *    Macro definitions for pg_ts_parser
 *
 * Portions Copyright (c) 1996-2023, PostgreSQL Global Development Group
 * Portions Copyright (c) 1994, Regents of the University of California
 *
 * NOTES
 *  ******************************
 *  *** DO NOT EDIT THIS FILE! ***
 *  ******************************
 *
 *  It has been GENERATED by src/backend/catalog/genbki.pl
 *
 *-------------------------------------------------------------------------
 */
#ifndef PG_TS_PARSER_D_H
#define PG_TS_PARSER_D_H

#define TSParserRelationId 3601
#define TSParserNameNspIndexId 3606
#define TSParserOidIndexId 3607

#define Anum_pg_ts_parser_oid 1
#define Anum_pg_ts_parser_prsname 2
#define Anum_pg_ts_parser_prsnamespace 3
#define Anum_pg_ts_parser_prsstart 4
#define Anum_pg_ts_parser_prstoken 5
#define Anum_pg_ts_parser_prsend 6
#define Anum_pg_ts_parser_prsheadline 7
#define Anum_pg_ts_parser_prslextype 8

#define Natts_pg_ts_parser 8


#endif							/* PG_TS_PARSER_D_H */
