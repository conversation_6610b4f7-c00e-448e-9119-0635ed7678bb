/*-------------------------------------------------------------------------
 *
 * pg_policy_d.h
 *    Macro definitions for pg_policy
 *
 * Portions Copyright (c) 1996-2023, PostgreSQL Global Development Group
 * Portions Copyright (c) 1994, Regents of the University of California
 *
 * NOTES
 *  ******************************
 *  *** DO NOT EDIT THIS FILE! ***
 *  ******************************
 *
 *  It has been GENERATED by src/backend/catalog/genbki.pl
 *
 *-------------------------------------------------------------------------
 */
#ifndef PG_POLICY_D_H
#define PG_POLICY_D_H

#define PolicyRelationId 3256
#define PolicyOidIndexId 3257
#define PolicyPolrelidPolnameIndexId 3258

#define Anum_pg_policy_oid 1
#define Anum_pg_policy_polname 2
#define Anum_pg_policy_polrelid 3
#define Anum_pg_policy_polcmd 4
#define Anum_pg_policy_polpermissive 5
#define Anum_pg_policy_polroles 6
#define Anum_pg_policy_polqual 7
#define Anum_pg_policy_polwithcheck 8

#define Natts_pg_policy 8


#endif							/* PG_POLICY_D_H */
