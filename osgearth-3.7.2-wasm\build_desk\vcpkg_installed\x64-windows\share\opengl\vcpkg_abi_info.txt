cmake 3.30.1
features core
glu.pc.in c1225cb320b7e2dfc19a5f81013f420caae0ee0174c28fefea5136a676de7a38
opengl-registry 53bed305119e66685dfeb18c7889d295c606c20795809b9cbe4c7d255aaacae2
opengl.pc.in 72d9d55d55ca7ee852fd9e8473295b8faf34e69d6c2e7b01230c17e68a3f75b7
portfile.cmake be1ddde13da8627b55b39b1a94aa14d69c87e191fadc1134cc2e9011a1ba6032
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
powershell 7.5.1
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-9322494aa025c04635f453cbc392b0ca170d297c
usage e89a9b1dc4f4af7ff9c5f91af4f6d8a0165958be547fbcd1b1a57e4765f5b13a
vcpkg.json 220f239373c77aa668fb692ce1191013cb57d8c38881dfe154f74e14215e8c0f
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_get_windows_sdk 1bb57ecf48db9703b9496a40ff9eeec83b856362b426d23e7d25a54a0ac55916
