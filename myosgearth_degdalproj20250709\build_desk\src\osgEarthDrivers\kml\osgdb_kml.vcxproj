﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{769FBC66-CEEE-3466-A452-06E751BF9E27}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <VcpkgEnabled>false</VcpkgEnabled>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>osg kml</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="do_not_import_user.props" Condition="exists('do_not_import_user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\redist_desk\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">osgdb_kml.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">osgdb_kmld</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\redist_desk\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">osgdb_kml.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">osgdb_kml</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\redist_desk\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">osgdb_kml.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">osgdb_kmls</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\redist_desk\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">osgdb_kml.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">osgdb_kml</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8 /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>
      </ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32_LEAN_AND_MEAN;NOMINMAX;VC_EXTRALEAN;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY_STATIC;CMAKE_INTDIR="Debug";osgdb_kml_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;WIN32_LEAN_AND_MEAN;NOMINMAX;VC_EXTRALEAN;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY_STATIC;CMAKE_INTDIR=\"Debug\";osgdb_kml_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/redist_desk/osgdb_kmld.dll -installedDir F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/build_desk/vcpkg_installed/x64-windows/debug/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\redist_desk\osgEarthd.lib;..\..\..\vcpkg_installed\x64-windows\debug\lib\osgManipulatord.lib;..\..\..\vcpkg_installed\x64-windows\debug\lib\osgShadowd.lib;..\..\..\vcpkg_installed\x64-windows\debug\lib\osgSimd.lib;..\..\..\vcpkg_installed\x64-windows\debug\lib\osgViewerd.lib;..\..\..\vcpkg_installed\x64-windows\debug\lib\osgGAd.lib;..\..\..\vcpkg_installed\x64-windows\debug\lib\osgUtild.lib;..\..\..\vcpkg_installed\x64-windows\debug\lib\osgTextd.lib;..\..\..\vcpkg_installed\x64-windows\debug\lib\osgDBd.lib;..\..\..\vcpkg_installed\x64-windows\debug\lib\osgd.lib;..\..\..\vcpkg_installed\x64-windows\debug\lib\OpenThreadsd.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/redist_desk/osgdb_kmld.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/redist_desk/osgdb_kmld.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>
      </ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32_LEAN_AND_MEAN;NOMINMAX;VC_EXTRALEAN;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY_STATIC;CMAKE_INTDIR="Release";osgdb_kml_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;WIN32_LEAN_AND_MEAN;NOMINMAX;VC_EXTRALEAN;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY_STATIC;CMAKE_INTDIR=\"Release\";osgdb_kml_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/redist_desk/osgdb_kml.dll -installedDir F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/build_desk/vcpkg_installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\redist_desk\osgEarth.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgManipulator.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgShadow.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgSim.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgViewer.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgGA.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgUtil.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgText.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgDB.lib;..\..\..\vcpkg_installed\x64-windows\lib\osg.lib;..\..\..\vcpkg_installed\x64-windows\lib\OpenThreads.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/redist_desk/osgdb_kml.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/redist_desk/osgdb_kml.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>
      </ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32_LEAN_AND_MEAN;NOMINMAX;VC_EXTRALEAN;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY_STATIC;CMAKE_INTDIR="MinSizeRel";osgdb_kml_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;WIN32_LEAN_AND_MEAN;NOMINMAX;VC_EXTRALEAN;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY_STATIC;CMAKE_INTDIR=\"MinSizeRel\";osgdb_kml_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/redist_desk/osgdb_kmls.dll -installedDir F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/build_desk/vcpkg_installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\redist_desk\osgEarths.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgManipulator.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgShadow.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgSim.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgViewer.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgGA.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgUtil.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgText.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgDB.lib;..\..\..\vcpkg_installed\x64-windows\lib\osg.lib;..\..\..\vcpkg_installed\x64-windows\lib\OpenThreads.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/redist_desk/osgdb_kmls.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/redist_desk/osgdb_kmls.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>
      </ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32_LEAN_AND_MEAN;NOMINMAX;VC_EXTRALEAN;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY_STATIC;CMAKE_INTDIR="RelWithDebInfo";osgdb_kml_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;WIN32_LEAN_AND_MEAN;NOMINMAX;VC_EXTRALEAN;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY_STATIC;CMAKE_INTDIR=\"RelWithDebInfo\";osgdb_kml_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/redist_desk/osgdb_kml.dll -installedDir F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/build_desk/vcpkg_installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\redist_desk\osgEarth.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgManipulator.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgShadow.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgSim.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgViewer.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgGA.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgUtil.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgText.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgDB.lib;..\..\..\vcpkg_installed\x64-windows\lib\osg.lib;..\..\..\vcpkg_installed\x64-windows\lib\OpenThreads.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/redist_desk/osgdb_kml.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/redist_desk/osgdb_kml.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/src/osgEarthDrivers/kml/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709 -BF:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/build_desk/src/osgEarthDrivers/kml/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\src\osgEarthDrivers\kml\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/src/osgEarthDrivers/kml/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709 -BF:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/build_desk/src/osgEarthDrivers/kml/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\src\osgEarthDrivers\kml\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/src/osgEarthDrivers/kml/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709 -BF:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/build_desk/src/osgEarthDrivers/kml/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\src\osgEarthDrivers\kml\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/src/osgEarthDrivers/kml/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709 -BF:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_origin/myosgearth_degdalproj20250709/build_desk/src/osgEarthDrivers/kml/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\src\osgEarthDrivers\kml\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\ReaderWriterKML.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KMLReader.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Document.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Feature.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Folder.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Geometry.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_GroundOverlay.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_IconStyle.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_LabelStyle.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_LinearRing.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_LineString.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_LineStyle.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Model.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_MultiGeometry.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_NetworkLink.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_NetworkLinkControl.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Object.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Overlay.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_PhotoOverlay.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Placemark.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Point.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Polygon.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_PolyStyle.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Root.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Schema.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_ScreenOverlay.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Style.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_StyleMap.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KMZArchive.cpp" />
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KMLOptions">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KMLReader">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Common">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Container">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Document">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Feature">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Folder">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Geometry">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_GroundOverlay">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_IconStyle">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_LabelStyle">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_LinearRing">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_LineString">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_LineStyle">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Model">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_MultiGeometry">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_NetworkLink">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_NetworkLinkControl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Object">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Overlay">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Placemark">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_PhotoOverlay">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Point">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Polygon">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_PolyStyle">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Root">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Schema">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_ScreenOverlay">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_Style">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_StyleMap">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KML_StyleSelector">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\KMZArchive">
    </None>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\src\osgEarthDrivers\kml\rapidxml_ext.hpp" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\ZERO_CHECK.vcxproj">
      <Project>{B4F6D5E4-190E-3B5C-BE14-F1C66D4406CE}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\myosgearth_degdalproj20250709\build_desk\src\osgEarth\osgEarth.vcxproj">
      <Project>{10D3C8AD-2F57-3294-A9A0-D94547CA851B}</Project>
      <Name>osgEarth</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>