The package opengl is compatible with built-in CMake targets via CMake v3.7 and prior syntax

    find_package(OpenGL REQUIRED)
    target_link_libraries(main PRIVATE ${OPENGL_LIBRARIES})
    target_include_directories(main PRIVATE ${OPENGL_INCLUDE_DIR})

and the CMake v3.8 and beyond imported target syntax

    find_package(OpenGL REQUIRED)
    target_link_libraries(main PRIVATE OpenGL::GL)

introduction of various components

    find_package(OpenGL REQUIRED COMPONENTS GL      # v3.8
                                            GLU     # v3.8
                                            GLX     # v3.10
                                            EGL     # v3.10
                                            OpenGL) # v3.10

The OpenGL SDK is highly platform dependent and is usually an OS component. It's not realistic to build from source for every platform.

    WINDOWS: is part of the Windows SDK which this package installs.
    LINUX: the SDK may be installed from your distro's repo or from 3rd parties manually. There are too many to count.
    APPLE: consult your distribution vendor on the state of OpenGL support: https://support.apple.com/en-us/HT202823
