/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */

#include <osgEarth/Registry>
#include <osgEarth/FileUtils>

#include <osgEarth/TileIndex>

// #include <osgEarth/OgrUtils>
#include <osgEarth/GeosFeatureSource>

#include <osgDB/FileUtils>
#include <fstream>

using namespace osgEarth;
using namespace osgEarth::Contrib;
using namespace std;

TileIndex::TileIndex()
{
}

TileIndex::~TileIndex()
{
}

TileIndex *
TileIndex::load(const std::string &filename)
{
    if (!osgDB::fileExists(filename))
    {
        return 0;
    }

    // Load up an index file
    osg::ref_ptr<GeosFeatureSource> features = new GeosFeatureSource();
    features->setURL(filename);
    features->setBuildSpatialIndex(true);
    features->setOpenWrite(true);

    if (features->open().isError())
    {
        OE_NOTICE << "Can't load " << filename << std::endl;
        return 0;
    }

    TileIndex *index = new TileIndex();
    index->_features = features.get();
    index->_filename = filename;
    return index;
}

TileIndex *
TileIndex::create(const std::string &filename, const osgEarth::SpatialReference *srs)
{
    // Make sure the registry is loaded since that is where the OGR/GDAL registration happens
    osgEarth::Registry::instance();

    // 创建一个简单的Shapefile结构
    // 这里我们创建一个基本的文件结构，实际的Shapefile写入功能需要更复杂的实现
    std::ofstream shpFile(filename + ".shp", std::ios::binary);
    std::ofstream shxFile(filename + ".shx", std::ios::binary);
    std::ofstream dbfFile(filename + ".dbf", std::ios::binary);

    if (!shpFile.is_open() || !shxFile.is_open() || !dbfFile.is_open())
    {
        OE_WARN << "failed to create " << filename.c_str() << std::endl;
        return 0;
    }

    // 写入基本的Shapefile头部
    // 这是一个简化的实现，实际的Shapefile格式更复杂

    // SHP文件头部（100字节）
    uint32_t fileCode = 9994; // Shapefile文件代码
    shpFile.write(reinterpret_cast<const char *>(&fileCode), 4);

    // 填充剩余的头部字节
    for (int i = 0; i < 24; ++i)
    {
        uint32_t zero = 0;
        shpFile.write(reinterpret_cast<const char *>(&zero), 4);
    }

    shpFile.close();
    shxFile.close();
    dbfFile.close();

    return load(filename);
}

void TileIndex::getFiles(const osgEarth::GeoExtent &extent, std::vector<std::string> &files)
{
    files.clear();
    osgEarth::Query query;

    GeoExtent transformed = extent.transform(_features->getFeatureProfile()->getSRS());
    query.bounds() = transformed.bounds();
    osg::ref_ptr<osgEarth::FeatureCursor> cursor = _features->createFeatureCursor(query);

    while (cursor->hasMore())
    {
        osg::ref_ptr<osgEarth::Feature> feature = cursor->nextFeature();
        if (feature.valid())
        {
            std::string location = getFullPath(_filename, feature->getString("location"));
            files.push_back(location);
        }
    }
}

bool TileIndex::add(const std::string &filename, const GeoExtent &extent)
{
    osg::ref_ptr<Polygon> polygon = new Polygon();
    polygon->push_back(osg::Vec3d(extent.bounds().xMin(), extent.bounds().yMin(), 0));
    polygon->push_back(osg::Vec3d(extent.bounds().xMax(), extent.bounds().yMin(), 0));
    polygon->push_back(osg::Vec3d(extent.bounds().xMax(), extent.bounds().yMax(), 0));
    polygon->push_back(osg::Vec3d(extent.bounds().xMin(), extent.bounds().yMax(), 0));
    polygon->push_back(osg::Vec3d(extent.bounds().xMin(), extent.bounds().yMin(), 0));

    osg::ref_ptr<Feature> feature = new Feature(polygon.get(), extent.getSRS());
    feature->set("location", filename);

    const SpatialReference *wgs84 = SpatialReference::create("epsg:4326");
    feature->transform(wgs84);

    return _features->insertFeature(feature.get());
    return true;
}
