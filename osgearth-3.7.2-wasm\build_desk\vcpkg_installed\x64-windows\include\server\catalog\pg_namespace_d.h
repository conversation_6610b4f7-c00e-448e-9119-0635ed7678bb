/*-------------------------------------------------------------------------
 *
 * pg_namespace_d.h
 *    Macro definitions for pg_namespace
 *
 * Portions Copyright (c) 1996-2023, PostgreSQL Global Development Group
 * Portions Copyright (c) 1994, Regents of the University of California
 *
 * NOTES
 *  ******************************
 *  *** DO NOT EDIT THIS FILE! ***
 *  ******************************
 *
 *  It has been GENERATED by src/backend/catalog/genbki.pl
 *
 *-------------------------------------------------------------------------
 */
#ifndef PG_NAMESPACE_D_H
#define PG_NAMESPACE_D_H

#define NamespaceRelationId 2615
#define NamespaceNameIndexId 2684
#define NamespaceOidIndexId 2685

#define Anum_pg_namespace_oid 1
#define Anum_pg_namespace_nspname 2
#define Anum_pg_namespace_nspowner 3
#define Anum_pg_namespace_nspacl 4

#define Natts_pg_namespace 4

#define PG_CATALOG_NAMESPACE 11
#define PG_TOAST_NAMESPACE 99
#define PG_PUBLIC_NAMESPACE 2200

#endif							/* PG_NAMESPACE_D_H */
