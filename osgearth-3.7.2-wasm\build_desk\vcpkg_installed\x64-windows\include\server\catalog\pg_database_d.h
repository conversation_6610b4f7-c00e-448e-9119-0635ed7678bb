/*-------------------------------------------------------------------------
 *
 * pg_database_d.h
 *    Macro definitions for pg_database
 *
 * Portions Copyright (c) 1996-2023, PostgreSQL Global Development Group
 * Portions Copyright (c) 1994, Regents of the University of California
 *
 * NOTES
 *  ******************************
 *  *** DO NOT EDIT THIS FILE! ***
 *  ******************************
 *
 *  It has been GENERATED by src/backend/catalog/genbki.pl
 *
 *-------------------------------------------------------------------------
 */
#ifndef PG_DATABASE_D_H
#define PG_DATABASE_D_H

#define DatabaseRelationId 1262
#define DatabaseRelation_Rowtype_Id 1248
#define PgDatabaseToastTable 4177
#define PgDatabaseToastIndex 4178
#define DatabaseNameIndexId 2671
#define DatabaseOidIndexId 2672
#define Template0DbOid 4
#define PostgresDbOid 5

#define Anum_pg_database_oid 1
#define Anum_pg_database_datname 2
#define Anum_pg_database_datdba 3
#define Anum_pg_database_encoding 4
#define Anum_pg_database_datlocprovider 5
#define Anum_pg_database_datistemplate 6
#define Anum_pg_database_datallowconn 7
#define Anum_pg_database_datconnlimit 8
#define Anum_pg_database_datfrozenxid 9
#define Anum_pg_database_datminmxid 10
#define Anum_pg_database_dattablespace 11
#define Anum_pg_database_datcollate 12
#define Anum_pg_database_datctype 13
#define Anum_pg_database_daticulocale 14
#define Anum_pg_database_daticurules 15
#define Anum_pg_database_datcollversion 16
#define Anum_pg_database_datacl 17

#define Natts_pg_database 17

#define Template1DbOid 1

#endif							/* PG_DATABASE_D_H */
