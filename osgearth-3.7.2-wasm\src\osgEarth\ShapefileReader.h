/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/Common>
#include <osgEarth/Feature>
#include <osgEarth/Geometry>
#include <string>
#include <vector>
#include <fstream>

namespace osgEarth
{
    /**
     * Simple Shapefile reader that doesn't depend on GDAL.
     * Supports basic shapefile reading for Point, Polyline, and Polygon types.
     */
    class OSGEARTH_EXPORT ShapefileReader
    {
    public:
        enum ShapeType
        {
            SHAPE_NULL = 0,
            SHAPE_POINT = 1,
            SHAPE_POLYLINE = 3,
            SHAPE_POLYGON = 5,
            SHAPE_MULTIPOINT = 8,
            SHAPE_POINTZ = 11,
            SHAPE_POLYLINEZ = 13,
            SHAPE_POLYGONZ = 15,
            SHAPE_MULTIPOINTZ = 18,
            SHAPE_POINTM = 21,
            SHAPE_POLYLINEM = 23,
            SHAPE_POLYGONM = 25,
            SHAPE_MULTIPOINTM = 28,
            SHAPE_MULTIPATCH = 31
        };

        struct ShapeHeader
        {
            int fileCode;
            int fileLength;
            int version;
            int shapeType;
            double xMin, yMin, xMax, yMax;
            double zMin, zMax;
            double mMin, mMax;
        };

        struct RecordHeader
        {
            int recordNumber;
            int contentLength;
        };

    public:
        ShapefileReader();
        ~ShapefileReader();

        /**
         * Open a shapefile for reading
         */
        bool open(const std::string& filename);

        /**
         * Close the shapefile
         */
        void close();

        /**
         * Read all features from the shapefile
         */
        bool readFeatures(std::vector<osg::ref_ptr<Feature>>& features);

        /**
         * Get the shapefile header
         */
        const ShapeHeader& getHeader() const { return _header; }

        /**
         * Get the bounding box
         */
        void getBounds(double& xMin, double& yMin, double& xMax, double& yMax) const;

    private:
        bool readHeader();
        bool readRecord(osg::ref_ptr<Feature>& feature);
        Geometry* readPointGeometry();
        Geometry* readPolylineGeometry();
        Geometry* readPolygonGeometry();
        
        // Utility functions for reading binary data
        int readInt32();
        double readDouble();
        void readBytes(char* buffer, int count);
        bool seekTo(std::streampos pos);

        // Endian conversion
        int swapEndian(int value);
        double swapEndian(double value);
        
        // DBF file reading
        bool openDBF(const std::string& shpFilename);
        void closeDBF();
        bool readDBFHeader();
        bool readDBFRecord(int recordIndex, AttributeTable& attributes);
        
        struct DBFField
        {
            std::string name;
            char type;
            int length;
            int decimals;
        };
        
        struct DBFHeader
        {
            char version;
            int recordCount;
            int headerLength;
            int recordLength;
            std::vector<DBFField> fields;
        };

    private:
        std::ifstream _shpFile;
        std::ifstream _dbfFile;
        ShapeHeader _header;
        DBFHeader _dbfHeader;
        bool _isOpen;
        bool _dbfOpen;
        std::string _filename;
        
        // Current position tracking
        std::streampos _currentPos;
        int _recordCount;
        
        // Error handling
        std::string _lastError;
        
        // Helper methods
        bool isLittleEndian();
        void setError(const std::string& error);
        std::string getError() const { return _lastError; }
        
        // Geometry creation helpers
        Point* createPoint(double x, double y, double z = 0.0);
        LineString* createLineString(const std::vector<osg::Vec3d>& points);
        Polygon* createPolygon(const std::vector<std::vector<osg::Vec3d>>& rings);
        MultiGeometry* createMultiGeometry();
        
        // Coordinate validation
        bool isValidCoordinate(double x, double y);
        void validateBounds(double& xMin, double& yMin, double& xMax, double& yMax);
        
        // Memory management
        void cleanup();
        
        // Constants
        static const int SHP_FILE_CODE = 9994;
        static const int SHP_VERSION = 1000;
        static const int HEADER_SIZE = 100;
        static const int RECORD_HEADER_SIZE = 8;
        
        // DBF constants
        static const char DBF_FIELD_TERMINATOR = 0x0D;
        static const int DBF_HEADER_SIZE = 32;
        static const int DBF_FIELD_SIZE = 32;
    };

    /**
     * Utility functions for Shapefile processing
     */
    namespace ShapefileUtils
    {
        /**
         * Get shape type name as string
         */
        OSGEARTH_EXPORT std::string getShapeTypeName(int shapeType);
        
        /**
         * Check if shape type is supported
         */
        OSGEARTH_EXPORT bool isSupportedShapeType(int shapeType);
        
        /**
         * Get geometry type from shape type
         */
        OSGEARTH_EXPORT Geometry::Type getGeometryType(int shapeType);
        
        /**
         * Validate shapefile filename and check for required files
         */
        OSGEARTH_EXPORT bool validateShapefileFiles(const std::string& shpFilename, 
                                                   std::vector<std::string>& missingFiles);
        
        /**
         * Get associated filenames (.shx, .dbf, .prj, etc.)
         */
        OSGEARTH_EXPORT std::vector<std::string> getAssociatedFiles(const std::string& shpFilename);
        
        /**
         * Check if file exists and is readable
         */
        OSGEARTH_EXPORT bool isFileReadable(const std::string& filename);
    }

} // namespace osgEarth
