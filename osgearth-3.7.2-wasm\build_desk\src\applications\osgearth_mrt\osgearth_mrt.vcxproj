﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{1C0042E4-E2C6-35E3-AE0A-3C319DF4B1B3}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <VcpkgEnabled>false</VcpkgEnabled>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>osgearth_mrt</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="do_not_import_user.props" Condition="exists('do_not_import_user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\redist_desk\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">osgearth_mrt.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">osgearth_mrtd</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\redist_desk\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">osgearth_mrt.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">osgearth_mrt</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\redist_desk\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">osgearth_mrt.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">osgearth_mrts</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\redist_desk\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">osgearth_mrt.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">osgearth_mrt</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8 /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>
      </ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;WIN32_LEAN_AND_MEAN;NOMINMAX;NOGDI;APIENTRY=;WINGDIAPI=;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;WIN32_LEAN_AND_MEAN;NOMINMAX;NOGDI;APIENTRY=;WINGDIAPI=;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/redist_desk/osgearth_mrtd.exe -installedDir F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/vcpkg_installed/x64-windows/debug/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\redist_desk\osgEarthd.lib;..\..\..\vcpkg_installed\x64-windows\debug\lib\osgManipulatord.lib;..\..\..\vcpkg_installed\x64-windows\debug\lib\osgShadowd.lib;..\..\..\vcpkg_installed\x64-windows\debug\lib\osgSimd.lib;..\..\..\vcpkg_installed\x64-windows\debug\lib\osgViewerd.lib;..\..\..\vcpkg_installed\x64-windows\debug\lib\osgGAd.lib;..\..\..\vcpkg_installed\x64-windows\debug\lib\osgUtild.lib;..\..\..\vcpkg_installed\x64-windows\debug\lib\osgTextd.lib;..\..\..\vcpkg_installed\x64-windows\debug\lib\osgDBd.lib;..\..\..\vcpkg_installed\x64-windows\debug\lib\osgd.lib;..\..\..\vcpkg_installed\x64-windows\debug\lib\OpenThreadsd.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/redist_desk/osgearth_mrtd.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/redist_desk/osgearth_mrtd.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>
      </ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;WIN32_LEAN_AND_MEAN;NOMINMAX;NOGDI;APIENTRY=;WINGDIAPI=;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;WIN32_LEAN_AND_MEAN;NOMINMAX;NOGDI;APIENTRY=;WINGDIAPI=;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/redist_desk/osgearth_mrt.exe -installedDir F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/vcpkg_installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\redist_desk\osgEarth.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgManipulator.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgShadow.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgSim.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgViewer.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgGA.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgUtil.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgText.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgDB.lib;..\..\..\vcpkg_installed\x64-windows\lib\osg.lib;..\..\..\vcpkg_installed\x64-windows\lib\OpenThreads.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/redist_desk/osgearth_mrt.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/redist_desk/osgearth_mrt.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>
      </ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;WIN32_LEAN_AND_MEAN;NOMINMAX;NOGDI;APIENTRY=;WINGDIAPI=;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;WIN32_LEAN_AND_MEAN;NOMINMAX;NOGDI;APIENTRY=;WINGDIAPI=;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/redist_desk/osgearth_mrts.exe -installedDir F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/vcpkg_installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\redist_desk\osgEarths.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgManipulator.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgShadow.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgSim.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgViewer.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgGA.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgUtil.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgText.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgDB.lib;..\..\..\vcpkg_installed\x64-windows\lib\osg.lib;..\..\..\vcpkg_installed\x64-windows\lib\OpenThreads.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/redist_desk/osgearth_mrts.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/redist_desk/osgearth_mrts.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>
      </ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;WIN32_LEAN_AND_MEAN;NOMINMAX;NOGDI;APIENTRY=;WINGDIAPI=;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;WIN32_LEAN_AND_MEAN;NOMINMAX;NOGDI;APIENTRY=;WINGDIAPI=;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/redist_desk/osgearth_mrt.exe -installedDir F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/vcpkg_installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\redist_desk\osgEarth.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgManipulator.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgShadow.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgSim.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgViewer.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgGA.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgUtil.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgText.lib;..\..\..\vcpkg_installed\x64-windows\lib\osgDB.lib;..\..\..\vcpkg_installed\x64-windows\lib\osg.lib;..\..\..\vcpkg_installed\x64-windows\lib\OpenThreads.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/redist_desk/osgearth_mrt.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/redist_desk/osgearth_mrt.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_mrt\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/applications/osgearth_mrt/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm -BF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/src/applications/osgearth_mrt/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_mrt\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/applications/osgearth_mrt/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm -BF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/src/applications/osgearth_mrt/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_mrt\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/applications/osgearth_mrt/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm -BF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/src/applications/osgearth_mrt/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_mrt\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/src/applications/osgearth_mrt/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm -BF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/src/applications/osgearth_mrt/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_mrt\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_mrt\osgearth_mrt.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\ZERO_CHECK.vcxproj">
      <Project>{7FEAE911-DF1C-3546-885F-77CF38BC3684}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\osgEarth.vcxproj">
      <Project>{88534FC6-C81A-3A31-8BFA-FF6DAD09198E}</Project>
      <Name>osgEarth</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>