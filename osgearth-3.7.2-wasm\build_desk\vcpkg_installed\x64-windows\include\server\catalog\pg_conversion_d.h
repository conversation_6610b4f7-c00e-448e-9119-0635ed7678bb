/*-------------------------------------------------------------------------
 *
 * pg_conversion_d.h
 *    Macro definitions for pg_conversion
 *
 * Portions Copyright (c) 1996-2023, PostgreSQL Global Development Group
 * Portions Copyright (c) 1994, Regents of the University of California
 *
 * NOTES
 *  ******************************
 *  *** DO NOT EDIT THIS FILE! ***
 *  ******************************
 *
 *  It has been GENERATED by src/backend/catalog/genbki.pl
 *
 *-------------------------------------------------------------------------
 */
#ifndef PG_CONVERSION_D_H
#define PG_CONVERSION_D_H

#define ConversionRelationId 2607
#define ConversionDefaultIndexId 2668
#define ConversionNameNspIndexId 2669
#define ConversionOidIndexId 2670

#define Anum_pg_conversion_oid 1
#define Anum_pg_conversion_conname 2
#define Anum_pg_conversion_connamespace 3
#define Anum_pg_conversion_conowner 4
#define Anum_pg_conversion_conforencoding 5
#define Anum_pg_conversion_contoencoding 6
#define Anum_pg_conversion_conproc 7
#define Anum_pg_conversion_condefault 8

#define Natts_pg_conversion 8


#endif							/* PG_CONVERSION_D_H */
