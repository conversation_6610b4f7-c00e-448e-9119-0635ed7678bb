0002-cmake-config.patch 55567849c38d3231032cb0aff6c067e1e2ca1c6515823880a6c4a885780c0ba3
0003-simd.patch 4ca73353afbfefc31147d62d7f035e151b981f9cda6636151a84a3ca51cd39d9
0008-sdl.patch 2f05c20f07165ac556ef7c67c25af0e461d3cbd8539d5769296ace52b37b9311
cmake 3.30.1
features core;libwebpmux;nearlossless;simd;unicode
portfile.cmake 651dcac16db26c84ae4b7e2212d50069e03f23e109dd8305f81aa35293c1fbf6
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
powershell 7.5.1
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-9322494aa025c04635f453cbc392b0ca170d297c
usage 4898bdd3351dcd0868d2f0786ab114c8b161dda8db6cd84f7fe93bf0639f08d1
vcpkg-cmake fa0a421976433b6b0635f7ccfd6e08f02a20aef877e749d6792bc1d9ae8233af
vcpkg-cmake-config 8d261494f5cab6cecd7851edc7eb14f27f3c82cd259e8a56095998d1199973a0
vcpkg.json 947caa1503b7f70b846f045ad40868189ab2eecdf7c4da24fe459bc3805d0139
vcpkg_check_features 943b217e0968d64cf2cb9c272608e6a0b497377e792034f819809a79e1502c2b
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_copy_tools 3d45ff761bddbabe8923b52330168dc3abd295fa469d3f2e47cb14dce85332d5
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
vcpkg_list f5de3ebcbc40a4db90622ade9aca918e2cf404dc0d91342fcde457d730e6fa29
