/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#include <osgEarth/GeosFeatureSource>
#include <osgEarth/FeatureCursor>
#include <osgEarth/Filter>
#include <osgEarth/JsonUtils>
#include <osgEarth/StringUtils>
#include <osgEarth/FileUtils>
#include <osgEarth/Metrics>
#include <osgEarth/GeometryUtils>
#include <osgDB/FileUtils>
#include "ShapefileReader.h"
#include "GeoJSONReader.h"
#include <fstream>
#include <sstream>

#define LC "[GeosFeatureSource] " << getName() << " : "

using namespace osgEarth;

#define OE_DEVEL OE_NULL

//........................................................................

Config
GeosFeatureSource::Options::getConfig() const
{
    Config conf = super::Options::getConfig();
    conf.set("url", _url);
    conf.set("connection", _connection);
    conf.set("format", _format);
    conf.set("build_spatial_index", _buildSpatialIndex);
    conf.set("force_rebuild_spatial_index", _forceRebuildSpatialIndex);
    conf.set("geometry", _geometryConfig);
    conf.set("geometry_url", _geometryUrl);
    conf.set("layer", _layer);
    conf.set("query", _query);
    return conf;
}

void GeosFeatureSource::Options::fromConfig(const Config &conf)
{
    conf.get("url", _url);
    conf.get("connection", _connection);
    conf.get("format", _format);
    conf.get("build_spatial_index", _buildSpatialIndex);
    conf.get("force_rebuild_spatial_index", _forceRebuildSpatialIndex);
    conf.get("geometry", _geometryConfig);
    conf.get("geometry_url", _geometryUrl);
    conf.get("layer", _layer);
    conf.get("query", _query);
}

//........................................................................

REGISTER_OSGEARTH_LAYER(geosfeatures, GeosFeatureSource);

GeosFeatureSource::GeosFeatureSource() :
    _extentComputed(false),
    _featuresLoaded(false),
    _buildSpatialIndex(false),
    _forceRebuildSpatialIndex(false),
    _isWritable(false),
    _featureCount(0),
    _supportsGetFeature(true)
{
    // nop
}

GeosFeatureSource::~GeosFeatureSource()
{
    // nop
}

void GeosFeatureSource::setURL(const URI &value)
{
    options().url() = value;
}

const URI &
GeosFeatureSource::getURL() const
{
    return options().url().value();
}

void GeosFeatureSource::setConnection(const std::string &value)
{
    options().connection() = value;
}

const std::string &
GeosFeatureSource::getConnection() const
{
    return options().connection().value();
}

void GeosFeatureSource::setFormat(const std::string &value)
{
    options().format() = value;
}

const std::string &
GeosFeatureSource::getFormat() const
{
    return options().format().value();
}

void GeosFeatureSource::setBuildSpatialIndex(const bool &value)
{
    options().buildSpatialIndex() = value;
}

const bool &
GeosFeatureSource::getBuildSpatialIndex() const
{
    return options().buildSpatialIndex().value();
}

void GeosFeatureSource::setGeometryConfig(const Config &value)
{
    options().geometryConfig() = value;
}

const Config &
GeosFeatureSource::getGeometryConfig() const
{
    return options().geometryConfig().value();
}

void GeosFeatureSource::setGeometryUrl(const URI &value)
{
    options().geometryUrl() = value;
}

const URI &
GeosFeatureSource::getGeometryUrl() const
{
    return options().geometryUrl().value();
}

void GeosFeatureSource::setLayer(const std::string &value)
{
    options().layer() = value;
}

const std::string &
GeosFeatureSource::getLayer() const
{
    return options().layer().value();
}

void GeosFeatureSource::setQuery(const Query &value)
{
    options().query() = value;
}

const Query &
GeosFeatureSource::getQuery() const
{
    return options().query().value();
}

void GeosFeatureSource::setGeometry(Geometry *geom)
{
    _geometry = geom;
}

const Geometry *
GeosFeatureSource::getGeometry() const
{
    return _geometry.get();
}

void GeosFeatureSource::init()
{
    super::init();
}

Status
GeosFeatureSource::openImplementation()
{
    Status parent = super::openImplementation();
    if (parent.isError())
        return parent;

    // Load features from file
    readFeatures();

    return Status::NoError;
}

Status
GeosFeatureSource::closeImplementation()
{
    _features.clear();
    _featuresLoaded = false;
    _extentComputed = false;
    return Status::NoError;
}

void
GeosFeatureSource::readFeatures()
{
    if (_featuresLoaded)
        return;

    Threading::ScopedMutexLock lock(_mutex);
    if (_featuresLoaded)
        return;

    std::string format = getFormat();
    if (format.empty())
    {
        // Auto-detect format from URL
        std::string url = getURL().full();
        if (StringUtils::endsWith(url, ".shp", false))
            format = "shapefile";
        else if (StringUtils::endsWith(url, ".geojson", false) || 
                 StringUtils::endsWith(url, ".json", false))
            format = "geojson";
    }

    if (format == "shapefile")
    {
        readShapefile();
    }
    else if (format == "geojson")
    {
        readGeoJSON();
    }
    else
    {
        OE_WARN << LC << "Unsupported format: " << format << std::endl;
    }

    _featuresLoaded = true;
}

void
GeosFeatureSource::readShapefile()
{
    std::string filename = getURL().full();
    ShapefileReader reader;
    
    if (reader.open(filename))
    {
        FeatureList features;
        if (reader.readFeatures(features))
        {
            _features = features;
            _featureCount = _features.size();
            
            // Compute extent
            computeDataExtents();
        }
        reader.close();
    }
}

void
GeosFeatureSource::readGeoJSON()
{
    std::string filename = getURL().full();
    GeoJSONReader reader;
    
    FeatureList features;
    if (reader.readFeatures(filename, features))
    {
        _features = features;
        _featureCount = _features.size();
        
        // Compute extent
        computeDataExtents();
    }
}

void
GeosFeatureSource::computeDataExtents()
{
    if (_extentComputed || _features.empty())
        return;

    GeoExtent extent;
    bool first = true;

    for (const auto& feature : _features)
    {
        if (feature->getGeometry())
        {
            Bounds bounds = feature->getGeometry()->getBounds();
            if (bounds.valid())
            {
                if (first)
                {
                    extent = GeoExtent(getFeatureProfile()->getSRS(), bounds);
                    first = false;
                }
                else
                {
                    extent.expandToInclude(GeoExtent(getFeatureProfile()->getSRS(), bounds));
                }
            }
        }
    }

    if (!first)
    {
        _extent = extent;
        _extentComputed = true;
    }
}

FeatureCursor *
GeosFeatureSource::createFeatureCursorImplementation(const Query &query, ProgressCallback *progress) const
{
    const_cast<GeosFeatureSource*>(this)->readFeatures();
    
    FeatureList filteredFeatures;
    
    // Apply query filters
    for (const auto& feature : _features)
    {
        bool include = true;
        
        // Apply spatial filter if present
        if (query.bounds().isSet() && feature->getGeometry())
        {
            Bounds featureBounds = feature->getGeometry()->getBounds();
            if (!query.bounds()->intersects(featureBounds))
                include = false;
        }
        
        if (include)
            filteredFeatures.push_back(feature);
    }
    
    return new FeatureListCursor(filteredFeatures);
}

int
GeosFeatureSource::getFeatureCount() const
{
    const_cast<GeosFeatureSource*>(this)->readFeatures();
    return _featureCount;
}

bool
GeosFeatureSource::supportsGetFeature() const
{
    return _supportsGetFeature;
}

Feature *
GeosFeatureSource::getFeature(FeatureID fid)
{
    readFeatures();
    
    for (const auto& feature : _features)
    {
        if (feature->getFID() == fid)
            return feature.get();
    }
    
    return nullptr;
}

bool
GeosFeatureSource::isWritable() const
{
    return _isWritable;
}

const FeatureSchema &
GeosFeatureSource::getSchema() const
{
    const_cast<GeosFeatureSource*>(this)->readFeatures();
    return _schema;
}

bool
GeosFeatureSource::deleteFeature(FeatureID fid)
{
    return false; // Not implemented
}

bool
GeosFeatureSource::insertFeature(Feature *feature)
{
    return false; // Not implemented
}

Geometry *
GeosFeatureSource::createGeometry(const Config &geomConf)
{
    return nullptr; // Not implemented
}

std::string
GeosFeatureSource::getFeatureUrl()
{
    return getURL().full();
}
