﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{7FEAE911-DF1C-3546-885F-77CF38BC3684}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ZERO_CHECK</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\6d4e337677d5c33fe22e900acc5c22cb\generate.stamp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm -BF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/OSGEARTH.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenGL.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenSceneGraph.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Findosg.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgDB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgGA.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgManipulator.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgShadow.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgSim.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgText.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgUtil.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgViewer.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Findosg_functions.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\WriteBasicConfigVersionFile.cmake;C:\dev\vcpkg\scripts\buildsystems\vcpkg.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\3.26.4\CMakeCCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\3.26.4\CMakeSystem.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\WebP\WebPConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\WebP\WebPConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\WebP\WebPTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\WebP\WebPTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\WebP\WebPTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-generate.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-module.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-options.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\rocksdb\RocksDBConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\rocksdb\RocksDBConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\rocksdb\RocksDBTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\rocksdb\RocksDBTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\rocksdb\RocksDBTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\ConfigureShaders.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\cmake_uninstall.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\install-package-config-files.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\oe_ios.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\oe_osx.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\oe_unix.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\oe_win32.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\osgearth-config.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\osgearth-macros.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\docs\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_3pv\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_annotation\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_atlas\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_bakefeaturetiles\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_boundarygen\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_clamp\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_conv\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_featurefilter\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_features\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_heatmap\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_imgui\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_infinitescroll\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_los\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_map\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_minimap\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_mrt\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_occlusionculling\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_server\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_simple\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_skyview\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_terrainprofile\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_tfs\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_version\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_video\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_viewer\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BuildConfig.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Version.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\bumpmap\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\cache_filesystem\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\cache_rocksdb\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\colorramp\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\detail\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\earth\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\engine_rex\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\fastdxt\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\featurefilter_intersect\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\featurefilter_join\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\gltf\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\kml\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\lerc\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\script_engine_duktape\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\sky_gl\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\sky_simple\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\template\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\terrainshader\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\vdatum_egm2008\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\vdatum_egm84\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\vdatum_egm96\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\viewpoints\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\webp\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\zip\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthImGui\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\vcpkg.json;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthImGui\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\bumpmap\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\cache_filesystem\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\colorramp\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\detail\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\earth\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\engine_rex\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\featurefilter_intersect\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\featurefilter_join\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\kml\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\script_engine_duktape\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\sky_gl\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\sky_simple\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\template\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\terrainshader\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\vdatum_egm2008\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\vdatum_egm84\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\vdatum_egm96\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\viewpoints\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\webp\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\zip\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\gltf\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\cache_rocksdb\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\fastdxt\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\lerc\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_viewer\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_tfs\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_boundarygen\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_version\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_atlas\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_bakefeaturetiles\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_conv\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_3pv\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_clamp\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_server\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_imgui\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_simple\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_features\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_featurefilter\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_los\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_terrainprofile\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_map\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_annotation\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_occlusionculling\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_minimap\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_mrt\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_skyview\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_infinitescroll\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_video\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_heatmap\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\docs\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm -BF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/OSGEARTH.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenGL.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenSceneGraph.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Findosg.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgDB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgGA.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgManipulator.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgShadow.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgSim.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgText.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgUtil.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgViewer.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Findosg_functions.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\WriteBasicConfigVersionFile.cmake;C:\dev\vcpkg\scripts\buildsystems\vcpkg.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\3.26.4\CMakeCCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\3.26.4\CMakeSystem.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\WebP\WebPConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\WebP\WebPConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\WebP\WebPTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\WebP\WebPTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\WebP\WebPTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-generate.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-module.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-options.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\rocksdb\RocksDBConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\rocksdb\RocksDBConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\rocksdb\RocksDBTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\rocksdb\RocksDBTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\rocksdb\RocksDBTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\ConfigureShaders.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\cmake_uninstall.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\install-package-config-files.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\oe_ios.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\oe_osx.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\oe_unix.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\oe_win32.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\osgearth-config.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\osgearth-macros.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\docs\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_3pv\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_annotation\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_atlas\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_bakefeaturetiles\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_boundarygen\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_clamp\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_conv\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_featurefilter\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_features\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_heatmap\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_imgui\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_infinitescroll\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_los\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_map\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_minimap\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_mrt\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_occlusionculling\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_server\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_simple\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_skyview\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_terrainprofile\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_tfs\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_version\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_video\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_viewer\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BuildConfig.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Version.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\bumpmap\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\cache_filesystem\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\cache_rocksdb\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\colorramp\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\detail\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\earth\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\engine_rex\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\fastdxt\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\featurefilter_intersect\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\featurefilter_join\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\gltf\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\kml\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\lerc\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\script_engine_duktape\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\sky_gl\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\sky_simple\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\template\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\terrainshader\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\vdatum_egm2008\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\vdatum_egm84\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\vdatum_egm96\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\viewpoints\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\webp\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\zip\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthImGui\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\vcpkg.json;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthImGui\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\bumpmap\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\cache_filesystem\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\colorramp\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\detail\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\earth\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\engine_rex\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\featurefilter_intersect\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\featurefilter_join\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\kml\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\script_engine_duktape\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\sky_gl\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\sky_simple\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\template\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\terrainshader\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\vdatum_egm2008\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\vdatum_egm84\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\vdatum_egm96\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\viewpoints\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\webp\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\zip\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\gltf\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\cache_rocksdb\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\fastdxt\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\lerc\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_viewer\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_tfs\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_boundarygen\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_version\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_atlas\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_bakefeaturetiles\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_conv\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_3pv\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_clamp\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_server\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_imgui\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_simple\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_features\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_featurefilter\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_los\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_terrainprofile\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_map\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_annotation\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_occlusionculling\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_minimap\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_mrt\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_skyview\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_infinitescroll\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_video\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_heatmap\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\docs\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm -BF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/OSGEARTH.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenGL.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenSceneGraph.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Findosg.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgDB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgGA.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgManipulator.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgShadow.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgSim.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgText.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgUtil.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgViewer.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Findosg_functions.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\WriteBasicConfigVersionFile.cmake;C:\dev\vcpkg\scripts\buildsystems\vcpkg.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\3.26.4\CMakeCCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\3.26.4\CMakeSystem.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\WebP\WebPConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\WebP\WebPConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\WebP\WebPTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\WebP\WebPTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\WebP\WebPTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-generate.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-module.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-options.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\rocksdb\RocksDBConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\rocksdb\RocksDBConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\rocksdb\RocksDBTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\rocksdb\RocksDBTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\rocksdb\RocksDBTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\ConfigureShaders.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\cmake_uninstall.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\install-package-config-files.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\oe_ios.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\oe_osx.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\oe_unix.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\oe_win32.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\osgearth-config.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\osgearth-macros.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\docs\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_3pv\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_annotation\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_atlas\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_bakefeaturetiles\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_boundarygen\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_clamp\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_conv\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_featurefilter\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_features\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_heatmap\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_imgui\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_infinitescroll\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_los\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_map\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_minimap\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_mrt\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_occlusionculling\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_server\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_simple\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_skyview\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_terrainprofile\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_tfs\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_version\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_video\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_viewer\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BuildConfig.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Version.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\bumpmap\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\cache_filesystem\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\cache_rocksdb\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\colorramp\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\detail\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\earth\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\engine_rex\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\fastdxt\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\featurefilter_intersect\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\featurefilter_join\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\gltf\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\kml\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\lerc\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\script_engine_duktape\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\sky_gl\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\sky_simple\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\template\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\terrainshader\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\vdatum_egm2008\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\vdatum_egm84\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\vdatum_egm96\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\viewpoints\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\webp\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\zip\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthImGui\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\vcpkg.json;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthImGui\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\bumpmap\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\cache_filesystem\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\colorramp\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\detail\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\earth\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\engine_rex\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\featurefilter_intersect\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\featurefilter_join\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\kml\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\script_engine_duktape\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\sky_gl\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\sky_simple\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\template\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\terrainshader\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\vdatum_egm2008\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\vdatum_egm84\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\vdatum_egm96\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\viewpoints\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\webp\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\zip\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\gltf\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\cache_rocksdb\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\fastdxt\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\lerc\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_viewer\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_tfs\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_boundarygen\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_version\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_atlas\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_bakefeaturetiles\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_conv\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_3pv\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_clamp\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_server\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_imgui\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_simple\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_features\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_featurefilter\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_los\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_terrainprofile\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_map\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_annotation\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_occlusionculling\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_minimap\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_mrt\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_skyview\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_infinitescroll\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_video\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_heatmap\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\docs\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm -BF:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file F:/cmo-dev/my_osgearth_web/osgearth_origin/osgearth-3.7.2-wasm/build_desk/OSGEARTH.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenGL.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenSceneGraph.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Findosg.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgDB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgGA.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgManipulator.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgShadow.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgSim.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgText.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgUtil.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgViewer.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Findosg_functions.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\WriteBasicConfigVersionFile.cmake;C:\dev\vcpkg\scripts\buildsystems\vcpkg.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\3.26.4\CMakeCCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\3.26.4\CMakeSystem.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\WebP\WebPConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\WebP\WebPConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\WebP\WebPTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\WebP\WebPTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\WebP\WebPTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\absl\abslTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\blend2d\blend2d-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\CURLTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\fmt\fmt-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geographiclib\geographiclib-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\geos\geos-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\libzip\libzip-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\meshoptimizer\meshoptimizerTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-config-version.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-generate.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-module.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-options.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\protobuf-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\protobuf\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\rocksdb\RocksDBConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\rocksdb\RocksDBConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\rocksdb\RocksDBTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\rocksdb\RocksDBTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\rocksdb\RocksDBTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfig.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-config.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets-debug.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets-release.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\utf8_range\utf8_range-targets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\ConfigureShaders.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\cmake_uninstall.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\install-package-config-files.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\oe_ios.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\oe_osx.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\oe_unix.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\oe_win32.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\osgearth-config.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\cmake\osgearth-macros.cmake;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\docs\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_3pv\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_annotation\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_atlas\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_bakefeaturetiles\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_boundarygen\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_clamp\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_conv\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_featurefilter\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_features\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_heatmap\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_imgui\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_infinitescroll\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_los\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_map\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_minimap\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_mrt\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_occlusionculling\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_server\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_simple\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_skyview\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_terrainprofile\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_tfs\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_version\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_video\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\applications\osgearth_viewer\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BuildConfig.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Version.in;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\bumpmap\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\cache_filesystem\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\cache_rocksdb\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\colorramp\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\detail\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\earth\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\engine_rex\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\fastdxt\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\featurefilter_intersect\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\featurefilter_join\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\gltf\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\kml\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\lerc\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\script_engine_duktape\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\sky_gl\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\sky_simple\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\template\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\terrainshader\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\vdatum_egm2008\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\vdatum_egm84\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\vdatum_egm96\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\viewpoints\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\webp\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthDrivers\zip\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarthImGui\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\vcpkg.json;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthImGui\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\bumpmap\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\cache_filesystem\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\colorramp\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\detail\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\earth\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\engine_rex\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\featurefilter_intersect\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\featurefilter_join\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\kml\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\script_engine_duktape\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\sky_gl\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\sky_simple\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\template\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\terrainshader\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\vdatum_egm2008\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\vdatum_egm84\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\vdatum_egm96\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\viewpoints\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\webp\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\zip\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\gltf\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\cache_rocksdb\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\fastdxt\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarthDrivers\lerc\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_viewer\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_tfs\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_boundarygen\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_version\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_atlas\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_bakefeaturetiles\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_conv\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_3pv\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_clamp\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_server\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_imgui\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_simple\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_features\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_featurefilter\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_los\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_terrainprofile\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_map\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_annotation\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_occlusionculling\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_minimap\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_mrt\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_skyview\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_infinitescroll\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_video\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\applications\osgearth_heatmap\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\docs\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>