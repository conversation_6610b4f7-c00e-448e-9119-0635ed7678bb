CMakeLists.txt 0966993582b486b896db04fb99404d2f6100fe538bccfdce2b6fd013454706df
cmake 3.30.1
exports.def c6c3dee34dde15b1474e937b4f70ed7ca67847a826af4db749a8c9f414fae2b1
features core
msvc.diff 494165202112a8158c2e4fce46d092ec8bca41dd96fed3f064071f6675aa5ec2
portfile.cmake bb768bdf0d1040de0bf0fe3742f74aee00b5d244533f8b23cf213cf05362270a
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
powershell 7.5.1
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-9322494aa025c04635f453cbc392b0ca170d297c
usage 76da1f15431142f10b07577a061a3028ad2e07090258533c24a59a8965e72abd
vcpkg-cmake fa0a421976433b6b0635f7ccfd6e08f02a20aef877e749d6792bc1d9ae8233af
vcpkg-cmake-wrapper.cmake 35c26f064da36e0e2da823d27249200b913196ee70a5f2559c9b00598d527a4e
vcpkg.json 25c0bf45d9dbf08adc169743f4759e712a022b1dbf67d3fb3b8b5afae2a96767
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_from_sourceforge 00cb7d5767d56fdd8a1715ebd3c159fccd44dc17653522e23d2e507bce44a4f8
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
