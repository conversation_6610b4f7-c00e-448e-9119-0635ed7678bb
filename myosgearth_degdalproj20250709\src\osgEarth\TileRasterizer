/* osgEarth
* Copyright 2025 Pelican Mapping
* MIT License
*/
#ifndef OSGEARTH_TILE_RASTERIZER_H
#define OSGEARTH_TILE_RASTERIZER_H 1

#include <osgEarth/Common>
#include <osgEarth/GeoData>
#include <osgEarth/Threading>
#include <osgEarth/TileKey>
#include <osgEarth/GLUtils>

#include <osg/Camera>
#include <osg/Texture2D>
#include <osgViewer/Viewer>
#include <osg/buffered_value>

#include <queue>
#include <list>

namespace osgEarth
{
    using namespace Threading;

    /**
    * Render node graphs to textures, one at a time.
    */
    class OSGEARTH_EXPORT TileRasterizer : public osg::Node
    {
    public:
        //! Construct a new tile rasterizer camera
        TileRasterizer(unsigned width, unsigned height);

        //! Number of renderers to create in each graphics context.
        //! Default (and minimum) is 3.
        void setNumRenderersPerGraphicsContext(unsigned num);

        //! Maximum number of rendering jobs to dispatch per frame,
        //! assuming renderers are available. Default is 1.
        void setNumJobsToDispatchPerFrame(unsigned num);

        /**
        * Schedule a rasterization to an osg::Image.
        * @param node Node to render to the image
        * @param size of the target image (both dimensions)
        * @param extent geospatial extent of the node to render.
        * @return Future image - blocks on .get() or .release()
        */
        Future<osg::ref_ptr<osg::Image>> render(
            osg::Node* node, 
            const GeoExtent& extent);

    public: // osg::Node

        void traverse(osg::NodeVisitor& nv) override;

        void releaseGLObjects(osg::State*) const override;

        void resizeGLObjectBuffers(unsigned) override;

    private:

        // Object that renders a node with an RTT camera.
        struct Renderer
        {
            enum Phase { RENDER, QUERY, READBACK };
            Phase _phase;

            using Ptr = std::shared_ptr<Renderer>;

            // Since the Renderer is already per-gc,
            // we do not need to per-gc these objects:
            mutable GLQuery::Ptr _query; // GL query object
            mutable GLBuffer::Ptr _pbo; // pixel buffer object
            mutable GLBuffer::Ptr _cbo; // copy buffer

            osg::ref_ptr<osg::Texture2D> _tex;
            osg::ref_ptr<osg::Camera> _rtt;
            UID _uid;
            GLsizei _dataSize;
            osg::Image* createImage() const;

            Renderer(unsigned width, unsigned height);
            //void preDraw(osg::State&);
            void releaseGLObjects(osg::State*) const;
            void resizeGLObjectBuffers(unsigned);

            void allocate(osg::State&);
            GLuint query(osg::State&);
            osg::ref_ptr<osg::Image> readback(osg::State&);
        };

        // One tile-rendering job
        struct Job
        {
            using Ptr = std::shared_ptr<Job>;
            using Result = osg::ref_ptr<osg::Image>;

            UID _uid;
            osg::ref_ptr<osg::Node> _node; // node to render
            GeoExtent _extent; // viewport extent of the node
            Renderer::Ptr _renderer; // Rtt camera to use to renderer

            jobs::promise<Result> _promise;

            void useRenderer(Renderer::Ptr);
        };

        // separate collection of renderers for each state-based graphics context
        struct GLObjects : public PerStateGLObjects
        {
            using Ptr = std::shared_ptr<GLObjects>;
            RoundRobin<Renderer::Ptr> _renderers;
            MutexedQueue<Job::Ptr> _renderQ;
        };
        mutable osg::buffered_object<GLObjects::Ptr> _globjects;

        // Jobs waiting to run
        MutexedQueue<Job::Ptr> _jobQ;

        // simple adapter to make draw callbacks take lambdas
        struct DrawCallback : public osg::Camera::DrawCallback
        {
            DrawCallback(std::function<void(osg::RenderInfo&)> func) : _func(func) { }
            std::function<void(osg::RenderInfo&)> _func;
            void operator()(osg::RenderInfo& ri) const override { _func(ri); }
            friend class TileRasterizer;
        };

        //void preDraw(osg::RenderInfo& ri);
        void postDraw(osg::RenderInfo& ri);

        void install(GLObjects::Ptr);
        osg::ref_ptr<osg::StateSet> _rttStateSet;
        unsigned _width, _height;
        unsigned _numRenderersPerGC = 3u;
        unsigned _numJobsToDispatchPerFrame = 1u;
    };

} // namespace osgEarth

#endif // OSGEARTH_TILE_RASTERIZER_H
