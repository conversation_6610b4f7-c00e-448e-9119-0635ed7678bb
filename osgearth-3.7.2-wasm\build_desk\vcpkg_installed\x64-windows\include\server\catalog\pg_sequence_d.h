/*-------------------------------------------------------------------------
 *
 * pg_sequence_d.h
 *    Macro definitions for pg_sequence
 *
 * Portions Copyright (c) 1996-2023, PostgreSQL Global Development Group
 * Portions Copyright (c) 1994, Regents of the University of California
 *
 * NOTES
 *  ******************************
 *  *** DO NOT EDIT THIS FILE! ***
 *  ******************************
 *
 *  It has been GENERATED by src/backend/catalog/genbki.pl
 *
 *-------------------------------------------------------------------------
 */
#ifndef PG_SEQUENCE_D_H
#define PG_SEQUENCE_D_H

#define SequenceRelationId 2224
#define SequenceRelidIndexId 5002

#define Anum_pg_sequence_seqrelid 1
#define Anum_pg_sequence_seqtypid 2
#define Anum_pg_sequence_seqstart 3
#define Anum_pg_sequence_seqincrement 4
#define Anum_pg_sequence_seqmax 5
#define Anum_pg_sequence_seqmin 6
#define Anum_pg_sequence_seqcache 7
#define Anum_pg_sequence_seqcycle 8

#define Natts_pg_sequence 8


#endif							/* PG_SEQUENCE_D_H */
