// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: vector_tile.proto
// Protobuf C++ Version: 5.29.3

#ifndef vector_5ftile_2eproto_2epb_2eh
#define vector_5ftile_2eproto_2epb_2eh

#include <limits>
#include <string>
#include <type_traits>
#include <utility>

#include "google/protobuf/runtime_version.h"
#if PROTOBUF_VERSION != 5029003
#error "Protobuf C++ gencode is built with an incompatible version of"
#error "Protobuf C++ headers/runtime. See"
#error "https://protobuf.dev/support/cross-version-runtime-guarantee/#cpp"
#endif
#include "google/protobuf/io/coded_stream.h"
#include "google/protobuf/arena.h"
#include "google/protobuf/arenastring.h"
#include "google/protobuf/generated_message_tctable_decl.h"
#include "google/protobuf/generated_message_util.h"
#include "google/protobuf/metadata_lite.h"
#include "google/protobuf/message_lite.h"
#include "google/protobuf/repeated_field.h"  // IWYU pragma: export
#include "google/protobuf/extension_set.h"  // IWYU pragma: export
#include "google/protobuf/generated_enum_util.h"
// @@protoc_insertion_point(includes)

// Must be included last.
#include "google/protobuf/port_def.inc"

#define PROTOBUF_INTERNAL_EXPORT_vector_5ftile_2eproto

namespace google {
namespace protobuf {
namespace internal {
template <typename T>
::absl::string_view GetAnyMessageName();
}  // namespace internal
}  // namespace protobuf
}  // namespace google

// Internal implementation detail -- do not use these members.
struct TableStruct_vector_5ftile_2eproto {
  static const ::uint32_t offsets[];
};
namespace mapnik {
namespace vector {
class tile;
struct tileDefaultTypeInternal;
extern tileDefaultTypeInternal _tile_default_instance_;
class tile_feature;
struct tile_featureDefaultTypeInternal;
extern tile_featureDefaultTypeInternal _tile_feature_default_instance_;
class tile_layer;
struct tile_layerDefaultTypeInternal;
extern tile_layerDefaultTypeInternal _tile_layer_default_instance_;
class tile_value;
struct tile_valueDefaultTypeInternal;
extern tile_valueDefaultTypeInternal _tile_value_default_instance_;
}  // namespace vector
}  // namespace mapnik
namespace google {
namespace protobuf {
}  // namespace protobuf
}  // namespace google

namespace mapnik {
namespace vector {
enum tile_GeomType : int {
  tile_GeomType_Unknown = 0,
  tile_GeomType_Point = 1,
  tile_GeomType_LineString = 2,
  tile_GeomType_Polygon = 3,
};

bool tile_GeomType_IsValid(int value);
extern const uint32_t tile_GeomType_internal_data_[];
constexpr tile_GeomType tile_GeomType_GeomType_MIN = static_cast<tile_GeomType>(0);
constexpr tile_GeomType tile_GeomType_GeomType_MAX = static_cast<tile_GeomType>(3);
constexpr int tile_GeomType_GeomType_ARRAYSIZE = 3 + 1;
const std::string& tile_GeomType_Name(tile_GeomType value);
template <typename T>
const std::string& tile_GeomType_Name(T value) {
  static_assert(std::is_same<T, tile_GeomType>::value ||
                    std::is_integral<T>::value,
                "Incorrect type passed to GeomType_Name().");
  return tile_GeomType_Name(static_cast<tile_GeomType>(value));
}
bool tile_GeomType_Parse(absl::string_view name, tile_GeomType* value);

// ===================================================================


// -------------------------------------------------------------------

class tile_value final : public ::google::protobuf::MessageLite
/* @@protoc_insertion_point(class_definition:mapnik.vector.tile.value) */ {
 public:
  inline tile_value() : tile_value(nullptr) {}
  ~tile_value() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(tile_value* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(tile_value));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR tile_value(
      ::google::protobuf::internal::ConstantInitialized);

  inline tile_value(const tile_value& from) : tile_value(nullptr, from) {}
  inline tile_value(tile_value&& from) noexcept
      : tile_value(nullptr, std::move(from)) {}
  inline tile_value& operator=(const tile_value& from) {
    CopyFrom(from);
    return *this;
  }
  inline tile_value& operator=(tile_value&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const tile_value& default_instance() {
    return *internal_default_instance();
  }
  static inline const tile_value* internal_default_instance() {
    return reinterpret_cast<const tile_value*>(
        &_tile_value_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 0;
  friend void swap(tile_value& a, tile_value& b) { a.Swap(&b); }
  inline void Swap(tile_value* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(tile_value* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  tile_value* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::MessageLite::DefaultConstruct<tile_value>(arena);
  }
  void CopyFrom(const tile_value& from);
  void MergeFrom(const tile_value& from) { tile_value::MergeImpl(*this, from); }

  private:
  static void MergeImpl(::google::protobuf::MessageLite& to_msg,
                        const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return IsInitializedImpl(*this);
  }

  private:
  static bool IsInitializedImpl(const MessageLite& msg);

  public:
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(tile_value* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "mapnik.vector.tile.value"; }

 protected:
  explicit tile_value(::google::protobuf::Arena* arena);
  tile_value(::google::protobuf::Arena* arena, const tile_value& from);
  tile_value(::google::protobuf::Arena* arena, tile_value&& from) noexcept
      : tile_value(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataLite<25> _class_data_;

 public:
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kStringValueFieldNumber = 1,
    kDoubleValueFieldNumber = 3,
    kIntValueFieldNumber = 4,
    kFloatValueFieldNumber = 2,
    kBoolValueFieldNumber = 7,
    kUintValueFieldNumber = 5,
    kSintValueFieldNumber = 6,
  };
  // optional string string_value = 1;
  bool has_string_value() const;
  void clear_string_value() ;
  const std::string& string_value() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_string_value(Arg_&& arg, Args_... args);
  std::string* mutable_string_value();
  PROTOBUF_NODISCARD std::string* release_string_value();
  void set_allocated_string_value(std::string* value);

  private:
  const std::string& _internal_string_value() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_string_value(
      const std::string& value);
  std::string* _internal_mutable_string_value();

  public:
  // optional double double_value = 3;
  bool has_double_value() const;
  void clear_double_value() ;
  double double_value() const;
  void set_double_value(double value);

  private:
  double _internal_double_value() const;
  void _internal_set_double_value(double value);

  public:
  // optional int64 int_value = 4;
  bool has_int_value() const;
  void clear_int_value() ;
  ::int64_t int_value() const;
  void set_int_value(::int64_t value);

  private:
  ::int64_t _internal_int_value() const;
  void _internal_set_int_value(::int64_t value);

  public:
  // optional float float_value = 2;
  bool has_float_value() const;
  void clear_float_value() ;
  float float_value() const;
  void set_float_value(float value);

  private:
  float _internal_float_value() const;
  void _internal_set_float_value(float value);

  public:
  // optional bool bool_value = 7;
  bool has_bool_value() const;
  void clear_bool_value() ;
  bool bool_value() const;
  void set_bool_value(bool value);

  private:
  bool _internal_bool_value() const;
  void _internal_set_bool_value(bool value);

  public:
  // optional uint64 uint_value = 5;
  bool has_uint_value() const;
  void clear_uint_value() ;
  ::uint64_t uint_value() const;
  void set_uint_value(::uint64_t value);

  private:
  ::uint64_t _internal_uint_value() const;
  void _internal_set_uint_value(::uint64_t value);

  public:
  // optional sint64 sint_value = 6;
  bool has_sint_value() const;
  void clear_sint_value() ;
  ::int64_t sint_value() const;
  void set_sint_value(::int64_t value);

  private:
  ::int64_t _internal_sint_value() const;
  void _internal_set_sint_value(::int64_t value);

  public:
  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed,
            typename = typename _proto_TypeTraits::Singular>
  inline bool HasExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_value, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) const {
    return _impl_._extensions_.Has(id.number());
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline void ClearExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_value, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) {
    _impl_._extensions_.ClearExtension(id.number());
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed,
            typename = typename _proto_TypeTraits::Repeated>
  inline int ExtensionSize(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_value, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) const {
    return _impl_._extensions_.ExtensionSize(id.number());
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed,
            std::enable_if_t<!_proto_TypeTraits::kLifetimeBound, int> = 0>
  inline typename _proto_TypeTraits::Singular::ConstType GetExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_value, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) const {
    return _proto_TypeTraits::Get(id.number(), _impl_._extensions_, id.default_value());
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed,
            std::enable_if_t<_proto_TypeTraits::kLifetimeBound, int> = 0>
  inline typename _proto_TypeTraits::Singular::ConstType GetExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_value, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _proto_TypeTraits::Get(id.number(), _impl_._extensions_, id.default_value());
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Singular::MutableType MutableExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_value, _proto_TypeTraits,
                                       _field_type, _is_packed>& id)
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _proto_TypeTraits::Mutable(id.number(), _field_type, &_impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline void SetExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_value, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      typename _proto_TypeTraits::Singular::ConstType value) {
    _proto_TypeTraits::Set(id.number(), _field_type, value, &_impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline void SetAllocatedExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_value, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      typename _proto_TypeTraits::Singular::MutableType value) {
    _proto_TypeTraits::SetAllocated(id.number(), _field_type, value,
                                    &_impl_._extensions_);
  }
  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline void UnsafeArenaSetAllocatedExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_value, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      typename _proto_TypeTraits::Singular::MutableType value) {
    _proto_TypeTraits::UnsafeArenaSetAllocated(id.number(), _field_type,
                                               value, &_impl_._extensions_);
  }
  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  PROTOBUF_NODISCARD inline
      typename _proto_TypeTraits::Singular::MutableType
      ReleaseExtension(
          const ::google::protobuf::internal::ExtensionIdentifier<tile_value, _proto_TypeTraits,
                                           _field_type, _is_packed>& id) {
    return _proto_TypeTraits::Release(id.number(), _field_type, &_impl_._extensions_);
  }
  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Singular::MutableType
  UnsafeArenaReleaseExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_value, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) {
    return _proto_TypeTraits::UnsafeArenaRelease(id.number(), _field_type,
                                                 &_impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed,
            std::enable_if_t<!_proto_TypeTraits::kLifetimeBound, int> = 0>
  inline typename _proto_TypeTraits::Repeated::ConstType GetExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_value, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      int index) const {
    return _proto_TypeTraits::Get(id.number(), _impl_._extensions_, index);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed,
            std::enable_if_t<_proto_TypeTraits::kLifetimeBound, int> = 0>
  inline typename _proto_TypeTraits::Repeated::ConstType GetExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_value, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      int index) const ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _proto_TypeTraits::Get(id.number(), _impl_._extensions_, index);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Repeated::MutableType MutableExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_value, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      int index) ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _proto_TypeTraits::Mutable(id.number(), index, &_impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline void SetExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_value, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      int index, typename _proto_TypeTraits::Repeated::ConstType value) {
    _proto_TypeTraits::Set(id.number(), index, value, &_impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Repeated::MutableType AddExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_value, _proto_TypeTraits,
                                       _field_type, _is_packed>& id)
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    typename _proto_TypeTraits::Repeated::MutableType to_add =
        _proto_TypeTraits::Add(id.number(), _field_type, &_impl_._extensions_);
    return to_add;
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline void AddExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_value, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      typename _proto_TypeTraits::Repeated::ConstType value) {
    _proto_TypeTraits::Add(id.number(), _field_type, _is_packed, value,
                           &_impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline const typename _proto_TypeTraits::Repeated::RepeatedFieldType&
  GetRepeatedExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_value, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _proto_TypeTraits::GetRepeated(id.number(), _impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Repeated::RepeatedFieldType*
  MutableRepeatedExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_value, _proto_TypeTraits,
                                       _field_type, _is_packed>& id)
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _proto_TypeTraits::MutableRepeated(id.number(), _field_type,
                                              _is_packed, &_impl_._extensions_);
  }
  // @@protoc_insertion_point(class_scope:mapnik.vector.tile.value)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      3, 7, 0,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const tile_value& from_msg);
    ::google::protobuf::internal::ExtensionSet _extensions_;
    ::google::protobuf::internal::HasBits<1> _has_bits_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    ::google::protobuf::internal::ArenaStringPtr string_value_;
    double double_value_;
    ::int64_t int_value_;
    float float_value_;
    bool bool_value_;
    ::uint64_t uint_value_;
    ::int64_t sint_value_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_vector_5ftile_2eproto;
};
// -------------------------------------------------------------------

class tile_feature final : public ::google::protobuf::MessageLite
/* @@protoc_insertion_point(class_definition:mapnik.vector.tile.feature) */ {
 public:
  inline tile_feature() : tile_feature(nullptr) {}
  ~tile_feature() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(tile_feature* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(tile_feature));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR tile_feature(
      ::google::protobuf::internal::ConstantInitialized);

  inline tile_feature(const tile_feature& from) : tile_feature(nullptr, from) {}
  inline tile_feature(tile_feature&& from) noexcept
      : tile_feature(nullptr, std::move(from)) {}
  inline tile_feature& operator=(const tile_feature& from) {
    CopyFrom(from);
    return *this;
  }
  inline tile_feature& operator=(tile_feature&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const tile_feature& default_instance() {
    return *internal_default_instance();
  }
  static inline const tile_feature* internal_default_instance() {
    return reinterpret_cast<const tile_feature*>(
        &_tile_feature_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 1;
  friend void swap(tile_feature& a, tile_feature& b) { a.Swap(&b); }
  inline void Swap(tile_feature* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(tile_feature* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  tile_feature* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::MessageLite::DefaultConstruct<tile_feature>(arena);
  }
  void CopyFrom(const tile_feature& from);
  void MergeFrom(const tile_feature& from) { tile_feature::MergeImpl(*this, from); }

  private:
  static void MergeImpl(::google::protobuf::MessageLite& to_msg,
                        const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return true;
  }
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(tile_feature* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "mapnik.vector.tile.feature"; }

 protected:
  explicit tile_feature(::google::protobuf::Arena* arena);
  tile_feature(::google::protobuf::Arena* arena, const tile_feature& from);
  tile_feature(::google::protobuf::Arena* arena, tile_feature&& from) noexcept
      : tile_feature(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataLite<27> _class_data_;

 public:
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kTagsFieldNumber = 2,
    kGeometryFieldNumber = 4,
    kIdFieldNumber = 1,
    kTypeFieldNumber = 3,
  };
  // repeated uint32 tags = 2 [packed = true];
  int tags_size() const;
  private:
  int _internal_tags_size() const;

  public:
  void clear_tags() ;
  ::uint32_t tags(int index) const;
  void set_tags(int index, ::uint32_t value);
  void add_tags(::uint32_t value);
  const ::google::protobuf::RepeatedField<::uint32_t>& tags() const;
  ::google::protobuf::RepeatedField<::uint32_t>* mutable_tags();

  private:
  const ::google::protobuf::RepeatedField<::uint32_t>& _internal_tags() const;
  ::google::protobuf::RepeatedField<::uint32_t>* _internal_mutable_tags();

  public:
  // repeated uint32 geometry = 4 [packed = true];
  int geometry_size() const;
  private:
  int _internal_geometry_size() const;

  public:
  void clear_geometry() ;
  ::uint32_t geometry(int index) const;
  void set_geometry(int index, ::uint32_t value);
  void add_geometry(::uint32_t value);
  const ::google::protobuf::RepeatedField<::uint32_t>& geometry() const;
  ::google::protobuf::RepeatedField<::uint32_t>* mutable_geometry();

  private:
  const ::google::protobuf::RepeatedField<::uint32_t>& _internal_geometry() const;
  ::google::protobuf::RepeatedField<::uint32_t>* _internal_mutable_geometry();

  public:
  // optional uint64 id = 1;
  bool has_id() const;
  void clear_id() ;
  ::uint64_t id() const;
  void set_id(::uint64_t value);

  private:
  ::uint64_t _internal_id() const;
  void _internal_set_id(::uint64_t value);

  public:
  // optional .mapnik.vector.tile.GeomType type = 3 [default = Unknown];
  bool has_type() const;
  void clear_type() ;
  ::mapnik::vector::tile_GeomType type() const;
  void set_type(::mapnik::vector::tile_GeomType value);

  private:
  ::mapnik::vector::tile_GeomType _internal_type() const;
  void _internal_set_type(::mapnik::vector::tile_GeomType value);

  public:
  // @@protoc_insertion_point(class_scope:mapnik.vector.tile.feature)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      2, 4, 1,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const tile_feature& from_msg);
    ::google::protobuf::internal::HasBits<1> _has_bits_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    ::google::protobuf::RepeatedField<::uint32_t> tags_;
    ::google::protobuf::internal::CachedSize _tags_cached_byte_size_;
    ::google::protobuf::RepeatedField<::uint32_t> geometry_;
    ::google::protobuf::internal::CachedSize _geometry_cached_byte_size_;
    ::uint64_t id_;
    int type_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_vector_5ftile_2eproto;
};
// -------------------------------------------------------------------

class tile_layer final : public ::google::protobuf::MessageLite
/* @@protoc_insertion_point(class_definition:mapnik.vector.tile.layer) */ {
 public:
  inline tile_layer() : tile_layer(nullptr) {}
  ~tile_layer() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(tile_layer* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(tile_layer));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR tile_layer(
      ::google::protobuf::internal::ConstantInitialized);

  inline tile_layer(const tile_layer& from) : tile_layer(nullptr, from) {}
  inline tile_layer(tile_layer&& from) noexcept
      : tile_layer(nullptr, std::move(from)) {}
  inline tile_layer& operator=(const tile_layer& from) {
    CopyFrom(from);
    return *this;
  }
  inline tile_layer& operator=(tile_layer&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const tile_layer& default_instance() {
    return *internal_default_instance();
  }
  static inline const tile_layer* internal_default_instance() {
    return reinterpret_cast<const tile_layer*>(
        &_tile_layer_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 2;
  friend void swap(tile_layer& a, tile_layer& b) { a.Swap(&b); }
  inline void Swap(tile_layer* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(tile_layer* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  tile_layer* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::MessageLite::DefaultConstruct<tile_layer>(arena);
  }
  void CopyFrom(const tile_layer& from);
  void MergeFrom(const tile_layer& from) { tile_layer::MergeImpl(*this, from); }

  private:
  static void MergeImpl(::google::protobuf::MessageLite& to_msg,
                        const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return IsInitializedImpl(*this);
  }

  private:
  static bool IsInitializedImpl(const MessageLite& msg);

  public:
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(tile_layer* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "mapnik.vector.tile.layer"; }

 protected:
  explicit tile_layer(::google::protobuf::Arena* arena);
  tile_layer(::google::protobuf::Arena* arena, const tile_layer& from);
  tile_layer(::google::protobuf::Arena* arena, tile_layer&& from) noexcept
      : tile_layer(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataLite<25> _class_data_;

 public:
  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------
  enum : int {
    kFeaturesFieldNumber = 2,
    kKeysFieldNumber = 3,
    kValuesFieldNumber = 4,
    kNameFieldNumber = 1,
    kExtentFieldNumber = 5,
    kVersionFieldNumber = 15,
  };
  // repeated .mapnik.vector.tile.feature features = 2;
  int features_size() const;
  private:
  int _internal_features_size() const;

  public:
  void clear_features() ;
  ::mapnik::vector::tile_feature* mutable_features(int index);
  ::google::protobuf::RepeatedPtrField<::mapnik::vector::tile_feature>* mutable_features();

  private:
  const ::google::protobuf::RepeatedPtrField<::mapnik::vector::tile_feature>& _internal_features() const;
  ::google::protobuf::RepeatedPtrField<::mapnik::vector::tile_feature>* _internal_mutable_features();
  public:
  const ::mapnik::vector::tile_feature& features(int index) const;
  ::mapnik::vector::tile_feature* add_features();
  const ::google::protobuf::RepeatedPtrField<::mapnik::vector::tile_feature>& features() const;
  // repeated string keys = 3;
  int keys_size() const;
  private:
  int _internal_keys_size() const;

  public:
  void clear_keys() ;
  const std::string& keys(int index) const;
  std::string* mutable_keys(int index);
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_keys(int index, Arg_&& value, Args_... args);
  std::string* add_keys();
  template <typename Arg_ = const std::string&, typename... Args_>
  void add_keys(Arg_&& value, Args_... args);
  const ::google::protobuf::RepeatedPtrField<std::string>& keys() const;
  ::google::protobuf::RepeatedPtrField<std::string>* mutable_keys();

  private:
  const ::google::protobuf::RepeatedPtrField<std::string>& _internal_keys() const;
  ::google::protobuf::RepeatedPtrField<std::string>* _internal_mutable_keys();

  public:
  // repeated .mapnik.vector.tile.value values = 4;
  int values_size() const;
  private:
  int _internal_values_size() const;

  public:
  void clear_values() ;
  ::mapnik::vector::tile_value* mutable_values(int index);
  ::google::protobuf::RepeatedPtrField<::mapnik::vector::tile_value>* mutable_values();

  private:
  const ::google::protobuf::RepeatedPtrField<::mapnik::vector::tile_value>& _internal_values() const;
  ::google::protobuf::RepeatedPtrField<::mapnik::vector::tile_value>* _internal_mutable_values();
  public:
  const ::mapnik::vector::tile_value& values(int index) const;
  ::mapnik::vector::tile_value* add_values();
  const ::google::protobuf::RepeatedPtrField<::mapnik::vector::tile_value>& values() const;
  // required string name = 1;
  bool has_name() const;
  void clear_name() ;
  const std::string& name() const;
  template <typename Arg_ = const std::string&, typename... Args_>
  void set_name(Arg_&& arg, Args_... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* value);

  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(
      const std::string& value);
  std::string* _internal_mutable_name();

  public:
  // optional uint32 extent = 5 [default = 4096];
  bool has_extent() const;
  void clear_extent() ;
  ::uint32_t extent() const;
  void set_extent(::uint32_t value);

  private:
  ::uint32_t _internal_extent() const;
  void _internal_set_extent(::uint32_t value);

  public:
  // required uint32 version = 15 [default = 1];
  bool has_version() const;
  void clear_version() ;
  ::uint32_t version() const;
  void set_version(::uint32_t value);

  private:
  ::uint32_t _internal_version() const;
  void _internal_set_version(::uint32_t value);

  public:
  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed,
            typename = typename _proto_TypeTraits::Singular>
  inline bool HasExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_layer, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) const {
    return _impl_._extensions_.Has(id.number());
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline void ClearExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_layer, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) {
    _impl_._extensions_.ClearExtension(id.number());
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed,
            typename = typename _proto_TypeTraits::Repeated>
  inline int ExtensionSize(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_layer, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) const {
    return _impl_._extensions_.ExtensionSize(id.number());
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed,
            std::enable_if_t<!_proto_TypeTraits::kLifetimeBound, int> = 0>
  inline typename _proto_TypeTraits::Singular::ConstType GetExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_layer, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) const {
    return _proto_TypeTraits::Get(id.number(), _impl_._extensions_, id.default_value());
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed,
            std::enable_if_t<_proto_TypeTraits::kLifetimeBound, int> = 0>
  inline typename _proto_TypeTraits::Singular::ConstType GetExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_layer, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _proto_TypeTraits::Get(id.number(), _impl_._extensions_, id.default_value());
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Singular::MutableType MutableExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_layer, _proto_TypeTraits,
                                       _field_type, _is_packed>& id)
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _proto_TypeTraits::Mutable(id.number(), _field_type, &_impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline void SetExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_layer, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      typename _proto_TypeTraits::Singular::ConstType value) {
    _proto_TypeTraits::Set(id.number(), _field_type, value, &_impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline void SetAllocatedExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_layer, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      typename _proto_TypeTraits::Singular::MutableType value) {
    _proto_TypeTraits::SetAllocated(id.number(), _field_type, value,
                                    &_impl_._extensions_);
  }
  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline void UnsafeArenaSetAllocatedExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_layer, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      typename _proto_TypeTraits::Singular::MutableType value) {
    _proto_TypeTraits::UnsafeArenaSetAllocated(id.number(), _field_type,
                                               value, &_impl_._extensions_);
  }
  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  PROTOBUF_NODISCARD inline
      typename _proto_TypeTraits::Singular::MutableType
      ReleaseExtension(
          const ::google::protobuf::internal::ExtensionIdentifier<tile_layer, _proto_TypeTraits,
                                           _field_type, _is_packed>& id) {
    return _proto_TypeTraits::Release(id.number(), _field_type, &_impl_._extensions_);
  }
  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Singular::MutableType
  UnsafeArenaReleaseExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_layer, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) {
    return _proto_TypeTraits::UnsafeArenaRelease(id.number(), _field_type,
                                                 &_impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed,
            std::enable_if_t<!_proto_TypeTraits::kLifetimeBound, int> = 0>
  inline typename _proto_TypeTraits::Repeated::ConstType GetExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_layer, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      int index) const {
    return _proto_TypeTraits::Get(id.number(), _impl_._extensions_, index);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed,
            std::enable_if_t<_proto_TypeTraits::kLifetimeBound, int> = 0>
  inline typename _proto_TypeTraits::Repeated::ConstType GetExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_layer, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      int index) const ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _proto_TypeTraits::Get(id.number(), _impl_._extensions_, index);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Repeated::MutableType MutableExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_layer, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      int index) ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _proto_TypeTraits::Mutable(id.number(), index, &_impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline void SetExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_layer, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      int index, typename _proto_TypeTraits::Repeated::ConstType value) {
    _proto_TypeTraits::Set(id.number(), index, value, &_impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Repeated::MutableType AddExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_layer, _proto_TypeTraits,
                                       _field_type, _is_packed>& id)
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    typename _proto_TypeTraits::Repeated::MutableType to_add =
        _proto_TypeTraits::Add(id.number(), _field_type, &_impl_._extensions_);
    return to_add;
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline void AddExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_layer, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      typename _proto_TypeTraits::Repeated::ConstType value) {
    _proto_TypeTraits::Add(id.number(), _field_type, _is_packed, value,
                           &_impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline const typename _proto_TypeTraits::Repeated::RepeatedFieldType&
  GetRepeatedExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_layer, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _proto_TypeTraits::GetRepeated(id.number(), _impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Repeated::RepeatedFieldType*
  MutableRepeatedExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile_layer, _proto_TypeTraits,
                                       _field_type, _is_packed>& id)
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _proto_TypeTraits::MutableRepeated(id.number(), _field_type,
                                              _is_packed, &_impl_._extensions_);
  }
  // @@protoc_insertion_point(class_scope:mapnik.vector.tile.layer)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      3, 6, 2,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const tile_layer& from_msg);
    ::google::protobuf::internal::ExtensionSet _extensions_;
    ::google::protobuf::internal::HasBits<1> _has_bits_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    ::google::protobuf::RepeatedPtrField< ::mapnik::vector::tile_feature > features_;
    ::google::protobuf::RepeatedPtrField<std::string> keys_;
    ::google::protobuf::RepeatedPtrField< ::mapnik::vector::tile_value > values_;
    ::google::protobuf::internal::ArenaStringPtr name_;
    ::uint32_t extent_;
    ::uint32_t version_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_vector_5ftile_2eproto;
};
// -------------------------------------------------------------------

class tile final : public ::google::protobuf::MessageLite
/* @@protoc_insertion_point(class_definition:mapnik.vector.tile) */ {
 public:
  inline tile() : tile(nullptr) {}
  ~tile() PROTOBUF_FINAL;

#if defined(PROTOBUF_CUSTOM_VTABLE)
  void operator delete(tile* msg, std::destroying_delete_t) {
    SharedDtor(*msg);
    ::google::protobuf::internal::SizedDelete(msg, sizeof(tile));
  }
#endif

  template <typename = void>
  explicit PROTOBUF_CONSTEXPR tile(
      ::google::protobuf::internal::ConstantInitialized);

  inline tile(const tile& from) : tile(nullptr, from) {}
  inline tile(tile&& from) noexcept
      : tile(nullptr, std::move(from)) {}
  inline tile& operator=(const tile& from) {
    CopyFrom(from);
    return *this;
  }
  inline tile& operator=(tile&& from) noexcept {
    if (this == &from) return *this;
    if (::google::protobuf::internal::CanMoveWithInternalSwap(GetArena(), from.GetArena())) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.unknown_fields<std::string>(::google::protobuf::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields()
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const tile& default_instance() {
    return *internal_default_instance();
  }
  static inline const tile* internal_default_instance() {
    return reinterpret_cast<const tile*>(
        &_tile_default_instance_);
  }
  static constexpr int kIndexInFileMessages = 3;
  friend void swap(tile& a, tile& b) { a.Swap(&b); }
  inline void Swap(tile* other) {
    if (other == this) return;
    if (::google::protobuf::internal::CanUseInternalSwap(GetArena(), other->GetArena())) {
      InternalSwap(other);
    } else {
      ::google::protobuf::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(tile* other) {
    if (other == this) return;
    ABSL_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  tile* New(::google::protobuf::Arena* arena = nullptr) const {
    return ::google::protobuf::MessageLite::DefaultConstruct<tile>(arena);
  }
  void CopyFrom(const tile& from);
  void MergeFrom(const tile& from) { tile::MergeImpl(*this, from); }

  private:
  static void MergeImpl(::google::protobuf::MessageLite& to_msg,
                        const ::google::protobuf::MessageLite& from_msg);

  public:
  bool IsInitialized() const {
    return IsInitializedImpl(*this);
  }

  private:
  static bool IsInitializedImpl(const MessageLite& msg);

  public:
  ABSL_ATTRIBUTE_REINITIALIZES void Clear() PROTOBUF_FINAL;
  #if defined(PROTOBUF_CUSTOM_VTABLE)
  private:
  static ::size_t ByteSizeLong(const ::google::protobuf::MessageLite& msg);
  static ::uint8_t* _InternalSerialize(
      const MessageLite& msg, ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream);

  public:
  ::size_t ByteSizeLong() const { return ByteSizeLong(*this); }
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const {
    return _InternalSerialize(*this, target, stream);
  }
  #else   // PROTOBUF_CUSTOM_VTABLE
  ::size_t ByteSizeLong() const final;
  ::uint8_t* _InternalSerialize(
      ::uint8_t* target,
      ::google::protobuf::io::EpsCopyOutputStream* stream) const final;
  #endif  // PROTOBUF_CUSTOM_VTABLE
  int GetCachedSize() const { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::google::protobuf::Arena* arena);
  static void SharedDtor(MessageLite& self);
  void InternalSwap(tile* other);
 private:
  template <typename T>
  friend ::absl::string_view(
      ::google::protobuf::internal::GetAnyMessageName)();
  static ::absl::string_view FullMessageName() { return "mapnik.vector.tile"; }

 protected:
  explicit tile(::google::protobuf::Arena* arena);
  tile(::google::protobuf::Arena* arena, const tile& from);
  tile(::google::protobuf::Arena* arena, tile&& from) noexcept
      : tile(arena) {
    *this = ::std::move(from);
  }
  const ::google::protobuf::internal::ClassData* GetClassData() const PROTOBUF_FINAL;
  static void* PlacementNew_(const void*, void* mem,
                             ::google::protobuf::Arena* arena);
  static constexpr auto InternalNewImpl_();
  static const ::google::protobuf::internal::ClassDataLite<19> _class_data_;

 public:
  // nested types ----------------------------------------------------
  using value = tile_value;
  using feature = tile_feature;
  using layer = tile_layer;
  using GeomType = tile_GeomType;
  static constexpr GeomType Unknown = tile_GeomType_Unknown;
  static constexpr GeomType Point = tile_GeomType_Point;
  static constexpr GeomType LineString = tile_GeomType_LineString;
  static constexpr GeomType Polygon = tile_GeomType_Polygon;
  static inline bool GeomType_IsValid(int value) {
    return tile_GeomType_IsValid(value);
  }
  static constexpr GeomType GeomType_MIN = tile_GeomType_GeomType_MIN;
  static constexpr GeomType GeomType_MAX = tile_GeomType_GeomType_MAX;
  static constexpr int GeomType_ARRAYSIZE = tile_GeomType_GeomType_ARRAYSIZE;
  template <typename T>
  static inline const std::string& GeomType_Name(T value) {
    return tile_GeomType_Name(value);
  }
  static inline bool GeomType_Parse(absl::string_view name, GeomType* value) {
    return tile_GeomType_Parse(name, value);
  }

  // accessors -------------------------------------------------------
  enum : int {
    kLayersFieldNumber = 3,
  };
  // repeated .mapnik.vector.tile.layer layers = 3;
  int layers_size() const;
  private:
  int _internal_layers_size() const;

  public:
  void clear_layers() ;
  ::mapnik::vector::tile_layer* mutable_layers(int index);
  ::google::protobuf::RepeatedPtrField<::mapnik::vector::tile_layer>* mutable_layers();

  private:
  const ::google::protobuf::RepeatedPtrField<::mapnik::vector::tile_layer>& _internal_layers() const;
  ::google::protobuf::RepeatedPtrField<::mapnik::vector::tile_layer>* _internal_mutable_layers();
  public:
  const ::mapnik::vector::tile_layer& layers(int index) const;
  ::mapnik::vector::tile_layer* add_layers();
  const ::google::protobuf::RepeatedPtrField<::mapnik::vector::tile_layer>& layers() const;
  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed,
            typename = typename _proto_TypeTraits::Singular>
  inline bool HasExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) const {
    return _impl_._extensions_.Has(id.number());
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline void ClearExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) {
    _impl_._extensions_.ClearExtension(id.number());
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed,
            typename = typename _proto_TypeTraits::Repeated>
  inline int ExtensionSize(
      const ::google::protobuf::internal::ExtensionIdentifier<tile, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) const {
    return _impl_._extensions_.ExtensionSize(id.number());
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed,
            std::enable_if_t<!_proto_TypeTraits::kLifetimeBound, int> = 0>
  inline typename _proto_TypeTraits::Singular::ConstType GetExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) const {
    return _proto_TypeTraits::Get(id.number(), _impl_._extensions_, id.default_value());
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed,
            std::enable_if_t<_proto_TypeTraits::kLifetimeBound, int> = 0>
  inline typename _proto_TypeTraits::Singular::ConstType GetExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _proto_TypeTraits::Get(id.number(), _impl_._extensions_, id.default_value());
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Singular::MutableType MutableExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile, _proto_TypeTraits,
                                       _field_type, _is_packed>& id)
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _proto_TypeTraits::Mutable(id.number(), _field_type, &_impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline void SetExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      typename _proto_TypeTraits::Singular::ConstType value) {
    _proto_TypeTraits::Set(id.number(), _field_type, value, &_impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline void SetAllocatedExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      typename _proto_TypeTraits::Singular::MutableType value) {
    _proto_TypeTraits::SetAllocated(id.number(), _field_type, value,
                                    &_impl_._extensions_);
  }
  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline void UnsafeArenaSetAllocatedExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      typename _proto_TypeTraits::Singular::MutableType value) {
    _proto_TypeTraits::UnsafeArenaSetAllocated(id.number(), _field_type,
                                               value, &_impl_._extensions_);
  }
  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  PROTOBUF_NODISCARD inline
      typename _proto_TypeTraits::Singular::MutableType
      ReleaseExtension(
          const ::google::protobuf::internal::ExtensionIdentifier<tile, _proto_TypeTraits,
                                           _field_type, _is_packed>& id) {
    return _proto_TypeTraits::Release(id.number(), _field_type, &_impl_._extensions_);
  }
  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Singular::MutableType
  UnsafeArenaReleaseExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) {
    return _proto_TypeTraits::UnsafeArenaRelease(id.number(), _field_type,
                                                 &_impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed,
            std::enable_if_t<!_proto_TypeTraits::kLifetimeBound, int> = 0>
  inline typename _proto_TypeTraits::Repeated::ConstType GetExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      int index) const {
    return _proto_TypeTraits::Get(id.number(), _impl_._extensions_, index);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed,
            std::enable_if_t<_proto_TypeTraits::kLifetimeBound, int> = 0>
  inline typename _proto_TypeTraits::Repeated::ConstType GetExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      int index) const ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _proto_TypeTraits::Get(id.number(), _impl_._extensions_, index);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Repeated::MutableType MutableExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      int index) ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _proto_TypeTraits::Mutable(id.number(), index, &_impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline void SetExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      int index, typename _proto_TypeTraits::Repeated::ConstType value) {
    _proto_TypeTraits::Set(id.number(), index, value, &_impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Repeated::MutableType AddExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile, _proto_TypeTraits,
                                       _field_type, _is_packed>& id)
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    typename _proto_TypeTraits::Repeated::MutableType to_add =
        _proto_TypeTraits::Add(id.number(), _field_type, &_impl_._extensions_);
    return to_add;
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline void AddExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile, _proto_TypeTraits,
                                       _field_type, _is_packed>& id,
      typename _proto_TypeTraits::Repeated::ConstType value) {
    _proto_TypeTraits::Add(id.number(), _field_type, _is_packed, value,
                           &_impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline const typename _proto_TypeTraits::Repeated::RepeatedFieldType&
  GetRepeatedExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile, _proto_TypeTraits,
                                       _field_type, _is_packed>& id) const
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _proto_TypeTraits::GetRepeated(id.number(), _impl_._extensions_);
  }

  template <typename _proto_TypeTraits, ::google::protobuf::internal::FieldType _field_type,
            bool _is_packed>
  inline typename _proto_TypeTraits::Repeated::RepeatedFieldType*
  MutableRepeatedExtension(
      const ::google::protobuf::internal::ExtensionIdentifier<tile, _proto_TypeTraits,
                                       _field_type, _is_packed>& id)
      ABSL_ATTRIBUTE_LIFETIME_BOUND {
    return _proto_TypeTraits::MutableRepeated(id.number(), _field_type,
                                              _is_packed, &_impl_._extensions_);
  }
  // @@protoc_insertion_point(class_scope:mapnik.vector.tile)
 private:
  class _Internal;
  friend class ::google::protobuf::internal::TcParser;
  static const ::google::protobuf::internal::TcParseTable<
      0, 1, 1,
      0, 2>
      _table_;

  friend class ::google::protobuf::MessageLite;
  friend class ::google::protobuf::Arena;
  template <typename T>
  friend class ::google::protobuf::Arena::InternalHelper;
  using InternalArenaConstructable_ = void;
  using DestructorSkippable_ = void;
  struct Impl_ {
    inline explicit constexpr Impl_(
        ::google::protobuf::internal::ConstantInitialized) noexcept;
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena);
    inline explicit Impl_(::google::protobuf::internal::InternalVisibility visibility,
                          ::google::protobuf::Arena* arena, const Impl_& from,
                          const tile& from_msg);
    ::google::protobuf::internal::ExtensionSet _extensions_;
    ::google::protobuf::RepeatedPtrField< ::mapnik::vector::tile_layer > layers_;
    ::google::protobuf::internal::CachedSize _cached_size_;
    PROTOBUF_TSAN_DECLARE_MEMBER
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_vector_5ftile_2eproto;
};

// ===================================================================




// ===================================================================


#ifdef __GNUC__
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// -------------------------------------------------------------------

// tile_value

// optional string string_value = 1;
inline bool tile_value::has_string_value() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline void tile_value::clear_string_value() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.string_value_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const std::string& tile_value::string_value() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:mapnik.vector.tile.value.string_value)
  return _internal_string_value();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void tile_value::set_string_value(Arg_&& arg,
                                                     Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.string_value_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:mapnik.vector.tile.value.string_value)
}
inline std::string* tile_value::mutable_string_value() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_string_value();
  // @@protoc_insertion_point(field_mutable:mapnik.vector.tile.value.string_value)
  return _s;
}
inline const std::string& tile_value::_internal_string_value() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.string_value_.Get();
}
inline void tile_value::_internal_set_string_value(const std::string& value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.string_value_.Set(value, GetArena());
}
inline std::string* tile_value::_internal_mutable_string_value() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_._has_bits_[0] |= 0x00000001u;
  return _impl_.string_value_.Mutable( GetArena());
}
inline std::string* tile_value::release_string_value() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:mapnik.vector.tile.value.string_value)
  if ((_impl_._has_bits_[0] & 0x00000001u) == 0) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000001u;
  auto* released = _impl_.string_value_.Release();
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString()) {
    _impl_.string_value_.Set("", GetArena());
  }
  return released;
}
inline void tile_value::set_allocated_string_value(std::string* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  _impl_.string_value_.SetAllocated(value, GetArena());
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString() && _impl_.string_value_.IsDefault()) {
    _impl_.string_value_.Set("", GetArena());
  }
  // @@protoc_insertion_point(field_set_allocated:mapnik.vector.tile.value.string_value)
}

// optional float float_value = 2;
inline bool tile_value::has_float_value() const {
  bool value = (_impl_._has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline void tile_value::clear_float_value() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.float_value_ = 0;
  _impl_._has_bits_[0] &= ~0x00000008u;
}
inline float tile_value::float_value() const {
  // @@protoc_insertion_point(field_get:mapnik.vector.tile.value.float_value)
  return _internal_float_value();
}
inline void tile_value::set_float_value(float value) {
  _internal_set_float_value(value);
  _impl_._has_bits_[0] |= 0x00000008u;
  // @@protoc_insertion_point(field_set:mapnik.vector.tile.value.float_value)
}
inline float tile_value::_internal_float_value() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.float_value_;
}
inline void tile_value::_internal_set_float_value(float value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.float_value_ = value;
}

// optional double double_value = 3;
inline bool tile_value::has_double_value() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline void tile_value::clear_double_value() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.double_value_ = 0;
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline double tile_value::double_value() const {
  // @@protoc_insertion_point(field_get:mapnik.vector.tile.value.double_value)
  return _internal_double_value();
}
inline void tile_value::set_double_value(double value) {
  _internal_set_double_value(value);
  _impl_._has_bits_[0] |= 0x00000002u;
  // @@protoc_insertion_point(field_set:mapnik.vector.tile.value.double_value)
}
inline double tile_value::_internal_double_value() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.double_value_;
}
inline void tile_value::_internal_set_double_value(double value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.double_value_ = value;
}

// optional int64 int_value = 4;
inline bool tile_value::has_int_value() const {
  bool value = (_impl_._has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline void tile_value::clear_int_value() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.int_value_ = ::int64_t{0};
  _impl_._has_bits_[0] &= ~0x00000004u;
}
inline ::int64_t tile_value::int_value() const {
  // @@protoc_insertion_point(field_get:mapnik.vector.tile.value.int_value)
  return _internal_int_value();
}
inline void tile_value::set_int_value(::int64_t value) {
  _internal_set_int_value(value);
  _impl_._has_bits_[0] |= 0x00000004u;
  // @@protoc_insertion_point(field_set:mapnik.vector.tile.value.int_value)
}
inline ::int64_t tile_value::_internal_int_value() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.int_value_;
}
inline void tile_value::_internal_set_int_value(::int64_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.int_value_ = value;
}

// optional uint64 uint_value = 5;
inline bool tile_value::has_uint_value() const {
  bool value = (_impl_._has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline void tile_value::clear_uint_value() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.uint_value_ = ::uint64_t{0u};
  _impl_._has_bits_[0] &= ~0x00000020u;
}
inline ::uint64_t tile_value::uint_value() const {
  // @@protoc_insertion_point(field_get:mapnik.vector.tile.value.uint_value)
  return _internal_uint_value();
}
inline void tile_value::set_uint_value(::uint64_t value) {
  _internal_set_uint_value(value);
  _impl_._has_bits_[0] |= 0x00000020u;
  // @@protoc_insertion_point(field_set:mapnik.vector.tile.value.uint_value)
}
inline ::uint64_t tile_value::_internal_uint_value() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.uint_value_;
}
inline void tile_value::_internal_set_uint_value(::uint64_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.uint_value_ = value;
}

// optional sint64 sint_value = 6;
inline bool tile_value::has_sint_value() const {
  bool value = (_impl_._has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline void tile_value::clear_sint_value() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.sint_value_ = ::int64_t{0};
  _impl_._has_bits_[0] &= ~0x00000040u;
}
inline ::int64_t tile_value::sint_value() const {
  // @@protoc_insertion_point(field_get:mapnik.vector.tile.value.sint_value)
  return _internal_sint_value();
}
inline void tile_value::set_sint_value(::int64_t value) {
  _internal_set_sint_value(value);
  _impl_._has_bits_[0] |= 0x00000040u;
  // @@protoc_insertion_point(field_set:mapnik.vector.tile.value.sint_value)
}
inline ::int64_t tile_value::_internal_sint_value() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.sint_value_;
}
inline void tile_value::_internal_set_sint_value(::int64_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.sint_value_ = value;
}

// optional bool bool_value = 7;
inline bool tile_value::has_bool_value() const {
  bool value = (_impl_._has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline void tile_value::clear_bool_value() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.bool_value_ = false;
  _impl_._has_bits_[0] &= ~0x00000010u;
}
inline bool tile_value::bool_value() const {
  // @@protoc_insertion_point(field_get:mapnik.vector.tile.value.bool_value)
  return _internal_bool_value();
}
inline void tile_value::set_bool_value(bool value) {
  _internal_set_bool_value(value);
  _impl_._has_bits_[0] |= 0x00000010u;
  // @@protoc_insertion_point(field_set:mapnik.vector.tile.value.bool_value)
}
inline bool tile_value::_internal_bool_value() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.bool_value_;
}
inline void tile_value::_internal_set_bool_value(bool value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.bool_value_ = value;
}

// -------------------------------------------------------------------

// tile_feature

// optional uint64 id = 1;
inline bool tile_feature::has_id() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline void tile_feature::clear_id() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.id_ = ::uint64_t{0u};
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline ::uint64_t tile_feature::id() const {
  // @@protoc_insertion_point(field_get:mapnik.vector.tile.feature.id)
  return _internal_id();
}
inline void tile_feature::set_id(::uint64_t value) {
  _internal_set_id(value);
  _impl_._has_bits_[0] |= 0x00000001u;
  // @@protoc_insertion_point(field_set:mapnik.vector.tile.feature.id)
}
inline ::uint64_t tile_feature::_internal_id() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.id_;
}
inline void tile_feature::_internal_set_id(::uint64_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.id_ = value;
}

// repeated uint32 tags = 2 [packed = true];
inline int tile_feature::_internal_tags_size() const {
  return _internal_tags().size();
}
inline int tile_feature::tags_size() const {
  return _internal_tags_size();
}
inline void tile_feature::clear_tags() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.tags_.Clear();
}
inline ::uint32_t tile_feature::tags(int index) const {
  // @@protoc_insertion_point(field_get:mapnik.vector.tile.feature.tags)
  return _internal_tags().Get(index);
}
inline void tile_feature::set_tags(int index, ::uint32_t value) {
  _internal_mutable_tags()->Set(index, value);
  // @@protoc_insertion_point(field_set:mapnik.vector.tile.feature.tags)
}
inline void tile_feature::add_tags(::uint32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _internal_mutable_tags()->Add(value);
  // @@protoc_insertion_point(field_add:mapnik.vector.tile.feature.tags)
}
inline const ::google::protobuf::RepeatedField<::uint32_t>& tile_feature::tags() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:mapnik.vector.tile.feature.tags)
  return _internal_tags();
}
inline ::google::protobuf::RepeatedField<::uint32_t>* tile_feature::mutable_tags()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:mapnik.vector.tile.feature.tags)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _internal_mutable_tags();
}
inline const ::google::protobuf::RepeatedField<::uint32_t>&
tile_feature::_internal_tags() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.tags_;
}
inline ::google::protobuf::RepeatedField<::uint32_t>* tile_feature::_internal_mutable_tags() {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return &_impl_.tags_;
}

// optional .mapnik.vector.tile.GeomType type = 3 [default = Unknown];
inline bool tile_feature::has_type() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline void tile_feature::clear_type() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.type_ = 0;
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline ::mapnik::vector::tile_GeomType tile_feature::type() const {
  // @@protoc_insertion_point(field_get:mapnik.vector.tile.feature.type)
  return _internal_type();
}
inline void tile_feature::set_type(::mapnik::vector::tile_GeomType value) {
  _internal_set_type(value);
  _impl_._has_bits_[0] |= 0x00000002u;
  // @@protoc_insertion_point(field_set:mapnik.vector.tile.feature.type)
}
inline ::mapnik::vector::tile_GeomType tile_feature::_internal_type() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return static_cast<::mapnik::vector::tile_GeomType>(_impl_.type_);
}
inline void tile_feature::_internal_set_type(::mapnik::vector::tile_GeomType value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  assert(::mapnik::vector::tile_GeomType_IsValid(value));
  _impl_.type_ = value;
}

// repeated uint32 geometry = 4 [packed = true];
inline int tile_feature::_internal_geometry_size() const {
  return _internal_geometry().size();
}
inline int tile_feature::geometry_size() const {
  return _internal_geometry_size();
}
inline void tile_feature::clear_geometry() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.geometry_.Clear();
}
inline ::uint32_t tile_feature::geometry(int index) const {
  // @@protoc_insertion_point(field_get:mapnik.vector.tile.feature.geometry)
  return _internal_geometry().Get(index);
}
inline void tile_feature::set_geometry(int index, ::uint32_t value) {
  _internal_mutable_geometry()->Set(index, value);
  // @@protoc_insertion_point(field_set:mapnik.vector.tile.feature.geometry)
}
inline void tile_feature::add_geometry(::uint32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _internal_mutable_geometry()->Add(value);
  // @@protoc_insertion_point(field_add:mapnik.vector.tile.feature.geometry)
}
inline const ::google::protobuf::RepeatedField<::uint32_t>& tile_feature::geometry() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:mapnik.vector.tile.feature.geometry)
  return _internal_geometry();
}
inline ::google::protobuf::RepeatedField<::uint32_t>* tile_feature::mutable_geometry()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:mapnik.vector.tile.feature.geometry)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _internal_mutable_geometry();
}
inline const ::google::protobuf::RepeatedField<::uint32_t>&
tile_feature::_internal_geometry() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.geometry_;
}
inline ::google::protobuf::RepeatedField<::uint32_t>* tile_feature::_internal_mutable_geometry() {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return &_impl_.geometry_;
}

// -------------------------------------------------------------------

// tile_layer

// required uint32 version = 15 [default = 1];
inline bool tile_layer::has_version() const {
  bool value = (_impl_._has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline void tile_layer::clear_version() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.version_ = 1u;
  _impl_._has_bits_[0] &= ~0x00000004u;
}
inline ::uint32_t tile_layer::version() const {
  // @@protoc_insertion_point(field_get:mapnik.vector.tile.layer.version)
  return _internal_version();
}
inline void tile_layer::set_version(::uint32_t value) {
  _internal_set_version(value);
  _impl_._has_bits_[0] |= 0x00000004u;
  // @@protoc_insertion_point(field_set:mapnik.vector.tile.layer.version)
}
inline ::uint32_t tile_layer::_internal_version() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.version_;
}
inline void tile_layer::_internal_set_version(::uint32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.version_ = value;
}

// required string name = 1;
inline bool tile_layer::has_name() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline void tile_layer::clear_name() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.name_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const std::string& tile_layer::name() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:mapnik.vector.tile.layer.name)
  return _internal_name();
}
template <typename Arg_, typename... Args_>
inline PROTOBUF_ALWAYS_INLINE void tile_layer::set_name(Arg_&& arg,
                                                     Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.name_.Set(static_cast<Arg_&&>(arg), args..., GetArena());
  // @@protoc_insertion_point(field_set:mapnik.vector.tile.layer.name)
}
inline std::string* tile_layer::mutable_name() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:mapnik.vector.tile.layer.name)
  return _s;
}
inline const std::string& tile_layer::_internal_name() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.name_.Get();
}
inline void tile_layer::_internal_set_name(const std::string& value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.name_.Set(value, GetArena());
}
inline std::string* tile_layer::_internal_mutable_name() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_._has_bits_[0] |= 0x00000001u;
  return _impl_.name_.Mutable( GetArena());
}
inline std::string* tile_layer::release_name() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  // @@protoc_insertion_point(field_release:mapnik.vector.tile.layer.name)
  if ((_impl_._has_bits_[0] & 0x00000001u) == 0) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000001u;
  auto* released = _impl_.name_.Release();
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString()) {
    _impl_.name_.Set("", GetArena());
  }
  return released;
}
inline void tile_layer::set_allocated_name(std::string* value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  if (value != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  _impl_.name_.SetAllocated(value, GetArena());
  if (::google::protobuf::internal::DebugHardenForceCopyDefaultString() && _impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArena());
  }
  // @@protoc_insertion_point(field_set_allocated:mapnik.vector.tile.layer.name)
}

// repeated .mapnik.vector.tile.feature features = 2;
inline int tile_layer::_internal_features_size() const {
  return _internal_features().size();
}
inline int tile_layer::features_size() const {
  return _internal_features_size();
}
inline void tile_layer::clear_features() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.features_.Clear();
}
inline ::mapnik::vector::tile_feature* tile_layer::mutable_features(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:mapnik.vector.tile.layer.features)
  return _internal_mutable_features()->Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField<::mapnik::vector::tile_feature>* tile_layer::mutable_features()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:mapnik.vector.tile.layer.features)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _internal_mutable_features();
}
inline const ::mapnik::vector::tile_feature& tile_layer::features(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:mapnik.vector.tile.layer.features)
  return _internal_features().Get(index);
}
inline ::mapnik::vector::tile_feature* tile_layer::add_features() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::mapnik::vector::tile_feature* _add = _internal_mutable_features()->Add();
  // @@protoc_insertion_point(field_add:mapnik.vector.tile.layer.features)
  return _add;
}
inline const ::google::protobuf::RepeatedPtrField<::mapnik::vector::tile_feature>& tile_layer::features() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:mapnik.vector.tile.layer.features)
  return _internal_features();
}
inline const ::google::protobuf::RepeatedPtrField<::mapnik::vector::tile_feature>&
tile_layer::_internal_features() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.features_;
}
inline ::google::protobuf::RepeatedPtrField<::mapnik::vector::tile_feature>*
tile_layer::_internal_mutable_features() {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return &_impl_.features_;
}

// repeated string keys = 3;
inline int tile_layer::_internal_keys_size() const {
  return _internal_keys().size();
}
inline int tile_layer::keys_size() const {
  return _internal_keys_size();
}
inline void tile_layer::clear_keys() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.keys_.Clear();
}
inline std::string* tile_layer::add_keys() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  std::string* _s = _internal_mutable_keys()->Add();
  // @@protoc_insertion_point(field_add_mutable:mapnik.vector.tile.layer.keys)
  return _s;
}
inline const std::string& tile_layer::keys(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:mapnik.vector.tile.layer.keys)
  return _internal_keys().Get(index);
}
inline std::string* tile_layer::mutable_keys(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:mapnik.vector.tile.layer.keys)
  return _internal_mutable_keys()->Mutable(index);
}
template <typename Arg_, typename... Args_>
inline void tile_layer::set_keys(int index, Arg_&& value, Args_... args) {
  ::google::protobuf::internal::AssignToString(
      *_internal_mutable_keys()->Mutable(index),
      std::forward<Arg_>(value), args... );
  // @@protoc_insertion_point(field_set:mapnik.vector.tile.layer.keys)
}
template <typename Arg_, typename... Args_>
inline void tile_layer::add_keys(Arg_&& value, Args_... args) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::google::protobuf::internal::AddToRepeatedPtrField(*_internal_mutable_keys(),
                               std::forward<Arg_>(value),
                               args... );
  // @@protoc_insertion_point(field_add:mapnik.vector.tile.layer.keys)
}
inline const ::google::protobuf::RepeatedPtrField<std::string>&
tile_layer::keys() const ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:mapnik.vector.tile.layer.keys)
  return _internal_keys();
}
inline ::google::protobuf::RepeatedPtrField<std::string>*
tile_layer::mutable_keys() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:mapnik.vector.tile.layer.keys)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _internal_mutable_keys();
}
inline const ::google::protobuf::RepeatedPtrField<std::string>&
tile_layer::_internal_keys() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.keys_;
}
inline ::google::protobuf::RepeatedPtrField<std::string>*
tile_layer::_internal_mutable_keys() {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return &_impl_.keys_;
}

// repeated .mapnik.vector.tile.value values = 4;
inline int tile_layer::_internal_values_size() const {
  return _internal_values().size();
}
inline int tile_layer::values_size() const {
  return _internal_values_size();
}
inline void tile_layer::clear_values() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.values_.Clear();
}
inline ::mapnik::vector::tile_value* tile_layer::mutable_values(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:mapnik.vector.tile.layer.values)
  return _internal_mutable_values()->Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField<::mapnik::vector::tile_value>* tile_layer::mutable_values()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:mapnik.vector.tile.layer.values)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _internal_mutable_values();
}
inline const ::mapnik::vector::tile_value& tile_layer::values(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:mapnik.vector.tile.layer.values)
  return _internal_values().Get(index);
}
inline ::mapnik::vector::tile_value* tile_layer::add_values() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::mapnik::vector::tile_value* _add = _internal_mutable_values()->Add();
  // @@protoc_insertion_point(field_add:mapnik.vector.tile.layer.values)
  return _add;
}
inline const ::google::protobuf::RepeatedPtrField<::mapnik::vector::tile_value>& tile_layer::values() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:mapnik.vector.tile.layer.values)
  return _internal_values();
}
inline const ::google::protobuf::RepeatedPtrField<::mapnik::vector::tile_value>&
tile_layer::_internal_values() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.values_;
}
inline ::google::protobuf::RepeatedPtrField<::mapnik::vector::tile_value>*
tile_layer::_internal_mutable_values() {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return &_impl_.values_;
}

// optional uint32 extent = 5 [default = 4096];
inline bool tile_layer::has_extent() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline void tile_layer::clear_extent() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.extent_ = 4096u;
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline ::uint32_t tile_layer::extent() const {
  // @@protoc_insertion_point(field_get:mapnik.vector.tile.layer.extent)
  return _internal_extent();
}
inline void tile_layer::set_extent(::uint32_t value) {
  _internal_set_extent(value);
  _impl_._has_bits_[0] |= 0x00000002u;
  // @@protoc_insertion_point(field_set:mapnik.vector.tile.layer.extent)
}
inline ::uint32_t tile_layer::_internal_extent() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.extent_;
}
inline void tile_layer::_internal_set_extent(::uint32_t value) {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.extent_ = value;
}

// -------------------------------------------------------------------

// tile

// repeated .mapnik.vector.tile.layer layers = 3;
inline int tile::_internal_layers_size() const {
  return _internal_layers().size();
}
inline int tile::layers_size() const {
  return _internal_layers_size();
}
inline void tile::clear_layers() {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  _impl_.layers_.Clear();
}
inline ::mapnik::vector::tile_layer* tile::mutable_layers(int index)
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable:mapnik.vector.tile.layers)
  return _internal_mutable_layers()->Mutable(index);
}
inline ::google::protobuf::RepeatedPtrField<::mapnik::vector::tile_layer>* tile::mutable_layers()
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_mutable_list:mapnik.vector.tile.layers)
  ::google::protobuf::internal::TSanWrite(&_impl_);
  return _internal_mutable_layers();
}
inline const ::mapnik::vector::tile_layer& tile::layers(int index) const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_get:mapnik.vector.tile.layers)
  return _internal_layers().Get(index);
}
inline ::mapnik::vector::tile_layer* tile::add_layers() ABSL_ATTRIBUTE_LIFETIME_BOUND {
  ::google::protobuf::internal::TSanWrite(&_impl_);
  ::mapnik::vector::tile_layer* _add = _internal_mutable_layers()->Add();
  // @@protoc_insertion_point(field_add:mapnik.vector.tile.layers)
  return _add;
}
inline const ::google::protobuf::RepeatedPtrField<::mapnik::vector::tile_layer>& tile::layers() const
    ABSL_ATTRIBUTE_LIFETIME_BOUND {
  // @@protoc_insertion_point(field_list:mapnik.vector.tile.layers)
  return _internal_layers();
}
inline const ::google::protobuf::RepeatedPtrField<::mapnik::vector::tile_layer>&
tile::_internal_layers() const {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return _impl_.layers_;
}
inline ::google::protobuf::RepeatedPtrField<::mapnik::vector::tile_layer>*
tile::_internal_mutable_layers() {
  ::google::protobuf::internal::TSanRead(&_impl_);
  return &_impl_.layers_;
}

#ifdef __GNUC__
#pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)
}  // namespace vector
}  // namespace mapnik


namespace google {
namespace protobuf {

template <>
struct is_proto_enum<::mapnik::vector::tile_GeomType> : std::true_type {};

}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#include "google/protobuf/port_undef.inc"

#endif  // vector_5ftile_2eproto_2epb_2eh
