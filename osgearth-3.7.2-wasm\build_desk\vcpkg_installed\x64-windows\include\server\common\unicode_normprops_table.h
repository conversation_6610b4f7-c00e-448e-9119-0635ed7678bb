/* generated by src/common/unicode/generate-unicode_normprops_table.pl, do not edit */

#include "common/unicode_norm.h"

/*
 * Normalization quick check entry for codepoint.  We use a bit field
 * here to save space.
 */
typedef struct
{
	unsigned int codepoint:21;
	signed int	quickcheck:4;	/* really UnicodeNormalizationQC */
} pg_unicode_normprops;

/* Typedef for hash function on quick check table */
typedef int (*qc_hash_func) (const void *key);

/* Information for quick check lookup with perfect hash function */
typedef struct
{
	const pg_unicode_normprops *normprops;
	qc_hash_func	hash;
	int		num_normprops;
} pg_unicode_norminfo;

static const pg_unicode_normprops UnicodeNormProps_NFC_QC[] = {
	{0x0300, UNICODE_NORM_QC_MAYBE},
	{0x0301, UNICODE_NORM_QC_MAYBE},
	{0x0302, UNICODE_NORM_QC_MAYBE},
	{0x0303, UNICODE_NORM_QC_MAYBE},
	{0x0304, UNICODE_NORM_QC_MAYBE},
	{0x0306, UNICODE_NORM_QC_MAYBE},
	{0x0307, UNICODE_NORM_QC_MAYBE},
	{0x0308, UNICODE_NORM_QC_MAYBE},
	{0x0309, UNICODE_NORM_QC_MAYBE},
	{0x030A, UNICODE_NORM_QC_MAYBE},
	{0x030B, UNICODE_NORM_QC_MAYBE},
	{0x030C, UNICODE_NORM_QC_MAYBE},
	{0x030F, UNICODE_NORM_QC_MAYBE},
	{0x0311, UNICODE_NORM_QC_MAYBE},
	{0x0313, UNICODE_NORM_QC_MAYBE},
	{0x0314, UNICODE_NORM_QC_MAYBE},
	{0x031B, UNICODE_NORM_QC_MAYBE},
	{0x0323, UNICODE_NORM_QC_MAYBE},
	{0x0324, UNICODE_NORM_QC_MAYBE},
	{0x0325, UNICODE_NORM_QC_MAYBE},
	{0x0326, UNICODE_NORM_QC_MAYBE},
	{0x0327, UNICODE_NORM_QC_MAYBE},
	{0x0328, UNICODE_NORM_QC_MAYBE},
	{0x032D, UNICODE_NORM_QC_MAYBE},
	{0x032E, UNICODE_NORM_QC_MAYBE},
	{0x0330, UNICODE_NORM_QC_MAYBE},
	{0x0331, UNICODE_NORM_QC_MAYBE},
	{0x0338, UNICODE_NORM_QC_MAYBE},
	{0x0340, UNICODE_NORM_QC_NO},
	{0x0341, UNICODE_NORM_QC_NO},
	{0x0342, UNICODE_NORM_QC_MAYBE},
	{0x0343, UNICODE_NORM_QC_NO},
	{0x0344, UNICODE_NORM_QC_NO},
	{0x0345, UNICODE_NORM_QC_MAYBE},
	{0x0374, UNICODE_NORM_QC_NO},
	{0x037E, UNICODE_NORM_QC_NO},
	{0x0387, UNICODE_NORM_QC_NO},
	{0x0653, UNICODE_NORM_QC_MAYBE},
	{0x0654, UNICODE_NORM_QC_MAYBE},
	{0x0655, UNICODE_NORM_QC_MAYBE},
	{0x093C, UNICODE_NORM_QC_MAYBE},
	{0x0958, UNICODE_NORM_QC_NO},
	{0x0959, UNICODE_NORM_QC_NO},
	{0x095A, UNICODE_NORM_QC_NO},
	{0x095B, UNICODE_NORM_QC_NO},
	{0x095C, UNICODE_NORM_QC_NO},
	{0x095D, UNICODE_NORM_QC_NO},
	{0x095E, UNICODE_NORM_QC_NO},
	{0x095F, UNICODE_NORM_QC_NO},
	{0x09BE, UNICODE_NORM_QC_MAYBE},
	{0x09D7, UNICODE_NORM_QC_MAYBE},
	{0x09DC, UNICODE_NORM_QC_NO},
	{0x09DD, UNICODE_NORM_QC_NO},
	{0x09DF, UNICODE_NORM_QC_NO},
	{0x0A33, UNICODE_NORM_QC_NO},
	{0x0A36, UNICODE_NORM_QC_NO},
	{0x0A59, UNICODE_NORM_QC_NO},
	{0x0A5A, UNICODE_NORM_QC_NO},
	{0x0A5B, UNICODE_NORM_QC_NO},
	{0x0A5E, UNICODE_NORM_QC_NO},
	{0x0B3E, UNICODE_NORM_QC_MAYBE},
	{0x0B56, UNICODE_NORM_QC_MAYBE},
	{0x0B57, UNICODE_NORM_QC_MAYBE},
	{0x0B5C, UNICODE_NORM_QC_NO},
	{0x0B5D, UNICODE_NORM_QC_NO},
	{0x0BBE, UNICODE_NORM_QC_MAYBE},
	{0x0BD7, UNICODE_NORM_QC_MAYBE},
	{0x0C56, UNICODE_NORM_QC_MAYBE},
	{0x0CC2, UNICODE_NORM_QC_MAYBE},
	{0x0CD5, UNICODE_NORM_QC_MAYBE},
	{0x0CD6, UNICODE_NORM_QC_MAYBE},
	{0x0D3E, UNICODE_NORM_QC_MAYBE},
	{0x0D57, UNICODE_NORM_QC_MAYBE},
	{0x0DCA, UNICODE_NORM_QC_MAYBE},
	{0x0DCF, UNICODE_NORM_QC_MAYBE},
	{0x0DDF, UNICODE_NORM_QC_MAYBE},
	{0x0F43, UNICODE_NORM_QC_NO},
	{0x0F4D, UNICODE_NORM_QC_NO},
	{0x0F52, UNICODE_NORM_QC_NO},
	{0x0F57, UNICODE_NORM_QC_NO},
	{0x0F5C, UNICODE_NORM_QC_NO},
	{0x0F69, UNICODE_NORM_QC_NO},
	{0x0F73, UNICODE_NORM_QC_NO},
	{0x0F75, UNICODE_NORM_QC_NO},
	{0x0F76, UNICODE_NORM_QC_NO},
	{0x0F78, UNICODE_NORM_QC_NO},
	{0x0F81, UNICODE_NORM_QC_NO},
	{0x0F93, UNICODE_NORM_QC_NO},
	{0x0F9D, UNICODE_NORM_QC_NO},
	{0x0FA2, UNICODE_NORM_QC_NO},
	{0x0FA7, UNICODE_NORM_QC_NO},
	{0x0FAC, UNICODE_NORM_QC_NO},
	{0x0FB9, UNICODE_NORM_QC_NO},
	{0x102E, UNICODE_NORM_QC_MAYBE},
	{0x1161, UNICODE_NORM_QC_MAYBE},
	{0x1162, UNICODE_NORM_QC_MAYBE},
	{0x1163, UNICODE_NORM_QC_MAYBE},
	{0x1164, UNICODE_NORM_QC_MAYBE},
	{0x1165, UNICODE_NORM_QC_MAYBE},
	{0x1166, UNICODE_NORM_QC_MAYBE},
	{0x1167, UNICODE_NORM_QC_MAYBE},
	{0x1168, UNICODE_NORM_QC_MAYBE},
	{0x1169, UNICODE_NORM_QC_MAYBE},
	{0x116A, UNICODE_NORM_QC_MAYBE},
	{0x116B, UNICODE_NORM_QC_MAYBE},
	{0x116C, UNICODE_NORM_QC_MAYBE},
	{0x116D, UNICODE_NORM_QC_MAYBE},
	{0x116E, UNICODE_NORM_QC_MAYBE},
	{0x116F, UNICODE_NORM_QC_MAYBE},
	{0x1170, UNICODE_NORM_QC_MAYBE},
	{0x1171, UNICODE_NORM_QC_MAYBE},
	{0x1172, UNICODE_NORM_QC_MAYBE},
	{0x1173, UNICODE_NORM_QC_MAYBE},
	{0x1174, UNICODE_NORM_QC_MAYBE},
	{0x1175, UNICODE_NORM_QC_MAYBE},
	{0x11A8, UNICODE_NORM_QC_MAYBE},
	{0x11A9, UNICODE_NORM_QC_MAYBE},
	{0x11AA, UNICODE_NORM_QC_MAYBE},
	{0x11AB, UNICODE_NORM_QC_MAYBE},
	{0x11AC, UNICODE_NORM_QC_MAYBE},
	{0x11AD, UNICODE_NORM_QC_MAYBE},
	{0x11AE, UNICODE_NORM_QC_MAYBE},
	{0x11AF, UNICODE_NORM_QC_MAYBE},
	{0x11B0, UNICODE_NORM_QC_MAYBE},
	{0x11B1, UNICODE_NORM_QC_MAYBE},
	{0x11B2, UNICODE_NORM_QC_MAYBE},
	{0x11B3, UNICODE_NORM_QC_MAYBE},
	{0x11B4, UNICODE_NORM_QC_MAYBE},
	{0x11B5, UNICODE_NORM_QC_MAYBE},
	{0x11B6, UNICODE_NORM_QC_MAYBE},
	{0x11B7, UNICODE_NORM_QC_MAYBE},
	{0x11B8, UNICODE_NORM_QC_MAYBE},
	{0x11B9, UNICODE_NORM_QC_MAYBE},
	{0x11BA, UNICODE_NORM_QC_MAYBE},
	{0x11BB, UNICODE_NORM_QC_MAYBE},
	{0x11BC, UNICODE_NORM_QC_MAYBE},
	{0x11BD, UNICODE_NORM_QC_MAYBE},
	{0x11BE, UNICODE_NORM_QC_MAYBE},
	{0x11BF, UNICODE_NORM_QC_MAYBE},
	{0x11C0, UNICODE_NORM_QC_MAYBE},
	{0x11C1, UNICODE_NORM_QC_MAYBE},
	{0x11C2, UNICODE_NORM_QC_MAYBE},
	{0x1B35, UNICODE_NORM_QC_MAYBE},
	{0x1F71, UNICODE_NORM_QC_NO},
	{0x1F73, UNICODE_NORM_QC_NO},
	{0x1F75, UNICODE_NORM_QC_NO},
	{0x1F77, UNICODE_NORM_QC_NO},
	{0x1F79, UNICODE_NORM_QC_NO},
	{0x1F7B, UNICODE_NORM_QC_NO},
	{0x1F7D, UNICODE_NORM_QC_NO},
	{0x1FBB, UNICODE_NORM_QC_NO},
	{0x1FBE, UNICODE_NORM_QC_NO},
	{0x1FC9, UNICODE_NORM_QC_NO},
	{0x1FCB, UNICODE_NORM_QC_NO},
	{0x1FD3, UNICODE_NORM_QC_NO},
	{0x1FDB, UNICODE_NORM_QC_NO},
	{0x1FE3, UNICODE_NORM_QC_NO},
	{0x1FEB, UNICODE_NORM_QC_NO},
	{0x1FEE, UNICODE_NORM_QC_NO},
	{0x1FEF, UNICODE_NORM_QC_NO},
	{0x1FF9, UNICODE_NORM_QC_NO},
	{0x1FFB, UNICODE_NORM_QC_NO},
	{0x1FFD, UNICODE_NORM_QC_NO},
	{0x2000, UNICODE_NORM_QC_NO},
	{0x2001, UNICODE_NORM_QC_NO},
	{0x2126, UNICODE_NORM_QC_NO},
	{0x212A, UNICODE_NORM_QC_NO},
	{0x212B, UNICODE_NORM_QC_NO},
	{0x2329, UNICODE_NORM_QC_NO},
	{0x232A, UNICODE_NORM_QC_NO},
	{0x2ADC, UNICODE_NORM_QC_NO},
	{0x3099, UNICODE_NORM_QC_MAYBE},
	{0x309A, UNICODE_NORM_QC_MAYBE},
	{0xF900, UNICODE_NORM_QC_NO},
	{0xF901, UNICODE_NORM_QC_NO},
	{0xF902, UNICODE_NORM_QC_NO},
	{0xF903, UNICODE_NORM_QC_NO},
	{0xF904, UNICODE_NORM_QC_NO},
	{0xF905, UNICODE_NORM_QC_NO},
	{0xF906, UNICODE_NORM_QC_NO},
	{0xF907, UNICODE_NORM_QC_NO},
	{0xF908, UNICODE_NORM_QC_NO},
	{0xF909, UNICODE_NORM_QC_NO},
	{0xF90A, UNICODE_NORM_QC_NO},
	{0xF90B, UNICODE_NORM_QC_NO},
	{0xF90C, UNICODE_NORM_QC_NO},
	{0xF90D, UNICODE_NORM_QC_NO},
	{0xF90E, UNICODE_NORM_QC_NO},
	{0xF90F, UNICODE_NORM_QC_NO},
	{0xF910, UNICODE_NORM_QC_NO},
	{0xF911, UNICODE_NORM_QC_NO},
	{0xF912, UNICODE_NORM_QC_NO},
	{0xF913, UNICODE_NORM_QC_NO},
	{0xF914, UNICODE_NORM_QC_NO},
	{0xF915, UNICODE_NORM_QC_NO},
	{0xF916, UNICODE_NORM_QC_NO},
	{0xF917, UNICODE_NORM_QC_NO},
	{0xF918, UNICODE_NORM_QC_NO},
	{0xF919, UNICODE_NORM_QC_NO},
	{0xF91A, UNICODE_NORM_QC_NO},
	{0xF91B, UNICODE_NORM_QC_NO},
	{0xF91C, UNICODE_NORM_QC_NO},
	{0xF91D, UNICODE_NORM_QC_NO},
	{0xF91E, UNICODE_NORM_QC_NO},
	{0xF91F, UNICODE_NORM_QC_NO},
	{0xF920, UNICODE_NORM_QC_NO},
	{0xF921, UNICODE_NORM_QC_NO},
	{0xF922, UNICODE_NORM_QC_NO},
	{0xF923, UNICODE_NORM_QC_NO},
	{0xF924, UNICODE_NORM_QC_NO},
	{0xF925, UNICODE_NORM_QC_NO},
	{0xF926, UNICODE_NORM_QC_NO},
	{0xF927, UNICODE_NORM_QC_NO},
	{0xF928, UNICODE_NORM_QC_NO},
	{0xF929, UNICODE_NORM_QC_NO},
	{0xF92A, UNICODE_NORM_QC_NO},
	{0xF92B, UNICODE_NORM_QC_NO},
	{0xF92C, UNICODE_NORM_QC_NO},
	{0xF92D, UNICODE_NORM_QC_NO},
	{0xF92E, UNICODE_NORM_QC_NO},
	{0xF92F, UNICODE_NORM_QC_NO},
	{0xF930, UNICODE_NORM_QC_NO},
	{0xF931, UNICODE_NORM_QC_NO},
	{0xF932, UNICODE_NORM_QC_NO},
	{0xF933, UNICODE_NORM_QC_NO},
	{0xF934, UNICODE_NORM_QC_NO},
	{0xF935, UNICODE_NORM_QC_NO},
	{0xF936, UNICODE_NORM_QC_NO},
	{0xF937, UNICODE_NORM_QC_NO},
	{0xF938, UNICODE_NORM_QC_NO},
	{0xF939, UNICODE_NORM_QC_NO},
	{0xF93A, UNICODE_NORM_QC_NO},
	{0xF93B, UNICODE_NORM_QC_NO},
	{0xF93C, UNICODE_NORM_QC_NO},
	{0xF93D, UNICODE_NORM_QC_NO},
	{0xF93E, UNICODE_NORM_QC_NO},
	{0xF93F, UNICODE_NORM_QC_NO},
	{0xF940, UNICODE_NORM_QC_NO},
	{0xF941, UNICODE_NORM_QC_NO},
	{0xF942, UNICODE_NORM_QC_NO},
	{0xF943, UNICODE_NORM_QC_NO},
	{0xF944, UNICODE_NORM_QC_NO},
	{0xF945, UNICODE_NORM_QC_NO},
	{0xF946, UNICODE_NORM_QC_NO},
	{0xF947, UNICODE_NORM_QC_NO},
	{0xF948, UNICODE_NORM_QC_NO},
	{0xF949, UNICODE_NORM_QC_NO},
	{0xF94A, UNICODE_NORM_QC_NO},
	{0xF94B, UNICODE_NORM_QC_NO},
	{0xF94C, UNICODE_NORM_QC_NO},
	{0xF94D, UNICODE_NORM_QC_NO},
	{0xF94E, UNICODE_NORM_QC_NO},
	{0xF94F, UNICODE_NORM_QC_NO},
	{0xF950, UNICODE_NORM_QC_NO},
	{0xF951, UNICODE_NORM_QC_NO},
	{0xF952, UNICODE_NORM_QC_NO},
	{0xF953, UNICODE_NORM_QC_NO},
	{0xF954, UNICODE_NORM_QC_NO},
	{0xF955, UNICODE_NORM_QC_NO},
	{0xF956, UNICODE_NORM_QC_NO},
	{0xF957, UNICODE_NORM_QC_NO},
	{0xF958, UNICODE_NORM_QC_NO},
	{0xF959, UNICODE_NORM_QC_NO},
	{0xF95A, UNICODE_NORM_QC_NO},
	{0xF95B, UNICODE_NORM_QC_NO},
	{0xF95C, UNICODE_NORM_QC_NO},
	{0xF95D, UNICODE_NORM_QC_NO},
	{0xF95E, UNICODE_NORM_QC_NO},
	{0xF95F, UNICODE_NORM_QC_NO},
	{0xF960, UNICODE_NORM_QC_NO},
	{0xF961, UNICODE_NORM_QC_NO},
	{0xF962, UNICODE_NORM_QC_NO},
	{0xF963, UNICODE_NORM_QC_NO},
	{0xF964, UNICODE_NORM_QC_NO},
	{0xF965, UNICODE_NORM_QC_NO},
	{0xF966, UNICODE_NORM_QC_NO},
	{0xF967, UNICODE_NORM_QC_NO},
	{0xF968, UNICODE_NORM_QC_NO},
	{0xF969, UNICODE_NORM_QC_NO},
	{0xF96A, UNICODE_NORM_QC_NO},
	{0xF96B, UNICODE_NORM_QC_NO},
	{0xF96C, UNICODE_NORM_QC_NO},
	{0xF96D, UNICODE_NORM_QC_NO},
	{0xF96E, UNICODE_NORM_QC_NO},
	{0xF96F, UNICODE_NORM_QC_NO},
	{0xF970, UNICODE_NORM_QC_NO},
	{0xF971, UNICODE_NORM_QC_NO},
	{0xF972, UNICODE_NORM_QC_NO},
	{0xF973, UNICODE_NORM_QC_NO},
	{0xF974, UNICODE_NORM_QC_NO},
	{0xF975, UNICODE_NORM_QC_NO},
	{0xF976, UNICODE_NORM_QC_NO},
	{0xF977, UNICODE_NORM_QC_NO},
	{0xF978, UNICODE_NORM_QC_NO},
	{0xF979, UNICODE_NORM_QC_NO},
	{0xF97A, UNICODE_NORM_QC_NO},
	{0xF97B, UNICODE_NORM_QC_NO},
	{0xF97C, UNICODE_NORM_QC_NO},
	{0xF97D, UNICODE_NORM_QC_NO},
	{0xF97E, UNICODE_NORM_QC_NO},
	{0xF97F, UNICODE_NORM_QC_NO},
	{0xF980, UNICODE_NORM_QC_NO},
	{0xF981, UNICODE_NORM_QC_NO},
	{0xF982, UNICODE_NORM_QC_NO},
	{0xF983, UNICODE_NORM_QC_NO},
	{0xF984, UNICODE_NORM_QC_NO},
	{0xF985, UNICODE_NORM_QC_NO},
	{0xF986, UNICODE_NORM_QC_NO},
	{0xF987, UNICODE_NORM_QC_NO},
	{0xF988, UNICODE_NORM_QC_NO},
	{0xF989, UNICODE_NORM_QC_NO},
	{0xF98A, UNICODE_NORM_QC_NO},
	{0xF98B, UNICODE_NORM_QC_NO},
	{0xF98C, UNICODE_NORM_QC_NO},
	{0xF98D, UNICODE_NORM_QC_NO},
	{0xF98E, UNICODE_NORM_QC_NO},
	{0xF98F, UNICODE_NORM_QC_NO},
	{0xF990, UNICODE_NORM_QC_NO},
	{0xF991, UNICODE_NORM_QC_NO},
	{0xF992, UNICODE_NORM_QC_NO},
	{0xF993, UNICODE_NORM_QC_NO},
	{0xF994, UNICODE_NORM_QC_NO},
	{0xF995, UNICODE_NORM_QC_NO},
	{0xF996, UNICODE_NORM_QC_NO},
	{0xF997, UNICODE_NORM_QC_NO},
	{0xF998, UNICODE_NORM_QC_NO},
	{0xF999, UNICODE_NORM_QC_NO},
	{0xF99A, UNICODE_NORM_QC_NO},
	{0xF99B, UNICODE_NORM_QC_NO},
	{0xF99C, UNICODE_NORM_QC_NO},
	{0xF99D, UNICODE_NORM_QC_NO},
	{0xF99E, UNICODE_NORM_QC_NO},
	{0xF99F, UNICODE_NORM_QC_NO},
	{0xF9A0, UNICODE_NORM_QC_NO},
	{0xF9A1, UNICODE_NORM_QC_NO},
	{0xF9A2, UNICODE_NORM_QC_NO},
	{0xF9A3, UNICODE_NORM_QC_NO},
	{0xF9A4, UNICODE_NORM_QC_NO},
	{0xF9A5, UNICODE_NORM_QC_NO},
	{0xF9A6, UNICODE_NORM_QC_NO},
	{0xF9A7, UNICODE_NORM_QC_NO},
	{0xF9A8, UNICODE_NORM_QC_NO},
	{0xF9A9, UNICODE_NORM_QC_NO},
	{0xF9AA, UNICODE_NORM_QC_NO},
	{0xF9AB, UNICODE_NORM_QC_NO},
	{0xF9AC, UNICODE_NORM_QC_NO},
	{0xF9AD, UNICODE_NORM_QC_NO},
	{0xF9AE, UNICODE_NORM_QC_NO},
	{0xF9AF, UNICODE_NORM_QC_NO},
	{0xF9B0, UNICODE_NORM_QC_NO},
	{0xF9B1, UNICODE_NORM_QC_NO},
	{0xF9B2, UNICODE_NORM_QC_NO},
	{0xF9B3, UNICODE_NORM_QC_NO},
	{0xF9B4, UNICODE_NORM_QC_NO},
	{0xF9B5, UNICODE_NORM_QC_NO},
	{0xF9B6, UNICODE_NORM_QC_NO},
	{0xF9B7, UNICODE_NORM_QC_NO},
	{0xF9B8, UNICODE_NORM_QC_NO},
	{0xF9B9, UNICODE_NORM_QC_NO},
	{0xF9BA, UNICODE_NORM_QC_NO},
	{0xF9BB, UNICODE_NORM_QC_NO},
	{0xF9BC, UNICODE_NORM_QC_NO},
	{0xF9BD, UNICODE_NORM_QC_NO},
	{0xF9BE, UNICODE_NORM_QC_NO},
	{0xF9BF, UNICODE_NORM_QC_NO},
	{0xF9C0, UNICODE_NORM_QC_NO},
	{0xF9C1, UNICODE_NORM_QC_NO},
	{0xF9C2, UNICODE_NORM_QC_NO},
	{0xF9C3, UNICODE_NORM_QC_NO},
	{0xF9C4, UNICODE_NORM_QC_NO},
	{0xF9C5, UNICODE_NORM_QC_NO},
	{0xF9C6, UNICODE_NORM_QC_NO},
	{0xF9C7, UNICODE_NORM_QC_NO},
	{0xF9C8, UNICODE_NORM_QC_NO},
	{0xF9C9, UNICODE_NORM_QC_NO},
	{0xF9CA, UNICODE_NORM_QC_NO},
	{0xF9CB, UNICODE_NORM_QC_NO},
	{0xF9CC, UNICODE_NORM_QC_NO},
	{0xF9CD, UNICODE_NORM_QC_NO},
	{0xF9CE, UNICODE_NORM_QC_NO},
	{0xF9CF, UNICODE_NORM_QC_NO},
	{0xF9D0, UNICODE_NORM_QC_NO},
	{0xF9D1, UNICODE_NORM_QC_NO},
	{0xF9D2, UNICODE_NORM_QC_NO},
	{0xF9D3, UNICODE_NORM_QC_NO},
	{0xF9D4, UNICODE_NORM_QC_NO},
	{0xF9D5, UNICODE_NORM_QC_NO},
	{0xF9D6, UNICODE_NORM_QC_NO},
	{0xF9D7, UNICODE_NORM_QC_NO},
	{0xF9D8, UNICODE_NORM_QC_NO},
	{0xF9D9, UNICODE_NORM_QC_NO},
	{0xF9DA, UNICODE_NORM_QC_NO},
	{0xF9DB, UNICODE_NORM_QC_NO},
	{0xF9DC, UNICODE_NORM_QC_NO},
	{0xF9DD, UNICODE_NORM_QC_NO},
	{0xF9DE, UNICODE_NORM_QC_NO},
	{0xF9DF, UNICODE_NORM_QC_NO},
	{0xF9E0, UNICODE_NORM_QC_NO},
	{0xF9E1, UNICODE_NORM_QC_NO},
	{0xF9E2, UNICODE_NORM_QC_NO},
	{0xF9E3, UNICODE_NORM_QC_NO},
	{0xF9E4, UNICODE_NORM_QC_NO},
	{0xF9E5, UNICODE_NORM_QC_NO},
	{0xF9E6, UNICODE_NORM_QC_NO},
	{0xF9E7, UNICODE_NORM_QC_NO},
	{0xF9E8, UNICODE_NORM_QC_NO},
	{0xF9E9, UNICODE_NORM_QC_NO},
	{0xF9EA, UNICODE_NORM_QC_NO},
	{0xF9EB, UNICODE_NORM_QC_NO},
	{0xF9EC, UNICODE_NORM_QC_NO},
	{0xF9ED, UNICODE_NORM_QC_NO},
	{0xF9EE, UNICODE_NORM_QC_NO},
	{0xF9EF, UNICODE_NORM_QC_NO},
	{0xF9F0, UNICODE_NORM_QC_NO},
	{0xF9F1, UNICODE_NORM_QC_NO},
	{0xF9F2, UNICODE_NORM_QC_NO},
	{0xF9F3, UNICODE_NORM_QC_NO},
	{0xF9F4, UNICODE_NORM_QC_NO},
	{0xF9F5, UNICODE_NORM_QC_NO},
	{0xF9F6, UNICODE_NORM_QC_NO},
	{0xF9F7, UNICODE_NORM_QC_NO},
	{0xF9F8, UNICODE_NORM_QC_NO},
	{0xF9F9, UNICODE_NORM_QC_NO},
	{0xF9FA, UNICODE_NORM_QC_NO},
	{0xF9FB, UNICODE_NORM_QC_NO},
	{0xF9FC, UNICODE_NORM_QC_NO},
	{0xF9FD, UNICODE_NORM_QC_NO},
	{0xF9FE, UNICODE_NORM_QC_NO},
	{0xF9FF, UNICODE_NORM_QC_NO},
	{0xFA00, UNICODE_NORM_QC_NO},
	{0xFA01, UNICODE_NORM_QC_NO},
	{0xFA02, UNICODE_NORM_QC_NO},
	{0xFA03, UNICODE_NORM_QC_NO},
	{0xFA04, UNICODE_NORM_QC_NO},
	{0xFA05, UNICODE_NORM_QC_NO},
	{0xFA06, UNICODE_NORM_QC_NO},
	{0xFA07, UNICODE_NORM_QC_NO},
	{0xFA08, UNICODE_NORM_QC_NO},
	{0xFA09, UNICODE_NORM_QC_NO},
	{0xFA0A, UNICODE_NORM_QC_NO},
	{0xFA0B, UNICODE_NORM_QC_NO},
	{0xFA0C, UNICODE_NORM_QC_NO},
	{0xFA0D, UNICODE_NORM_QC_NO},
	{0xFA10, UNICODE_NORM_QC_NO},
	{0xFA12, UNICODE_NORM_QC_NO},
	{0xFA15, UNICODE_NORM_QC_NO},
	{0xFA16, UNICODE_NORM_QC_NO},
	{0xFA17, UNICODE_NORM_QC_NO},
	{0xFA18, UNICODE_NORM_QC_NO},
	{0xFA19, UNICODE_NORM_QC_NO},
	{0xFA1A, UNICODE_NORM_QC_NO},
	{0xFA1B, UNICODE_NORM_QC_NO},
	{0xFA1C, UNICODE_NORM_QC_NO},
	{0xFA1D, UNICODE_NORM_QC_NO},
	{0xFA1E, UNICODE_NORM_QC_NO},
	{0xFA20, UNICODE_NORM_QC_NO},
	{0xFA22, UNICODE_NORM_QC_NO},
	{0xFA25, UNICODE_NORM_QC_NO},
	{0xFA26, UNICODE_NORM_QC_NO},
	{0xFA2A, UNICODE_NORM_QC_NO},
	{0xFA2B, UNICODE_NORM_QC_NO},
	{0xFA2C, UNICODE_NORM_QC_NO},
	{0xFA2D, UNICODE_NORM_QC_NO},
	{0xFA2E, UNICODE_NORM_QC_NO},
	{0xFA2F, UNICODE_NORM_QC_NO},
	{0xFA30, UNICODE_NORM_QC_NO},
	{0xFA31, UNICODE_NORM_QC_NO},
	{0xFA32, UNICODE_NORM_QC_NO},
	{0xFA33, UNICODE_NORM_QC_NO},
	{0xFA34, UNICODE_NORM_QC_NO},
	{0xFA35, UNICODE_NORM_QC_NO},
	{0xFA36, UNICODE_NORM_QC_NO},
	{0xFA37, UNICODE_NORM_QC_NO},
	{0xFA38, UNICODE_NORM_QC_NO},
	{0xFA39, UNICODE_NORM_QC_NO},
	{0xFA3A, UNICODE_NORM_QC_NO},
	{0xFA3B, UNICODE_NORM_QC_NO},
	{0xFA3C, UNICODE_NORM_QC_NO},
	{0xFA3D, UNICODE_NORM_QC_NO},
	{0xFA3E, UNICODE_NORM_QC_NO},
	{0xFA3F, UNICODE_NORM_QC_NO},
	{0xFA40, UNICODE_NORM_QC_NO},
	{0xFA41, UNICODE_NORM_QC_NO},
	{0xFA42, UNICODE_NORM_QC_NO},
	{0xFA43, UNICODE_NORM_QC_NO},
	{0xFA44, UNICODE_NORM_QC_NO},
	{0xFA45, UNICODE_NORM_QC_NO},
	{0xFA46, UNICODE_NORM_QC_NO},
	{0xFA47, UNICODE_NORM_QC_NO},
	{0xFA48, UNICODE_NORM_QC_NO},
	{0xFA49, UNICODE_NORM_QC_NO},
	{0xFA4A, UNICODE_NORM_QC_NO},
	{0xFA4B, UNICODE_NORM_QC_NO},
	{0xFA4C, UNICODE_NORM_QC_NO},
	{0xFA4D, UNICODE_NORM_QC_NO},
	{0xFA4E, UNICODE_NORM_QC_NO},
	{0xFA4F, UNICODE_NORM_QC_NO},
	{0xFA50, UNICODE_NORM_QC_NO},
	{0xFA51, UNICODE_NORM_QC_NO},
	{0xFA52, UNICODE_NORM_QC_NO},
	{0xFA53, UNICODE_NORM_QC_NO},
	{0xFA54, UNICODE_NORM_QC_NO},
	{0xFA55, UNICODE_NORM_QC_NO},
	{0xFA56, UNICODE_NORM_QC_NO},
	{0xFA57, UNICODE_NORM_QC_NO},
	{0xFA58, UNICODE_NORM_QC_NO},
	{0xFA59, UNICODE_NORM_QC_NO},
	{0xFA5A, UNICODE_NORM_QC_NO},
	{0xFA5B, UNICODE_NORM_QC_NO},
	{0xFA5C, UNICODE_NORM_QC_NO},
	{0xFA5D, UNICODE_NORM_QC_NO},
	{0xFA5E, UNICODE_NORM_QC_NO},
	{0xFA5F, UNICODE_NORM_QC_NO},
	{0xFA60, UNICODE_NORM_QC_NO},
	{0xFA61, UNICODE_NORM_QC_NO},
	{0xFA62, UNICODE_NORM_QC_NO},
	{0xFA63, UNICODE_NORM_QC_NO},
	{0xFA64, UNICODE_NORM_QC_NO},
	{0xFA65, UNICODE_NORM_QC_NO},
	{0xFA66, UNICODE_NORM_QC_NO},
	{0xFA67, UNICODE_NORM_QC_NO},
	{0xFA68, UNICODE_NORM_QC_NO},
	{0xFA69, UNICODE_NORM_QC_NO},
	{0xFA6A, UNICODE_NORM_QC_NO},
	{0xFA6B, UNICODE_NORM_QC_NO},
	{0xFA6C, UNICODE_NORM_QC_NO},
	{0xFA6D, UNICODE_NORM_QC_NO},
	{0xFA70, UNICODE_NORM_QC_NO},
	{0xFA71, UNICODE_NORM_QC_NO},
	{0xFA72, UNICODE_NORM_QC_NO},
	{0xFA73, UNICODE_NORM_QC_NO},
	{0xFA74, UNICODE_NORM_QC_NO},
	{0xFA75, UNICODE_NORM_QC_NO},
	{0xFA76, UNICODE_NORM_QC_NO},
	{0xFA77, UNICODE_NORM_QC_NO},
	{0xFA78, UNICODE_NORM_QC_NO},
	{0xFA79, UNICODE_NORM_QC_NO},
	{0xFA7A, UNICODE_NORM_QC_NO},
	{0xFA7B, UNICODE_NORM_QC_NO},
	{0xFA7C, UNICODE_NORM_QC_NO},
	{0xFA7D, UNICODE_NORM_QC_NO},
	{0xFA7E, UNICODE_NORM_QC_NO},
	{0xFA7F, UNICODE_NORM_QC_NO},
	{0xFA80, UNICODE_NORM_QC_NO},
	{0xFA81, UNICODE_NORM_QC_NO},
	{0xFA82, UNICODE_NORM_QC_NO},
	{0xFA83, UNICODE_NORM_QC_NO},
	{0xFA84, UNICODE_NORM_QC_NO},
	{0xFA85, UNICODE_NORM_QC_NO},
	{0xFA86, UNICODE_NORM_QC_NO},
	{0xFA87, UNICODE_NORM_QC_NO},
	{0xFA88, UNICODE_NORM_QC_NO},
	{0xFA89, UNICODE_NORM_QC_NO},
	{0xFA8A, UNICODE_NORM_QC_NO},
	{0xFA8B, UNICODE_NORM_QC_NO},
	{0xFA8C, UNICODE_NORM_QC_NO},
	{0xFA8D, UNICODE_NORM_QC_NO},
	{0xFA8E, UNICODE_NORM_QC_NO},
	{0xFA8F, UNICODE_NORM_QC_NO},
	{0xFA90, UNICODE_NORM_QC_NO},
	{0xFA91, UNICODE_NORM_QC_NO},
	{0xFA92, UNICODE_NORM_QC_NO},
	{0xFA93, UNICODE_NORM_QC_NO},
	{0xFA94, UNICODE_NORM_QC_NO},
	{0xFA95, UNICODE_NORM_QC_NO},
	{0xFA96, UNICODE_NORM_QC_NO},
	{0xFA97, UNICODE_NORM_QC_NO},
	{0xFA98, UNICODE_NORM_QC_NO},
	{0xFA99, UNICODE_NORM_QC_NO},
	{0xFA9A, UNICODE_NORM_QC_NO},
	{0xFA9B, UNICODE_NORM_QC_NO},
	{0xFA9C, UNICODE_NORM_QC_NO},
	{0xFA9D, UNICODE_NORM_QC_NO},
	{0xFA9E, UNICODE_NORM_QC_NO},
	{0xFA9F, UNICODE_NORM_QC_NO},
	{0xFAA0, UNICODE_NORM_QC_NO},
	{0xFAA1, UNICODE_NORM_QC_NO},
	{0xFAA2, UNICODE_NORM_QC_NO},
	{0xFAA3, UNICODE_NORM_QC_NO},
	{0xFAA4, UNICODE_NORM_QC_NO},
	{0xFAA5, UNICODE_NORM_QC_NO},
	{0xFAA6, UNICODE_NORM_QC_NO},
	{0xFAA7, UNICODE_NORM_QC_NO},
	{0xFAA8, UNICODE_NORM_QC_NO},
	{0xFAA9, UNICODE_NORM_QC_NO},
	{0xFAAA, UNICODE_NORM_QC_NO},
	{0xFAAB, UNICODE_NORM_QC_NO},
	{0xFAAC, UNICODE_NORM_QC_NO},
	{0xFAAD, UNICODE_NORM_QC_NO},
	{0xFAAE, UNICODE_NORM_QC_NO},
	{0xFAAF, UNICODE_NORM_QC_NO},
	{0xFAB0, UNICODE_NORM_QC_NO},
	{0xFAB1, UNICODE_NORM_QC_NO},
	{0xFAB2, UNICODE_NORM_QC_NO},
	{0xFAB3, UNICODE_NORM_QC_NO},
	{0xFAB4, UNICODE_NORM_QC_NO},
	{0xFAB5, UNICODE_NORM_QC_NO},
	{0xFAB6, UNICODE_NORM_QC_NO},
	{0xFAB7, UNICODE_NORM_QC_NO},
	{0xFAB8, UNICODE_NORM_QC_NO},
	{0xFAB9, UNICODE_NORM_QC_NO},
	{0xFABA, UNICODE_NORM_QC_NO},
	{0xFABB, UNICODE_NORM_QC_NO},
	{0xFABC, UNICODE_NORM_QC_NO},
	{0xFABD, UNICODE_NORM_QC_NO},
	{0xFABE, UNICODE_NORM_QC_NO},
	{0xFABF, UNICODE_NORM_QC_NO},
	{0xFAC0, UNICODE_NORM_QC_NO},
	{0xFAC1, UNICODE_NORM_QC_NO},
	{0xFAC2, UNICODE_NORM_QC_NO},
	{0xFAC3, UNICODE_NORM_QC_NO},
	{0xFAC4, UNICODE_NORM_QC_NO},
	{0xFAC5, UNICODE_NORM_QC_NO},
	{0xFAC6, UNICODE_NORM_QC_NO},
	{0xFAC7, UNICODE_NORM_QC_NO},
	{0xFAC8, UNICODE_NORM_QC_NO},
	{0xFAC9, UNICODE_NORM_QC_NO},
	{0xFACA, UNICODE_NORM_QC_NO},
	{0xFACB, UNICODE_NORM_QC_NO},
	{0xFACC, UNICODE_NORM_QC_NO},
	{0xFACD, UNICODE_NORM_QC_NO},
	{0xFACE, UNICODE_NORM_QC_NO},
	{0xFACF, UNICODE_NORM_QC_NO},
	{0xFAD0, UNICODE_NORM_QC_NO},
	{0xFAD1, UNICODE_NORM_QC_NO},
	{0xFAD2, UNICODE_NORM_QC_NO},
	{0xFAD3, UNICODE_NORM_QC_NO},
	{0xFAD4, UNICODE_NORM_QC_NO},
	{0xFAD5, UNICODE_NORM_QC_NO},
	{0xFAD6, UNICODE_NORM_QC_NO},
	{0xFAD7, UNICODE_NORM_QC_NO},
	{0xFAD8, UNICODE_NORM_QC_NO},
	{0xFAD9, UNICODE_NORM_QC_NO},
	{0xFB1D, UNICODE_NORM_QC_NO},
	{0xFB1F, UNICODE_NORM_QC_NO},
	{0xFB2A, UNICODE_NORM_QC_NO},
	{0xFB2B, UNICODE_NORM_QC_NO},
	{0xFB2C, UNICODE_NORM_QC_NO},
	{0xFB2D, UNICODE_NORM_QC_NO},
	{0xFB2E, UNICODE_NORM_QC_NO},
	{0xFB2F, UNICODE_NORM_QC_NO},
	{0xFB30, UNICODE_NORM_QC_NO},
	{0xFB31, UNICODE_NORM_QC_NO},
	{0xFB32, UNICODE_NORM_QC_NO},
	{0xFB33, UNICODE_NORM_QC_NO},
	{0xFB34, UNICODE_NORM_QC_NO},
	{0xFB35, UNICODE_NORM_QC_NO},
	{0xFB36, UNICODE_NORM_QC_NO},
	{0xFB38, UNICODE_NORM_QC_NO},
	{0xFB39, UNICODE_NORM_QC_NO},
	{0xFB3A, UNICODE_NORM_QC_NO},
	{0xFB3B, UNICODE_NORM_QC_NO},
	{0xFB3C, UNICODE_NORM_QC_NO},
	{0xFB3E, UNICODE_NORM_QC_NO},
	{0xFB40, UNICODE_NORM_QC_NO},
	{0xFB41, UNICODE_NORM_QC_NO},
	{0xFB43, UNICODE_NORM_QC_NO},
	{0xFB44, UNICODE_NORM_QC_NO},
	{0xFB46, UNICODE_NORM_QC_NO},
	{0xFB47, UNICODE_NORM_QC_NO},
	{0xFB48, UNICODE_NORM_QC_NO},
	{0xFB49, UNICODE_NORM_QC_NO},
	{0xFB4A, UNICODE_NORM_QC_NO},
	{0xFB4B, UNICODE_NORM_QC_NO},
	{0xFB4C, UNICODE_NORM_QC_NO},
	{0xFB4D, UNICODE_NORM_QC_NO},
	{0xFB4E, UNICODE_NORM_QC_NO},
	{0x110BA, UNICODE_NORM_QC_MAYBE},
	{0x11127, UNICODE_NORM_QC_MAYBE},
	{0x1133E, UNICODE_NORM_QC_MAYBE},
	{0x11357, UNICODE_NORM_QC_MAYBE},
	{0x114B0, UNICODE_NORM_QC_MAYBE},
	{0x114BA, UNICODE_NORM_QC_MAYBE},
	{0x114BD, UNICODE_NORM_QC_MAYBE},
	{0x115AF, UNICODE_NORM_QC_MAYBE},
	{0x11930, UNICODE_NORM_QC_MAYBE},
	{0x1D15E, UNICODE_NORM_QC_NO},
	{0x1D15F, UNICODE_NORM_QC_NO},
	{0x1D160, UNICODE_NORM_QC_NO},
	{0x1D161, UNICODE_NORM_QC_NO},
	{0x1D162, UNICODE_NORM_QC_NO},
	{0x1D163, UNICODE_NORM_QC_NO},
	{0x1D164, UNICODE_NORM_QC_NO},
	{0x1D1BB, UNICODE_NORM_QC_NO},
	{0x1D1BC, UNICODE_NORM_QC_NO},
	{0x1D1BD, UNICODE_NORM_QC_NO},
	{0x1D1BE, UNICODE_NORM_QC_NO},
	{0x1D1BF, UNICODE_NORM_QC_NO},
	{0x1D1C0, UNICODE_NORM_QC_NO},
	{0x2F800, UNICODE_NORM_QC_NO},
	{0x2F801, UNICODE_NORM_QC_NO},
	{0x2F802, UNICODE_NORM_QC_NO},
	{0x2F803, UNICODE_NORM_QC_NO},
	{0x2F804, UNICODE_NORM_QC_NO},
	{0x2F805, UNICODE_NORM_QC_NO},
	{0x2F806, UNICODE_NORM_QC_NO},
	{0x2F807, UNICODE_NORM_QC_NO},
	{0x2F808, UNICODE_NORM_QC_NO},
	{0x2F809, UNICODE_NORM_QC_NO},
	{0x2F80A, UNICODE_NORM_QC_NO},
	{0x2F80B, UNICODE_NORM_QC_NO},
	{0x2F80C, UNICODE_NORM_QC_NO},
	{0x2F80D, UNICODE_NORM_QC_NO},
	{0x2F80E, UNICODE_NORM_QC_NO},
	{0x2F80F, UNICODE_NORM_QC_NO},
	{0x2F810, UNICODE_NORM_QC_NO},
	{0x2F811, UNICODE_NORM_QC_NO},
	{0x2F812, UNICODE_NORM_QC_NO},
	{0x2F813, UNICODE_NORM_QC_NO},
	{0x2F814, UNICODE_NORM_QC_NO},
	{0x2F815, UNICODE_NORM_QC_NO},
	{0x2F816, UNICODE_NORM_QC_NO},
	{0x2F817, UNICODE_NORM_QC_NO},
	{0x2F818, UNICODE_NORM_QC_NO},
	{0x2F819, UNICODE_NORM_QC_NO},
	{0x2F81A, UNICODE_NORM_QC_NO},
	{0x2F81B, UNICODE_NORM_QC_NO},
	{0x2F81C, UNICODE_NORM_QC_NO},
	{0x2F81D, UNICODE_NORM_QC_NO},
	{0x2F81E, UNICODE_NORM_QC_NO},
	{0x2F81F, UNICODE_NORM_QC_NO},
	{0x2F820, UNICODE_NORM_QC_NO},
	{0x2F821, UNICODE_NORM_QC_NO},
	{0x2F822, UNICODE_NORM_QC_NO},
	{0x2F823, UNICODE_NORM_QC_NO},
	{0x2F824, UNICODE_NORM_QC_NO},
	{0x2F825, UNICODE_NORM_QC_NO},
	{0x2F826, UNICODE_NORM_QC_NO},
	{0x2F827, UNICODE_NORM_QC_NO},
	{0x2F828, UNICODE_NORM_QC_NO},
	{0x2F829, UNICODE_NORM_QC_NO},
	{0x2F82A, UNICODE_NORM_QC_NO},
	{0x2F82B, UNICODE_NORM_QC_NO},
	{0x2F82C, UNICODE_NORM_QC_NO},
	{0x2F82D, UNICODE_NORM_QC_NO},
	{0x2F82E, UNICODE_NORM_QC_NO},
	{0x2F82F, UNICODE_NORM_QC_NO},
	{0x2F830, UNICODE_NORM_QC_NO},
	{0x2F831, UNICODE_NORM_QC_NO},
	{0x2F832, UNICODE_NORM_QC_NO},
	{0x2F833, UNICODE_NORM_QC_NO},
	{0x2F834, UNICODE_NORM_QC_NO},
	{0x2F835, UNICODE_NORM_QC_NO},
	{0x2F836, UNICODE_NORM_QC_NO},
	{0x2F837, UNICODE_NORM_QC_NO},
	{0x2F838, UNICODE_NORM_QC_NO},
	{0x2F839, UNICODE_NORM_QC_NO},
	{0x2F83A, UNICODE_NORM_QC_NO},
	{0x2F83B, UNICODE_NORM_QC_NO},
	{0x2F83C, UNICODE_NORM_QC_NO},
	{0x2F83D, UNICODE_NORM_QC_NO},
	{0x2F83E, UNICODE_NORM_QC_NO},
	{0x2F83F, UNICODE_NORM_QC_NO},
	{0x2F840, UNICODE_NORM_QC_NO},
	{0x2F841, UNICODE_NORM_QC_NO},
	{0x2F842, UNICODE_NORM_QC_NO},
	{0x2F843, UNICODE_NORM_QC_NO},
	{0x2F844, UNICODE_NORM_QC_NO},
	{0x2F845, UNICODE_NORM_QC_NO},
	{0x2F846, UNICODE_NORM_QC_NO},
	{0x2F847, UNICODE_NORM_QC_NO},
	{0x2F848, UNICODE_NORM_QC_NO},
	{0x2F849, UNICODE_NORM_QC_NO},
	{0x2F84A, UNICODE_NORM_QC_NO},
	{0x2F84B, UNICODE_NORM_QC_NO},
	{0x2F84C, UNICODE_NORM_QC_NO},
	{0x2F84D, UNICODE_NORM_QC_NO},
	{0x2F84E, UNICODE_NORM_QC_NO},
	{0x2F84F, UNICODE_NORM_QC_NO},
	{0x2F850, UNICODE_NORM_QC_NO},
	{0x2F851, UNICODE_NORM_QC_NO},
	{0x2F852, UNICODE_NORM_QC_NO},
	{0x2F853, UNICODE_NORM_QC_NO},
	{0x2F854, UNICODE_NORM_QC_NO},
	{0x2F855, UNICODE_NORM_QC_NO},
	{0x2F856, UNICODE_NORM_QC_NO},
	{0x2F857, UNICODE_NORM_QC_NO},
	{0x2F858, UNICODE_NORM_QC_NO},
	{0x2F859, UNICODE_NORM_QC_NO},
	{0x2F85A, UNICODE_NORM_QC_NO},
	{0x2F85B, UNICODE_NORM_QC_NO},
	{0x2F85C, UNICODE_NORM_QC_NO},
	{0x2F85D, UNICODE_NORM_QC_NO},
	{0x2F85E, UNICODE_NORM_QC_NO},
	{0x2F85F, UNICODE_NORM_QC_NO},
	{0x2F860, UNICODE_NORM_QC_NO},
	{0x2F861, UNICODE_NORM_QC_NO},
	{0x2F862, UNICODE_NORM_QC_NO},
	{0x2F863, UNICODE_NORM_QC_NO},
	{0x2F864, UNICODE_NORM_QC_NO},
	{0x2F865, UNICODE_NORM_QC_NO},
	{0x2F866, UNICODE_NORM_QC_NO},
	{0x2F867, UNICODE_NORM_QC_NO},
	{0x2F868, UNICODE_NORM_QC_NO},
	{0x2F869, UNICODE_NORM_QC_NO},
	{0x2F86A, UNICODE_NORM_QC_NO},
	{0x2F86B, UNICODE_NORM_QC_NO},
	{0x2F86C, UNICODE_NORM_QC_NO},
	{0x2F86D, UNICODE_NORM_QC_NO},
	{0x2F86E, UNICODE_NORM_QC_NO},
	{0x2F86F, UNICODE_NORM_QC_NO},
	{0x2F870, UNICODE_NORM_QC_NO},
	{0x2F871, UNICODE_NORM_QC_NO},
	{0x2F872, UNICODE_NORM_QC_NO},
	{0x2F873, UNICODE_NORM_QC_NO},
	{0x2F874, UNICODE_NORM_QC_NO},
	{0x2F875, UNICODE_NORM_QC_NO},
	{0x2F876, UNICODE_NORM_QC_NO},
	{0x2F877, UNICODE_NORM_QC_NO},
	{0x2F878, UNICODE_NORM_QC_NO},
	{0x2F879, UNICODE_NORM_QC_NO},
	{0x2F87A, UNICODE_NORM_QC_NO},
	{0x2F87B, UNICODE_NORM_QC_NO},
	{0x2F87C, UNICODE_NORM_QC_NO},
	{0x2F87D, UNICODE_NORM_QC_NO},
	{0x2F87E, UNICODE_NORM_QC_NO},
	{0x2F87F, UNICODE_NORM_QC_NO},
	{0x2F880, UNICODE_NORM_QC_NO},
	{0x2F881, UNICODE_NORM_QC_NO},
	{0x2F882, UNICODE_NORM_QC_NO},
	{0x2F883, UNICODE_NORM_QC_NO},
	{0x2F884, UNICODE_NORM_QC_NO},
	{0x2F885, UNICODE_NORM_QC_NO},
	{0x2F886, UNICODE_NORM_QC_NO},
	{0x2F887, UNICODE_NORM_QC_NO},
	{0x2F888, UNICODE_NORM_QC_NO},
	{0x2F889, UNICODE_NORM_QC_NO},
	{0x2F88A, UNICODE_NORM_QC_NO},
	{0x2F88B, UNICODE_NORM_QC_NO},
	{0x2F88C, UNICODE_NORM_QC_NO},
	{0x2F88D, UNICODE_NORM_QC_NO},
	{0x2F88E, UNICODE_NORM_QC_NO},
	{0x2F88F, UNICODE_NORM_QC_NO},
	{0x2F890, UNICODE_NORM_QC_NO},
	{0x2F891, UNICODE_NORM_QC_NO},
	{0x2F892, UNICODE_NORM_QC_NO},
	{0x2F893, UNICODE_NORM_QC_NO},
	{0x2F894, UNICODE_NORM_QC_NO},
	{0x2F895, UNICODE_NORM_QC_NO},
	{0x2F896, UNICODE_NORM_QC_NO},
	{0x2F897, UNICODE_NORM_QC_NO},
	{0x2F898, UNICODE_NORM_QC_NO},
	{0x2F899, UNICODE_NORM_QC_NO},
	{0x2F89A, UNICODE_NORM_QC_NO},
	{0x2F89B, UNICODE_NORM_QC_NO},
	{0x2F89C, UNICODE_NORM_QC_NO},
	{0x2F89D, UNICODE_NORM_QC_NO},
	{0x2F89E, UNICODE_NORM_QC_NO},
	{0x2F89F, UNICODE_NORM_QC_NO},
	{0x2F8A0, UNICODE_NORM_QC_NO},
	{0x2F8A1, UNICODE_NORM_QC_NO},
	{0x2F8A2, UNICODE_NORM_QC_NO},
	{0x2F8A3, UNICODE_NORM_QC_NO},
	{0x2F8A4, UNICODE_NORM_QC_NO},
	{0x2F8A5, UNICODE_NORM_QC_NO},
	{0x2F8A6, UNICODE_NORM_QC_NO},
	{0x2F8A7, UNICODE_NORM_QC_NO},
	{0x2F8A8, UNICODE_NORM_QC_NO},
	{0x2F8A9, UNICODE_NORM_QC_NO},
	{0x2F8AA, UNICODE_NORM_QC_NO},
	{0x2F8AB, UNICODE_NORM_QC_NO},
	{0x2F8AC, UNICODE_NORM_QC_NO},
	{0x2F8AD, UNICODE_NORM_QC_NO},
	{0x2F8AE, UNICODE_NORM_QC_NO},
	{0x2F8AF, UNICODE_NORM_QC_NO},
	{0x2F8B0, UNICODE_NORM_QC_NO},
	{0x2F8B1, UNICODE_NORM_QC_NO},
	{0x2F8B2, UNICODE_NORM_QC_NO},
	{0x2F8B3, UNICODE_NORM_QC_NO},
	{0x2F8B4, UNICODE_NORM_QC_NO},
	{0x2F8B5, UNICODE_NORM_QC_NO},
	{0x2F8B6, UNICODE_NORM_QC_NO},
	{0x2F8B7, UNICODE_NORM_QC_NO},
	{0x2F8B8, UNICODE_NORM_QC_NO},
	{0x2F8B9, UNICODE_NORM_QC_NO},
	{0x2F8BA, UNICODE_NORM_QC_NO},
	{0x2F8BB, UNICODE_NORM_QC_NO},
	{0x2F8BC, UNICODE_NORM_QC_NO},
	{0x2F8BD, UNICODE_NORM_QC_NO},
	{0x2F8BE, UNICODE_NORM_QC_NO},
	{0x2F8BF, UNICODE_NORM_QC_NO},
	{0x2F8C0, UNICODE_NORM_QC_NO},
	{0x2F8C1, UNICODE_NORM_QC_NO},
	{0x2F8C2, UNICODE_NORM_QC_NO},
	{0x2F8C3, UNICODE_NORM_QC_NO},
	{0x2F8C4, UNICODE_NORM_QC_NO},
	{0x2F8C5, UNICODE_NORM_QC_NO},
	{0x2F8C6, UNICODE_NORM_QC_NO},
	{0x2F8C7, UNICODE_NORM_QC_NO},
	{0x2F8C8, UNICODE_NORM_QC_NO},
	{0x2F8C9, UNICODE_NORM_QC_NO},
	{0x2F8CA, UNICODE_NORM_QC_NO},
	{0x2F8CB, UNICODE_NORM_QC_NO},
	{0x2F8CC, UNICODE_NORM_QC_NO},
	{0x2F8CD, UNICODE_NORM_QC_NO},
	{0x2F8CE, UNICODE_NORM_QC_NO},
	{0x2F8CF, UNICODE_NORM_QC_NO},
	{0x2F8D0, UNICODE_NORM_QC_NO},
	{0x2F8D1, UNICODE_NORM_QC_NO},
	{0x2F8D2, UNICODE_NORM_QC_NO},
	{0x2F8D3, UNICODE_NORM_QC_NO},
	{0x2F8D4, UNICODE_NORM_QC_NO},
	{0x2F8D5, UNICODE_NORM_QC_NO},
	{0x2F8D6, UNICODE_NORM_QC_NO},
	{0x2F8D7, UNICODE_NORM_QC_NO},
	{0x2F8D8, UNICODE_NORM_QC_NO},
	{0x2F8D9, UNICODE_NORM_QC_NO},
	{0x2F8DA, UNICODE_NORM_QC_NO},
	{0x2F8DB, UNICODE_NORM_QC_NO},
	{0x2F8DC, UNICODE_NORM_QC_NO},
	{0x2F8DD, UNICODE_NORM_QC_NO},
	{0x2F8DE, UNICODE_NORM_QC_NO},
	{0x2F8DF, UNICODE_NORM_QC_NO},
	{0x2F8E0, UNICODE_NORM_QC_NO},
	{0x2F8E1, UNICODE_NORM_QC_NO},
	{0x2F8E2, UNICODE_NORM_QC_NO},
	{0x2F8E3, UNICODE_NORM_QC_NO},
	{0x2F8E4, UNICODE_NORM_QC_NO},
	{0x2F8E5, UNICODE_NORM_QC_NO},
	{0x2F8E6, UNICODE_NORM_QC_NO},
	{0x2F8E7, UNICODE_NORM_QC_NO},
	{0x2F8E8, UNICODE_NORM_QC_NO},
	{0x2F8E9, UNICODE_NORM_QC_NO},
	{0x2F8EA, UNICODE_NORM_QC_NO},
	{0x2F8EB, UNICODE_NORM_QC_NO},
	{0x2F8EC, UNICODE_NORM_QC_NO},
	{0x2F8ED, UNICODE_NORM_QC_NO},
	{0x2F8EE, UNICODE_NORM_QC_NO},
	{0x2F8EF, UNICODE_NORM_QC_NO},
	{0x2F8F0, UNICODE_NORM_QC_NO},
	{0x2F8F1, UNICODE_NORM_QC_NO},
	{0x2F8F2, UNICODE_NORM_QC_NO},
	{0x2F8F3, UNICODE_NORM_QC_NO},
	{0x2F8F4, UNICODE_NORM_QC_NO},
	{0x2F8F5, UNICODE_NORM_QC_NO},
	{0x2F8F6, UNICODE_NORM_QC_NO},
	{0x2F8F7, UNICODE_NORM_QC_NO},
	{0x2F8F8, UNICODE_NORM_QC_NO},
	{0x2F8F9, UNICODE_NORM_QC_NO},
	{0x2F8FA, UNICODE_NORM_QC_NO},
	{0x2F8FB, UNICODE_NORM_QC_NO},
	{0x2F8FC, UNICODE_NORM_QC_NO},
	{0x2F8FD, UNICODE_NORM_QC_NO},
	{0x2F8FE, UNICODE_NORM_QC_NO},
	{0x2F8FF, UNICODE_NORM_QC_NO},
	{0x2F900, UNICODE_NORM_QC_NO},
	{0x2F901, UNICODE_NORM_QC_NO},
	{0x2F902, UNICODE_NORM_QC_NO},
	{0x2F903, UNICODE_NORM_QC_NO},
	{0x2F904, UNICODE_NORM_QC_NO},
	{0x2F905, UNICODE_NORM_QC_NO},
	{0x2F906, UNICODE_NORM_QC_NO},
	{0x2F907, UNICODE_NORM_QC_NO},
	{0x2F908, UNICODE_NORM_QC_NO},
	{0x2F909, UNICODE_NORM_QC_NO},
	{0x2F90A, UNICODE_NORM_QC_NO},
	{0x2F90B, UNICODE_NORM_QC_NO},
	{0x2F90C, UNICODE_NORM_QC_NO},
	{0x2F90D, UNICODE_NORM_QC_NO},
	{0x2F90E, UNICODE_NORM_QC_NO},
	{0x2F90F, UNICODE_NORM_QC_NO},
	{0x2F910, UNICODE_NORM_QC_NO},
	{0x2F911, UNICODE_NORM_QC_NO},
	{0x2F912, UNICODE_NORM_QC_NO},
	{0x2F913, UNICODE_NORM_QC_NO},
	{0x2F914, UNICODE_NORM_QC_NO},
	{0x2F915, UNICODE_NORM_QC_NO},
	{0x2F916, UNICODE_NORM_QC_NO},
	{0x2F917, UNICODE_NORM_QC_NO},
	{0x2F918, UNICODE_NORM_QC_NO},
	{0x2F919, UNICODE_NORM_QC_NO},
	{0x2F91A, UNICODE_NORM_QC_NO},
	{0x2F91B, UNICODE_NORM_QC_NO},
	{0x2F91C, UNICODE_NORM_QC_NO},
	{0x2F91D, UNICODE_NORM_QC_NO},
	{0x2F91E, UNICODE_NORM_QC_NO},
	{0x2F91F, UNICODE_NORM_QC_NO},
	{0x2F920, UNICODE_NORM_QC_NO},
	{0x2F921, UNICODE_NORM_QC_NO},
	{0x2F922, UNICODE_NORM_QC_NO},
	{0x2F923, UNICODE_NORM_QC_NO},
	{0x2F924, UNICODE_NORM_QC_NO},
	{0x2F925, UNICODE_NORM_QC_NO},
	{0x2F926, UNICODE_NORM_QC_NO},
	{0x2F927, UNICODE_NORM_QC_NO},
	{0x2F928, UNICODE_NORM_QC_NO},
	{0x2F929, UNICODE_NORM_QC_NO},
	{0x2F92A, UNICODE_NORM_QC_NO},
	{0x2F92B, UNICODE_NORM_QC_NO},
	{0x2F92C, UNICODE_NORM_QC_NO},
	{0x2F92D, UNICODE_NORM_QC_NO},
	{0x2F92E, UNICODE_NORM_QC_NO},
	{0x2F92F, UNICODE_NORM_QC_NO},
	{0x2F930, UNICODE_NORM_QC_NO},
	{0x2F931, UNICODE_NORM_QC_NO},
	{0x2F932, UNICODE_NORM_QC_NO},
	{0x2F933, UNICODE_NORM_QC_NO},
	{0x2F934, UNICODE_NORM_QC_NO},
	{0x2F935, UNICODE_NORM_QC_NO},
	{0x2F936, UNICODE_NORM_QC_NO},
	{0x2F937, UNICODE_NORM_QC_NO},
	{0x2F938, UNICODE_NORM_QC_NO},
	{0x2F939, UNICODE_NORM_QC_NO},
	{0x2F93A, UNICODE_NORM_QC_NO},
	{0x2F93B, UNICODE_NORM_QC_NO},
	{0x2F93C, UNICODE_NORM_QC_NO},
	{0x2F93D, UNICODE_NORM_QC_NO},
	{0x2F93E, UNICODE_NORM_QC_NO},
	{0x2F93F, UNICODE_NORM_QC_NO},
	{0x2F940, UNICODE_NORM_QC_NO},
	{0x2F941, UNICODE_NORM_QC_NO},
	{0x2F942, UNICODE_NORM_QC_NO},
	{0x2F943, UNICODE_NORM_QC_NO},
	{0x2F944, UNICODE_NORM_QC_NO},
	{0x2F945, UNICODE_NORM_QC_NO},
	{0x2F946, UNICODE_NORM_QC_NO},
	{0x2F947, UNICODE_NORM_QC_NO},
	{0x2F948, UNICODE_NORM_QC_NO},
	{0x2F949, UNICODE_NORM_QC_NO},
	{0x2F94A, UNICODE_NORM_QC_NO},
	{0x2F94B, UNICODE_NORM_QC_NO},
	{0x2F94C, UNICODE_NORM_QC_NO},
	{0x2F94D, UNICODE_NORM_QC_NO},
	{0x2F94E, UNICODE_NORM_QC_NO},
	{0x2F94F, UNICODE_NORM_QC_NO},
	{0x2F950, UNICODE_NORM_QC_NO},
	{0x2F951, UNICODE_NORM_QC_NO},
	{0x2F952, UNICODE_NORM_QC_NO},
	{0x2F953, UNICODE_NORM_QC_NO},
	{0x2F954, UNICODE_NORM_QC_NO},
	{0x2F955, UNICODE_NORM_QC_NO},
	{0x2F956, UNICODE_NORM_QC_NO},
	{0x2F957, UNICODE_NORM_QC_NO},
	{0x2F958, UNICODE_NORM_QC_NO},
	{0x2F959, UNICODE_NORM_QC_NO},
	{0x2F95A, UNICODE_NORM_QC_NO},
	{0x2F95B, UNICODE_NORM_QC_NO},
	{0x2F95C, UNICODE_NORM_QC_NO},
	{0x2F95D, UNICODE_NORM_QC_NO},
	{0x2F95E, UNICODE_NORM_QC_NO},
	{0x2F95F, UNICODE_NORM_QC_NO},
	{0x2F960, UNICODE_NORM_QC_NO},
	{0x2F961, UNICODE_NORM_QC_NO},
	{0x2F962, UNICODE_NORM_QC_NO},
	{0x2F963, UNICODE_NORM_QC_NO},
	{0x2F964, UNICODE_NORM_QC_NO},
	{0x2F965, UNICODE_NORM_QC_NO},
	{0x2F966, UNICODE_NORM_QC_NO},
	{0x2F967, UNICODE_NORM_QC_NO},
	{0x2F968, UNICODE_NORM_QC_NO},
	{0x2F969, UNICODE_NORM_QC_NO},
	{0x2F96A, UNICODE_NORM_QC_NO},
	{0x2F96B, UNICODE_NORM_QC_NO},
	{0x2F96C, UNICODE_NORM_QC_NO},
	{0x2F96D, UNICODE_NORM_QC_NO},
	{0x2F96E, UNICODE_NORM_QC_NO},
	{0x2F96F, UNICODE_NORM_QC_NO},
	{0x2F970, UNICODE_NORM_QC_NO},
	{0x2F971, UNICODE_NORM_QC_NO},
	{0x2F972, UNICODE_NORM_QC_NO},
	{0x2F973, UNICODE_NORM_QC_NO},
	{0x2F974, UNICODE_NORM_QC_NO},
	{0x2F975, UNICODE_NORM_QC_NO},
	{0x2F976, UNICODE_NORM_QC_NO},
	{0x2F977, UNICODE_NORM_QC_NO},
	{0x2F978, UNICODE_NORM_QC_NO},
	{0x2F979, UNICODE_NORM_QC_NO},
	{0x2F97A, UNICODE_NORM_QC_NO},
	{0x2F97B, UNICODE_NORM_QC_NO},
	{0x2F97C, UNICODE_NORM_QC_NO},
	{0x2F97D, UNICODE_NORM_QC_NO},
	{0x2F97E, UNICODE_NORM_QC_NO},
	{0x2F97F, UNICODE_NORM_QC_NO},
	{0x2F980, UNICODE_NORM_QC_NO},
	{0x2F981, UNICODE_NORM_QC_NO},
	{0x2F982, UNICODE_NORM_QC_NO},
	{0x2F983, UNICODE_NORM_QC_NO},
	{0x2F984, UNICODE_NORM_QC_NO},
	{0x2F985, UNICODE_NORM_QC_NO},
	{0x2F986, UNICODE_NORM_QC_NO},
	{0x2F987, UNICODE_NORM_QC_NO},
	{0x2F988, UNICODE_NORM_QC_NO},
	{0x2F989, UNICODE_NORM_QC_NO},
	{0x2F98A, UNICODE_NORM_QC_NO},
	{0x2F98B, UNICODE_NORM_QC_NO},
	{0x2F98C, UNICODE_NORM_QC_NO},
	{0x2F98D, UNICODE_NORM_QC_NO},
	{0x2F98E, UNICODE_NORM_QC_NO},
	{0x2F98F, UNICODE_NORM_QC_NO},
	{0x2F990, UNICODE_NORM_QC_NO},
	{0x2F991, UNICODE_NORM_QC_NO},
	{0x2F992, UNICODE_NORM_QC_NO},
	{0x2F993, UNICODE_NORM_QC_NO},
	{0x2F994, UNICODE_NORM_QC_NO},
	{0x2F995, UNICODE_NORM_QC_NO},
	{0x2F996, UNICODE_NORM_QC_NO},
	{0x2F997, UNICODE_NORM_QC_NO},
	{0x2F998, UNICODE_NORM_QC_NO},
	{0x2F999, UNICODE_NORM_QC_NO},
	{0x2F99A, UNICODE_NORM_QC_NO},
	{0x2F99B, UNICODE_NORM_QC_NO},
	{0x2F99C, UNICODE_NORM_QC_NO},
	{0x2F99D, UNICODE_NORM_QC_NO},
	{0x2F99E, UNICODE_NORM_QC_NO},
	{0x2F99F, UNICODE_NORM_QC_NO},
	{0x2F9A0, UNICODE_NORM_QC_NO},
	{0x2F9A1, UNICODE_NORM_QC_NO},
	{0x2F9A2, UNICODE_NORM_QC_NO},
	{0x2F9A3, UNICODE_NORM_QC_NO},
	{0x2F9A4, UNICODE_NORM_QC_NO},
	{0x2F9A5, UNICODE_NORM_QC_NO},
	{0x2F9A6, UNICODE_NORM_QC_NO},
	{0x2F9A7, UNICODE_NORM_QC_NO},
	{0x2F9A8, UNICODE_NORM_QC_NO},
	{0x2F9A9, UNICODE_NORM_QC_NO},
	{0x2F9AA, UNICODE_NORM_QC_NO},
	{0x2F9AB, UNICODE_NORM_QC_NO},
	{0x2F9AC, UNICODE_NORM_QC_NO},
	{0x2F9AD, UNICODE_NORM_QC_NO},
	{0x2F9AE, UNICODE_NORM_QC_NO},
	{0x2F9AF, UNICODE_NORM_QC_NO},
	{0x2F9B0, UNICODE_NORM_QC_NO},
	{0x2F9B1, UNICODE_NORM_QC_NO},
	{0x2F9B2, UNICODE_NORM_QC_NO},
	{0x2F9B3, UNICODE_NORM_QC_NO},
	{0x2F9B4, UNICODE_NORM_QC_NO},
	{0x2F9B5, UNICODE_NORM_QC_NO},
	{0x2F9B6, UNICODE_NORM_QC_NO},
	{0x2F9B7, UNICODE_NORM_QC_NO},
	{0x2F9B8, UNICODE_NORM_QC_NO},
	{0x2F9B9, UNICODE_NORM_QC_NO},
	{0x2F9BA, UNICODE_NORM_QC_NO},
	{0x2F9BB, UNICODE_NORM_QC_NO},
	{0x2F9BC, UNICODE_NORM_QC_NO},
	{0x2F9BD, UNICODE_NORM_QC_NO},
	{0x2F9BE, UNICODE_NORM_QC_NO},
	{0x2F9BF, UNICODE_NORM_QC_NO},
	{0x2F9C0, UNICODE_NORM_QC_NO},
	{0x2F9C1, UNICODE_NORM_QC_NO},
	{0x2F9C2, UNICODE_NORM_QC_NO},
	{0x2F9C3, UNICODE_NORM_QC_NO},
	{0x2F9C4, UNICODE_NORM_QC_NO},
	{0x2F9C5, UNICODE_NORM_QC_NO},
	{0x2F9C6, UNICODE_NORM_QC_NO},
	{0x2F9C7, UNICODE_NORM_QC_NO},
	{0x2F9C8, UNICODE_NORM_QC_NO},
	{0x2F9C9, UNICODE_NORM_QC_NO},
	{0x2F9CA, UNICODE_NORM_QC_NO},
	{0x2F9CB, UNICODE_NORM_QC_NO},
	{0x2F9CC, UNICODE_NORM_QC_NO},
	{0x2F9CD, UNICODE_NORM_QC_NO},
	{0x2F9CE, UNICODE_NORM_QC_NO},
	{0x2F9CF, UNICODE_NORM_QC_NO},
	{0x2F9D0, UNICODE_NORM_QC_NO},
	{0x2F9D1, UNICODE_NORM_QC_NO},
	{0x2F9D2, UNICODE_NORM_QC_NO},
	{0x2F9D3, UNICODE_NORM_QC_NO},
	{0x2F9D4, UNICODE_NORM_QC_NO},
	{0x2F9D5, UNICODE_NORM_QC_NO},
	{0x2F9D6, UNICODE_NORM_QC_NO},
	{0x2F9D7, UNICODE_NORM_QC_NO},
	{0x2F9D8, UNICODE_NORM_QC_NO},
	{0x2F9D9, UNICODE_NORM_QC_NO},
	{0x2F9DA, UNICODE_NORM_QC_NO},
	{0x2F9DB, UNICODE_NORM_QC_NO},
	{0x2F9DC, UNICODE_NORM_QC_NO},
	{0x2F9DD, UNICODE_NORM_QC_NO},
	{0x2F9DE, UNICODE_NORM_QC_NO},
	{0x2F9DF, UNICODE_NORM_QC_NO},
	{0x2F9E0, UNICODE_NORM_QC_NO},
	{0x2F9E1, UNICODE_NORM_QC_NO},
	{0x2F9E2, UNICODE_NORM_QC_NO},
	{0x2F9E3, UNICODE_NORM_QC_NO},
	{0x2F9E4, UNICODE_NORM_QC_NO},
	{0x2F9E5, UNICODE_NORM_QC_NO},
	{0x2F9E6, UNICODE_NORM_QC_NO},
	{0x2F9E7, UNICODE_NORM_QC_NO},
	{0x2F9E8, UNICODE_NORM_QC_NO},
	{0x2F9E9, UNICODE_NORM_QC_NO},
	{0x2F9EA, UNICODE_NORM_QC_NO},
	{0x2F9EB, UNICODE_NORM_QC_NO},
	{0x2F9EC, UNICODE_NORM_QC_NO},
	{0x2F9ED, UNICODE_NORM_QC_NO},
	{0x2F9EE, UNICODE_NORM_QC_NO},
	{0x2F9EF, UNICODE_NORM_QC_NO},
	{0x2F9F0, UNICODE_NORM_QC_NO},
	{0x2F9F1, UNICODE_NORM_QC_NO},
	{0x2F9F2, UNICODE_NORM_QC_NO},
	{0x2F9F3, UNICODE_NORM_QC_NO},
	{0x2F9F4, UNICODE_NORM_QC_NO},
	{0x2F9F5, UNICODE_NORM_QC_NO},
	{0x2F9F6, UNICODE_NORM_QC_NO},
	{0x2F9F7, UNICODE_NORM_QC_NO},
	{0x2F9F8, UNICODE_NORM_QC_NO},
	{0x2F9F9, UNICODE_NORM_QC_NO},
	{0x2F9FA, UNICODE_NORM_QC_NO},
	{0x2F9FB, UNICODE_NORM_QC_NO},
	{0x2F9FC, UNICODE_NORM_QC_NO},
	{0x2F9FD, UNICODE_NORM_QC_NO},
	{0x2F9FE, UNICODE_NORM_QC_NO},
	{0x2F9FF, UNICODE_NORM_QC_NO},
	{0x2FA00, UNICODE_NORM_QC_NO},
	{0x2FA01, UNICODE_NORM_QC_NO},
	{0x2FA02, UNICODE_NORM_QC_NO},
	{0x2FA03, UNICODE_NORM_QC_NO},
	{0x2FA04, UNICODE_NORM_QC_NO},
	{0x2FA05, UNICODE_NORM_QC_NO},
	{0x2FA06, UNICODE_NORM_QC_NO},
	{0x2FA07, UNICODE_NORM_QC_NO},
	{0x2FA08, UNICODE_NORM_QC_NO},
	{0x2FA09, UNICODE_NORM_QC_NO},
	{0x2FA0A, UNICODE_NORM_QC_NO},
	{0x2FA0B, UNICODE_NORM_QC_NO},
	{0x2FA0C, UNICODE_NORM_QC_NO},
	{0x2FA0D, UNICODE_NORM_QC_NO},
	{0x2FA0E, UNICODE_NORM_QC_NO},
	{0x2FA0F, UNICODE_NORM_QC_NO},
	{0x2FA10, UNICODE_NORM_QC_NO},
	{0x2FA11, UNICODE_NORM_QC_NO},
	{0x2FA12, UNICODE_NORM_QC_NO},
	{0x2FA13, UNICODE_NORM_QC_NO},
	{0x2FA14, UNICODE_NORM_QC_NO},
	{0x2FA15, UNICODE_NORM_QC_NO},
	{0x2FA16, UNICODE_NORM_QC_NO},
	{0x2FA17, UNICODE_NORM_QC_NO},
	{0x2FA18, UNICODE_NORM_QC_NO},
	{0x2FA19, UNICODE_NORM_QC_NO},
	{0x2FA1A, UNICODE_NORM_QC_NO},
	{0x2FA1B, UNICODE_NORM_QC_NO},
	{0x2FA1C, UNICODE_NORM_QC_NO},
	{0x2FA1D, UNICODE_NORM_QC_NO},
};

/* Perfect hash function for NFC_QC */
static int
NFC_QC_hash_func(const void *key)
{
	static const int16 h[2463] = {
		0,     -2717, 0,     221,   1293,  223,   1295,  225,
		226,   241,   0,     229,   230,   231,   0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		-386,  0,     0,     0,     0,     0,     0,     0,
		-163,  0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		-246,  -175,  1260,  0,     0,     0,     -174,  -173,
		0,     -172,  0,     0,     0,     0,     0,     0,
		1049,  0,     300,   301,   1071,  0,     1071,  0,
		1071,  1071,  1057,  0,     0,     0,     0,     1061,
		0,     -1053, 1664,  0,     2956,  0,     0,     -13,
		0,     0,     0,     0,     2156,  0,     0,     0,
		0,     0,     0,     0,     71,    0,     1082,  0,
		1083,  1083,  0,     1084,  0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     359,   360,   361,
		-1091, 363,   -762,  -130,  -129,  -128,  -127,  -126,
		137,   -124,  -708,  -707,  -706,  -120,  -185,  -705,
		-117,  -184,  -1307, -114,  -113,  -112,  -111,  0,
		386,   387,   388,   389,   -90,   391,   171,   172,
		394,   -94,   -183,  397,   398,   399,   -98,   -225,
		402,   -1019, -636,  -1019, -225,  407,   408,   409,
		410,   411,   674,   413,   -171,  -170,  -169,  417,
		352,   -168,  420,   353,   -770,  423,   424,   425,
		426,   427,   428,   32767, 239,   239,   239,   239,
		239,   239,   239,   239,   239,   239,   239,   239,
		239,   239,   32767, 32767, 237,   32767, 236,   32767,
		32767, 234,   234,   234,   234,   617,   234,   234,
		234,   -2483, 234,   -1430, 1526,  -1430, 1527,  47,
		48,    471,   230,   32767, 32767, 32767, 227,   227,
		227,   227,   227,   227,   227,   227,   227,   227,
		227,   227,   227,   227,   227,   227,   227,   227,
		-159,  227,   227,   227,   227,   227,   227,   227,
		64,    227,   227,   227,   227,   227,   227,   227,
		227,   227,   227,   227,   227,   227,   227,   227,
		227,   227,   227,   227,   227,   227,   227,   227,
		-19,   52,    1487,  227,   227,   227,   53,    54,
		227,   55,    227,   227,   227,   227,   227,   227,
		1276,  227,   -989,  32767, 1296,  225,   1296,  225,
		1296,  1296,  1282,  225,   225,   225,   225,   1286,
		225,   -828,  1889,  225,   3181,  225,   225,   212,
		225,   225,   225,   225,   2381,  225,   225,   225,
		225,   225,   225,   225,   296,   225,   1307,  225,
		1308,  1308,  225,   1309,  225,   225,   225,   225,
		225,   225,   225,   225,   225,   225,   225,   225,
		225,   225,   225,   225,   225,   584,   585,   586,
		-866,  588,   -537,  95,    96,    97,    98,    99,
		362,   101,   -483,  -482,  -481,  105,   40,    -480,
		108,   41,    -1082, 111,   112,   113,   114,   225,
		611,   612,   613,   614,   135,   616,   396,   397,
		619,   131,   42,    622,   623,   624,   127,   0,
		627,   -794,  -411,  -794,  0,     632,   32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		-272,  32767, 32767, 32767, 0,     32767, 32767, 32767,
		32767, 32767, -166,  -165,  32767, 32767, 32767, 32767,
		-164,  0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 397,   32767, 396,   32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 386,
		0,     386,   386,   386,   386,   386,   386,   386,
		223,   386,   386,   386,   32767, 385,   385,   385,
		385,   385,   32767, 384,   32767, 383,   383,   32767,
		382,   382,   32767, 381,   381,   381,   381,   381,
		135,   206,   1641,  381,   32767, 32767, 32767, 32767,
		32767, 32767, -160,  32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 1148,  32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 0,
		32767, 32767, 32767, 0,     0,     32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, -257,  32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, -910,  -910,  32767, 32767,
		0,     32767, 0,     32767, 0,     32767, 0,     32767,
		147,   32767, 0,     32767, 0,     32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 0,     32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 143,   32767, 144,   32767, 145,
		32767, 146,   32767, 0,     32767, 148,   32767, 149,
		32767, 32767, 32767, -160,  32767, 32767, 32767, 32767,
		32767, 32767, 15,    32767, 32767, 0,     32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		145,   32767, 144,   32767, 32767, 32767, 32767, 32767,
		32767, 32767, 0,     32767, 32767, 32767, 32767, 32767,
		32767, 32767, 0,     -148,  32767, 32767, 32767, 32767,
		32767, 32767, 2009,  32767, 32767, 32767, 32767, 32767,
		32767, 32767, 0,     32767, 32767, 135,   -918,  32767,
		151,   32767, 32767, 0,     1,     2,     3,     4,
		133,   5,     6,     7,     8,     9,     10,    11,
		32767, 32767, -1248, 32767, 13,    154,   188,   188,
		32767, 32767, 32767, 32767, 32767, 155,   16,    32767,
		32767, 32767, 32767, 32767, 32767, -1853, -1054, 18,
		-1052, -1051, -1036, 22,    32767, 157,   32767, 28,
		23,    1077,  673,   25,    -2930, 0,     32767, 32767,
		32767, 32767, 32767, 27,    32767, 155,   32767, 154,
		32767, 32767, -62,   28,    -42,   30,    -1051, 32,
		-1050, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 34,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 129,   32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 672,   32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 0,     32767,
		32767, 32767, 32767, 32767, -156,  32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, -155,  32767, 32767,
		32767, 0,     0,     32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		73,    32767, 32767, 32767, 32767, 74,    32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 675,
		32767, 32767, 32767, 32767, 32767, 75,    32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 165,   32767, 32767, 32767, 166,   167,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 170,   32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 689,   690,   691,   692,   693,   694,   695,
		696,   697,   698,   699,   700,   701,   702,   703,
		704,   705,   706,   707,   708,   709,   710,   711,
		712,   713,   714,   715,   716,   717,   718,   719,
		720,   721,   722,   -304,  -303,  -302,  -301,  -300,
		-299,  -298,  -297,  930,   -295,  -294,  -293,  -292,
		-291,  -290,  -289,  -288,  -287,  -286,  -285,  -284,
		-283,  -282,  -281,  -280,  -279,  -278,  -277,  -276,
		-275,  753,   754,   755,   646,   757,   -712,  -1765,
		952,   -712,  2244,  -712,  2245,  765,   766,   767,
		768,   125,   770,   771,   772,   773,   774,   775,
		603,   777,   778,   779,   780,   781,   782,   783,
		784,   2011,  786,   787,   788,   789,   790,   791,
		792,   793,   794,   795,   796,   797,   798,   799,
		800,   801,   802,   803,   804,   805,   806,   603,
		603,   809,   603,   811,   603,   603,   814,   815,
		816,   817,   435,   819,   820,   821,   3539,  823,
		603,   -468,  603,   -468,  603,   603,   589,   831,
		603,   603,   603,   835,   836,   837,   838,   839,
		840,   841,   842,   843,   844,   845,   846,   847,
		848,   849,   850,   851,   852,   1239,  854,   855,
		856,   857,   858,   859,   860,   1024,  862,   863,
		864,   865,   866,   867,   868,   869,   870,   871,
		872,   873,   874,   875,   876,   877,   878,   879,
		880,   881,   882,   883,   884,   1131,  1061,  -373,
		888,   889,   890,   1065,  1065,  893,   1066,  895,
		896,   897,   898,   899,   900,   -148,  902,   603,
		603,   -166,  906,   -164,  908,   -162,  -161,  -146,
		912,   913,   914,   915,   -145,  917,   1971,  -745,
		920,   -2035, 922,   923,   937,   925,   926,   927,
		928,   -1227, 930,   931,   932,   933,   934,   935,
		936,   866,   938,   -143,  940,   -142,  -141,  943,
		-140,  32767, 945,   946,   947,   948,   949,   950,
		951,   952,   953,   954,   955,   956,   957,   958,
		959,   960,   961,   -65,   -64,   -63,   -62,   -61,
		-60,   -59,   -58,   1169,  -56,   -55,   -54,   -53,
		-52,   -51,   -50,   -49,   -48,   -47,   -46,   -45,
		-44,   -43,   -42,   -41,   -40,   -39,   -38,   -37,
		-36,   992,   993,   994,   885,   996,   -473,  -1526,
		1191,  -473,  2483,  -473,  2484,  1004,  1005,  1006,
		1007,  364,   1009,  1010,  1011,  1012,  1013,  1014,
		842,   1016,  1017,  1018,  1019,  1020,  1021,  1022,
		1023,  2250,  1025,  1026,  1027,  1028,  1029,  1030,
		1031,  1032,  1033,  1034,  1035,  1036,  1037,  1038,
		1039,  1040,  1041,  1042,  1043,  1044,  1045,  842,
		842,   1048,  842,   1050,  842,   842,   1053,  1054,
		1055,  1056,  674,   1058,  1059,  1060,  3778,  1062,
		842,   -229,  842,   -229,  842,   842,   828,   1070,
		842,   842,   842,   1074,  1075,  1076,  1077,  1078,
		1079,  1080,  1081,  1082,  1083,  1084,  1085,  1086,
		1087,  1088,  1089,  1090,  1091,  1478,  1093,  1094,
		1095,  1096,  1097,  1098,  1099,  1263,  1101,  1102,
		1103,  1104,  1105,  1106,  1107,  1108,  1109,  1110,
		1111,  1112,  1113,  1114,  1115,  1116,  1117,  1118,
		1119,  1120,  1121,  1122,  1123,  1370,  1300,  -134,
		1127,  1128,  1129,  1304,  1304,  1132,  1305,  1134,
		1135,  1136,  1137,  1138,  1139,  91,    1141,  842,
		842,   73,    1145,  75,    1147,  77,    78,    93,
		1151,  1152,  1153,  1154,  94,    1156,  2210,  -506,
		1159,  -1796, 1161,  1162,  1176,  1164,  1165,  1166,
		1167,  -988,  1169,  1170,  1171,  1172,  1173,  1174,
		1175,  1105,  1177,  96,    1179,  97,    98,    1182,
		99,    1184,  1185,  1186,  1187,  1188,  1189,  1190,
		1191,  1192,  1193,  1194,  1195,  1196,  1197,  1198,
		1199,  1200,  0,     174,   175,   176,   177,   178,
		179,   180,   181,   1408,  183,   184,   185,   186,
		187,   188,   189,   190,   191,   192,   193,   194,
		195,   196,   197,   198,   199,   200,   201,   202,
		203,   0,     0,     206,   0,     208,   0,     0,
		211,   212,   213,   214,   -168,  216,   217,   218,
		2936,  220,   0,     -1071, 0,     -1071, 0,     0,
		-14,   228,   0,     0,     0,     232,   233,   234,
		235,   236,   237,   238,   239,   240,   241,   242,
		243,   244,   245,   246,   247,   248,   249,   636,
		251,   252,   253,   254,   255,   256,   257,   421,
		259,   260,   261,   262,   263,   264,   265,   266,
		267,   268,   269,   270,   271,   272,   273,   274,
		275,   276,   277,   278,   279,   280,   281,   528,
		458,   -976,  285,   286,   287,   462,   462,   290,
		463,   292,   293,   294,   295,   296,   297,   -751,
		299,   0,     0,     -769,  303,   -767,  305,   -765,
		-764,  -749,  309,   310,   311,   312,   -748,  314,
		1368,  -1348, 317,   -2638, 319,   320,   334,   322,
		323,   324,   325,   -1830, 327,   328,   329,   330,
		331,   332,   333,   263,   335,   -746,  337,   -745,
		-744,  340,   -743,  342,   343,   344,   345,   346,
		347,   348,   349,   350,   351,   352,   353,   354,
		355,   356,   357,   358,   0,     0,     0,     1453,
		0,     1126,  495,   495,   495,   495,   495,   233,
		495,   1080,  1080,  1080,  495,   561,   1082,  495,
		563,   1687,  495,   495,   495,   495,   385,   0,
		0,     0,     0,     480,   0,     221,   221,   0,
		489,   579,   0,     0,     0,     498,   626,   0,
		1422,  1040,  1424,  631,   0,     0,     0,     0,
		0,     -262,  0,     585,   585,   585,   0,     66,
		587,   0,     68,    1192,  0,     0,     0,     0,
		0,     0,     32767, 32767, 32767, 32767, 669,   32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 670,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 142,   32767, 32767, 32767, 32767, 32767,
		32767, 32767, 115,   116,   117,   118,   119,   120,
		121,   122,   123,   124,   125,   126,   127,   128,
		129,   130,   131,   132,   133,   134,   135,   136,
		137,   138,   139,   140,   141,   32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     1027,  1027,  1027,
		1027,  1027,  1027,  1027,  1027,  -199,  1027,  1027,
		1027,  1027,  1027,  1027,  1027,  1027,  1027,  1027,
		1027,  1027,  1027,  1027,  1027,  1027,  1027,  1027,
		1027,  1027,  1027,  0,     0,     0,     110,   0,
		1470,  2524,  -192,  1473,  -1482, 1475,  -1481, 0,
		0,     0,     0,     644,   0,     0,     0,     0,
		0,     0,     173,   0,     0,     0,     0,     0,
		0,     0,     0,     -1226, 0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     204,   205,   0,     207,   0,     209,   210,
		0,     0,     0,     0,     383,   0,     0
	};

	const unsigned char *k = (const unsigned char *) key;
	size_t		keylen = 4;
	uint32		a = 0;
	uint32		b = 0;

	while (keylen--)
	{
		unsigned char c = *k++;

		a = a * 257 + c;
		b = b * 17 + c;
	}
	return h[a % 2463] + h[b % 2463];
}

/* Hash lookup information for NFC_QC */
static const pg_unicode_norminfo UnicodeNormInfo_NFC_QC = {
	UnicodeNormProps_NFC_QC,
	NFC_QC_hash_func,
	1231
};

static const pg_unicode_normprops UnicodeNormProps_NFKC_QC[] = {
	{0x00A0, UNICODE_NORM_QC_NO},
	{0x00A8, UNICODE_NORM_QC_NO},
	{0x00AA, UNICODE_NORM_QC_NO},
	{0x00AF, UNICODE_NORM_QC_NO},
	{0x00B2, UNICODE_NORM_QC_NO},
	{0x00B3, UNICODE_NORM_QC_NO},
	{0x00B4, UNICODE_NORM_QC_NO},
	{0x00B5, UNICODE_NORM_QC_NO},
	{0x00B8, UNICODE_NORM_QC_NO},
	{0x00B9, UNICODE_NORM_QC_NO},
	{0x00BA, UNICODE_NORM_QC_NO},
	{0x00BC, UNICODE_NORM_QC_NO},
	{0x00BD, UNICODE_NORM_QC_NO},
	{0x00BE, UNICODE_NORM_QC_NO},
	{0x0132, UNICODE_NORM_QC_NO},
	{0x0133, UNICODE_NORM_QC_NO},
	{0x013F, UNICODE_NORM_QC_NO},
	{0x0140, UNICODE_NORM_QC_NO},
	{0x0149, UNICODE_NORM_QC_NO},
	{0x017F, UNICODE_NORM_QC_NO},
	{0x01C4, UNICODE_NORM_QC_NO},
	{0x01C5, UNICODE_NORM_QC_NO},
	{0x01C6, UNICODE_NORM_QC_NO},
	{0x01C7, UNICODE_NORM_QC_NO},
	{0x01C8, UNICODE_NORM_QC_NO},
	{0x01C9, UNICODE_NORM_QC_NO},
	{0x01CA, UNICODE_NORM_QC_NO},
	{0x01CB, UNICODE_NORM_QC_NO},
	{0x01CC, UNICODE_NORM_QC_NO},
	{0x01F1, UNICODE_NORM_QC_NO},
	{0x01F2, UNICODE_NORM_QC_NO},
	{0x01F3, UNICODE_NORM_QC_NO},
	{0x02B0, UNICODE_NORM_QC_NO},
	{0x02B1, UNICODE_NORM_QC_NO},
	{0x02B2, UNICODE_NORM_QC_NO},
	{0x02B3, UNICODE_NORM_QC_NO},
	{0x02B4, UNICODE_NORM_QC_NO},
	{0x02B5, UNICODE_NORM_QC_NO},
	{0x02B6, UNICODE_NORM_QC_NO},
	{0x02B7, UNICODE_NORM_QC_NO},
	{0x02B8, UNICODE_NORM_QC_NO},
	{0x02D8, UNICODE_NORM_QC_NO},
	{0x02D9, UNICODE_NORM_QC_NO},
	{0x02DA, UNICODE_NORM_QC_NO},
	{0x02DB, UNICODE_NORM_QC_NO},
	{0x02DC, UNICODE_NORM_QC_NO},
	{0x02DD, UNICODE_NORM_QC_NO},
	{0x02E0, UNICODE_NORM_QC_NO},
	{0x02E1, UNICODE_NORM_QC_NO},
	{0x02E2, UNICODE_NORM_QC_NO},
	{0x02E3, UNICODE_NORM_QC_NO},
	{0x02E4, UNICODE_NORM_QC_NO},
	{0x0300, UNICODE_NORM_QC_MAYBE},
	{0x0301, UNICODE_NORM_QC_MAYBE},
	{0x0302, UNICODE_NORM_QC_MAYBE},
	{0x0303, UNICODE_NORM_QC_MAYBE},
	{0x0304, UNICODE_NORM_QC_MAYBE},
	{0x0306, UNICODE_NORM_QC_MAYBE},
	{0x0307, UNICODE_NORM_QC_MAYBE},
	{0x0308, UNICODE_NORM_QC_MAYBE},
	{0x0309, UNICODE_NORM_QC_MAYBE},
	{0x030A, UNICODE_NORM_QC_MAYBE},
	{0x030B, UNICODE_NORM_QC_MAYBE},
	{0x030C, UNICODE_NORM_QC_MAYBE},
	{0x030F, UNICODE_NORM_QC_MAYBE},
	{0x0311, UNICODE_NORM_QC_MAYBE},
	{0x0313, UNICODE_NORM_QC_MAYBE},
	{0x0314, UNICODE_NORM_QC_MAYBE},
	{0x031B, UNICODE_NORM_QC_MAYBE},
	{0x0323, UNICODE_NORM_QC_MAYBE},
	{0x0324, UNICODE_NORM_QC_MAYBE},
	{0x0325, UNICODE_NORM_QC_MAYBE},
	{0x0326, UNICODE_NORM_QC_MAYBE},
	{0x0327, UNICODE_NORM_QC_MAYBE},
	{0x0328, UNICODE_NORM_QC_MAYBE},
	{0x032D, UNICODE_NORM_QC_MAYBE},
	{0x032E, UNICODE_NORM_QC_MAYBE},
	{0x0330, UNICODE_NORM_QC_MAYBE},
	{0x0331, UNICODE_NORM_QC_MAYBE},
	{0x0338, UNICODE_NORM_QC_MAYBE},
	{0x0340, UNICODE_NORM_QC_NO},
	{0x0341, UNICODE_NORM_QC_NO},
	{0x0342, UNICODE_NORM_QC_MAYBE},
	{0x0343, UNICODE_NORM_QC_NO},
	{0x0344, UNICODE_NORM_QC_NO},
	{0x0345, UNICODE_NORM_QC_MAYBE},
	{0x0374, UNICODE_NORM_QC_NO},
	{0x037A, UNICODE_NORM_QC_NO},
	{0x037E, UNICODE_NORM_QC_NO},
	{0x0384, UNICODE_NORM_QC_NO},
	{0x0385, UNICODE_NORM_QC_NO},
	{0x0387, UNICODE_NORM_QC_NO},
	{0x03D0, UNICODE_NORM_QC_NO},
	{0x03D1, UNICODE_NORM_QC_NO},
	{0x03D2, UNICODE_NORM_QC_NO},
	{0x03D3, UNICODE_NORM_QC_NO},
	{0x03D4, UNICODE_NORM_QC_NO},
	{0x03D5, UNICODE_NORM_QC_NO},
	{0x03D6, UNICODE_NORM_QC_NO},
	{0x03F0, UNICODE_NORM_QC_NO},
	{0x03F1, UNICODE_NORM_QC_NO},
	{0x03F2, UNICODE_NORM_QC_NO},
	{0x03F4, UNICODE_NORM_QC_NO},
	{0x03F5, UNICODE_NORM_QC_NO},
	{0x03F9, UNICODE_NORM_QC_NO},
	{0x0587, UNICODE_NORM_QC_NO},
	{0x0653, UNICODE_NORM_QC_MAYBE},
	{0x0654, UNICODE_NORM_QC_MAYBE},
	{0x0655, UNICODE_NORM_QC_MAYBE},
	{0x0675, UNICODE_NORM_QC_NO},
	{0x0676, UNICODE_NORM_QC_NO},
	{0x0677, UNICODE_NORM_QC_NO},
	{0x0678, UNICODE_NORM_QC_NO},
	{0x093C, UNICODE_NORM_QC_MAYBE},
	{0x0958, UNICODE_NORM_QC_NO},
	{0x0959, UNICODE_NORM_QC_NO},
	{0x095A, UNICODE_NORM_QC_NO},
	{0x095B, UNICODE_NORM_QC_NO},
	{0x095C, UNICODE_NORM_QC_NO},
	{0x095D, UNICODE_NORM_QC_NO},
	{0x095E, UNICODE_NORM_QC_NO},
	{0x095F, UNICODE_NORM_QC_NO},
	{0x09BE, UNICODE_NORM_QC_MAYBE},
	{0x09D7, UNICODE_NORM_QC_MAYBE},
	{0x09DC, UNICODE_NORM_QC_NO},
	{0x09DD, UNICODE_NORM_QC_NO},
	{0x09DF, UNICODE_NORM_QC_NO},
	{0x0A33, UNICODE_NORM_QC_NO},
	{0x0A36, UNICODE_NORM_QC_NO},
	{0x0A59, UNICODE_NORM_QC_NO},
	{0x0A5A, UNICODE_NORM_QC_NO},
	{0x0A5B, UNICODE_NORM_QC_NO},
	{0x0A5E, UNICODE_NORM_QC_NO},
	{0x0B3E, UNICODE_NORM_QC_MAYBE},
	{0x0B56, UNICODE_NORM_QC_MAYBE},
	{0x0B57, UNICODE_NORM_QC_MAYBE},
	{0x0B5C, UNICODE_NORM_QC_NO},
	{0x0B5D, UNICODE_NORM_QC_NO},
	{0x0BBE, UNICODE_NORM_QC_MAYBE},
	{0x0BD7, UNICODE_NORM_QC_MAYBE},
	{0x0C56, UNICODE_NORM_QC_MAYBE},
	{0x0CC2, UNICODE_NORM_QC_MAYBE},
	{0x0CD5, UNICODE_NORM_QC_MAYBE},
	{0x0CD6, UNICODE_NORM_QC_MAYBE},
	{0x0D3E, UNICODE_NORM_QC_MAYBE},
	{0x0D57, UNICODE_NORM_QC_MAYBE},
	{0x0DCA, UNICODE_NORM_QC_MAYBE},
	{0x0DCF, UNICODE_NORM_QC_MAYBE},
	{0x0DDF, UNICODE_NORM_QC_MAYBE},
	{0x0E33, UNICODE_NORM_QC_NO},
	{0x0EB3, UNICODE_NORM_QC_NO},
	{0x0EDC, UNICODE_NORM_QC_NO},
	{0x0EDD, UNICODE_NORM_QC_NO},
	{0x0F0C, UNICODE_NORM_QC_NO},
	{0x0F43, UNICODE_NORM_QC_NO},
	{0x0F4D, UNICODE_NORM_QC_NO},
	{0x0F52, UNICODE_NORM_QC_NO},
	{0x0F57, UNICODE_NORM_QC_NO},
	{0x0F5C, UNICODE_NORM_QC_NO},
	{0x0F69, UNICODE_NORM_QC_NO},
	{0x0F73, UNICODE_NORM_QC_NO},
	{0x0F75, UNICODE_NORM_QC_NO},
	{0x0F76, UNICODE_NORM_QC_NO},
	{0x0F77, UNICODE_NORM_QC_NO},
	{0x0F78, UNICODE_NORM_QC_NO},
	{0x0F79, UNICODE_NORM_QC_NO},
	{0x0F81, UNICODE_NORM_QC_NO},
	{0x0F93, UNICODE_NORM_QC_NO},
	{0x0F9D, UNICODE_NORM_QC_NO},
	{0x0FA2, UNICODE_NORM_QC_NO},
	{0x0FA7, UNICODE_NORM_QC_NO},
	{0x0FAC, UNICODE_NORM_QC_NO},
	{0x0FB9, UNICODE_NORM_QC_NO},
	{0x102E, UNICODE_NORM_QC_MAYBE},
	{0x10FC, UNICODE_NORM_QC_NO},
	{0x1161, UNICODE_NORM_QC_MAYBE},
	{0x1162, UNICODE_NORM_QC_MAYBE},
	{0x1163, UNICODE_NORM_QC_MAYBE},
	{0x1164, UNICODE_NORM_QC_MAYBE},
	{0x1165, UNICODE_NORM_QC_MAYBE},
	{0x1166, UNICODE_NORM_QC_MAYBE},
	{0x1167, UNICODE_NORM_QC_MAYBE},
	{0x1168, UNICODE_NORM_QC_MAYBE},
	{0x1169, UNICODE_NORM_QC_MAYBE},
	{0x116A, UNICODE_NORM_QC_MAYBE},
	{0x116B, UNICODE_NORM_QC_MAYBE},
	{0x116C, UNICODE_NORM_QC_MAYBE},
	{0x116D, UNICODE_NORM_QC_MAYBE},
	{0x116E, UNICODE_NORM_QC_MAYBE},
	{0x116F, UNICODE_NORM_QC_MAYBE},
	{0x1170, UNICODE_NORM_QC_MAYBE},
	{0x1171, UNICODE_NORM_QC_MAYBE},
	{0x1172, UNICODE_NORM_QC_MAYBE},
	{0x1173, UNICODE_NORM_QC_MAYBE},
	{0x1174, UNICODE_NORM_QC_MAYBE},
	{0x1175, UNICODE_NORM_QC_MAYBE},
	{0x11A8, UNICODE_NORM_QC_MAYBE},
	{0x11A9, UNICODE_NORM_QC_MAYBE},
	{0x11AA, UNICODE_NORM_QC_MAYBE},
	{0x11AB, UNICODE_NORM_QC_MAYBE},
	{0x11AC, UNICODE_NORM_QC_MAYBE},
	{0x11AD, UNICODE_NORM_QC_MAYBE},
	{0x11AE, UNICODE_NORM_QC_MAYBE},
	{0x11AF, UNICODE_NORM_QC_MAYBE},
	{0x11B0, UNICODE_NORM_QC_MAYBE},
	{0x11B1, UNICODE_NORM_QC_MAYBE},
	{0x11B2, UNICODE_NORM_QC_MAYBE},
	{0x11B3, UNICODE_NORM_QC_MAYBE},
	{0x11B4, UNICODE_NORM_QC_MAYBE},
	{0x11B5, UNICODE_NORM_QC_MAYBE},
	{0x11B6, UNICODE_NORM_QC_MAYBE},
	{0x11B7, UNICODE_NORM_QC_MAYBE},
	{0x11B8, UNICODE_NORM_QC_MAYBE},
	{0x11B9, UNICODE_NORM_QC_MAYBE},
	{0x11BA, UNICODE_NORM_QC_MAYBE},
	{0x11BB, UNICODE_NORM_QC_MAYBE},
	{0x11BC, UNICODE_NORM_QC_MAYBE},
	{0x11BD, UNICODE_NORM_QC_MAYBE},
	{0x11BE, UNICODE_NORM_QC_MAYBE},
	{0x11BF, UNICODE_NORM_QC_MAYBE},
	{0x11C0, UNICODE_NORM_QC_MAYBE},
	{0x11C1, UNICODE_NORM_QC_MAYBE},
	{0x11C2, UNICODE_NORM_QC_MAYBE},
	{0x1B35, UNICODE_NORM_QC_MAYBE},
	{0x1D2C, UNICODE_NORM_QC_NO},
	{0x1D2D, UNICODE_NORM_QC_NO},
	{0x1D2E, UNICODE_NORM_QC_NO},
	{0x1D30, UNICODE_NORM_QC_NO},
	{0x1D31, UNICODE_NORM_QC_NO},
	{0x1D32, UNICODE_NORM_QC_NO},
	{0x1D33, UNICODE_NORM_QC_NO},
	{0x1D34, UNICODE_NORM_QC_NO},
	{0x1D35, UNICODE_NORM_QC_NO},
	{0x1D36, UNICODE_NORM_QC_NO},
	{0x1D37, UNICODE_NORM_QC_NO},
	{0x1D38, UNICODE_NORM_QC_NO},
	{0x1D39, UNICODE_NORM_QC_NO},
	{0x1D3A, UNICODE_NORM_QC_NO},
	{0x1D3C, UNICODE_NORM_QC_NO},
	{0x1D3D, UNICODE_NORM_QC_NO},
	{0x1D3E, UNICODE_NORM_QC_NO},
	{0x1D3F, UNICODE_NORM_QC_NO},
	{0x1D40, UNICODE_NORM_QC_NO},
	{0x1D41, UNICODE_NORM_QC_NO},
	{0x1D42, UNICODE_NORM_QC_NO},
	{0x1D43, UNICODE_NORM_QC_NO},
	{0x1D44, UNICODE_NORM_QC_NO},
	{0x1D45, UNICODE_NORM_QC_NO},
	{0x1D46, UNICODE_NORM_QC_NO},
	{0x1D47, UNICODE_NORM_QC_NO},
	{0x1D48, UNICODE_NORM_QC_NO},
	{0x1D49, UNICODE_NORM_QC_NO},
	{0x1D4A, UNICODE_NORM_QC_NO},
	{0x1D4B, UNICODE_NORM_QC_NO},
	{0x1D4C, UNICODE_NORM_QC_NO},
	{0x1D4D, UNICODE_NORM_QC_NO},
	{0x1D4F, UNICODE_NORM_QC_NO},
	{0x1D50, UNICODE_NORM_QC_NO},
	{0x1D51, UNICODE_NORM_QC_NO},
	{0x1D52, UNICODE_NORM_QC_NO},
	{0x1D53, UNICODE_NORM_QC_NO},
	{0x1D54, UNICODE_NORM_QC_NO},
	{0x1D55, UNICODE_NORM_QC_NO},
	{0x1D56, UNICODE_NORM_QC_NO},
	{0x1D57, UNICODE_NORM_QC_NO},
	{0x1D58, UNICODE_NORM_QC_NO},
	{0x1D59, UNICODE_NORM_QC_NO},
	{0x1D5A, UNICODE_NORM_QC_NO},
	{0x1D5B, UNICODE_NORM_QC_NO},
	{0x1D5C, UNICODE_NORM_QC_NO},
	{0x1D5D, UNICODE_NORM_QC_NO},
	{0x1D5E, UNICODE_NORM_QC_NO},
	{0x1D5F, UNICODE_NORM_QC_NO},
	{0x1D60, UNICODE_NORM_QC_NO},
	{0x1D61, UNICODE_NORM_QC_NO},
	{0x1D62, UNICODE_NORM_QC_NO},
	{0x1D63, UNICODE_NORM_QC_NO},
	{0x1D64, UNICODE_NORM_QC_NO},
	{0x1D65, UNICODE_NORM_QC_NO},
	{0x1D66, UNICODE_NORM_QC_NO},
	{0x1D67, UNICODE_NORM_QC_NO},
	{0x1D68, UNICODE_NORM_QC_NO},
	{0x1D69, UNICODE_NORM_QC_NO},
	{0x1D6A, UNICODE_NORM_QC_NO},
	{0x1D78, UNICODE_NORM_QC_NO},
	{0x1D9B, UNICODE_NORM_QC_NO},
	{0x1D9C, UNICODE_NORM_QC_NO},
	{0x1D9D, UNICODE_NORM_QC_NO},
	{0x1D9E, UNICODE_NORM_QC_NO},
	{0x1D9F, UNICODE_NORM_QC_NO},
	{0x1DA0, UNICODE_NORM_QC_NO},
	{0x1DA1, UNICODE_NORM_QC_NO},
	{0x1DA2, UNICODE_NORM_QC_NO},
	{0x1DA3, UNICODE_NORM_QC_NO},
	{0x1DA4, UNICODE_NORM_QC_NO},
	{0x1DA5, UNICODE_NORM_QC_NO},
	{0x1DA6, UNICODE_NORM_QC_NO},
	{0x1DA7, UNICODE_NORM_QC_NO},
	{0x1DA8, UNICODE_NORM_QC_NO},
	{0x1DA9, UNICODE_NORM_QC_NO},
	{0x1DAA, UNICODE_NORM_QC_NO},
	{0x1DAB, UNICODE_NORM_QC_NO},
	{0x1DAC, UNICODE_NORM_QC_NO},
	{0x1DAD, UNICODE_NORM_QC_NO},
	{0x1DAE, UNICODE_NORM_QC_NO},
	{0x1DAF, UNICODE_NORM_QC_NO},
	{0x1DB0, UNICODE_NORM_QC_NO},
	{0x1DB1, UNICODE_NORM_QC_NO},
	{0x1DB2, UNICODE_NORM_QC_NO},
	{0x1DB3, UNICODE_NORM_QC_NO},
	{0x1DB4, UNICODE_NORM_QC_NO},
	{0x1DB5, UNICODE_NORM_QC_NO},
	{0x1DB6, UNICODE_NORM_QC_NO},
	{0x1DB7, UNICODE_NORM_QC_NO},
	{0x1DB8, UNICODE_NORM_QC_NO},
	{0x1DB9, UNICODE_NORM_QC_NO},
	{0x1DBA, UNICODE_NORM_QC_NO},
	{0x1DBB, UNICODE_NORM_QC_NO},
	{0x1DBC, UNICODE_NORM_QC_NO},
	{0x1DBD, UNICODE_NORM_QC_NO},
	{0x1DBE, UNICODE_NORM_QC_NO},
	{0x1DBF, UNICODE_NORM_QC_NO},
	{0x1E9A, UNICODE_NORM_QC_NO},
	{0x1E9B, UNICODE_NORM_QC_NO},
	{0x1F71, UNICODE_NORM_QC_NO},
	{0x1F73, UNICODE_NORM_QC_NO},
	{0x1F75, UNICODE_NORM_QC_NO},
	{0x1F77, UNICODE_NORM_QC_NO},
	{0x1F79, UNICODE_NORM_QC_NO},
	{0x1F7B, UNICODE_NORM_QC_NO},
	{0x1F7D, UNICODE_NORM_QC_NO},
	{0x1FBB, UNICODE_NORM_QC_NO},
	{0x1FBD, UNICODE_NORM_QC_NO},
	{0x1FBE, UNICODE_NORM_QC_NO},
	{0x1FBF, UNICODE_NORM_QC_NO},
	{0x1FC0, UNICODE_NORM_QC_NO},
	{0x1FC1, UNICODE_NORM_QC_NO},
	{0x1FC9, UNICODE_NORM_QC_NO},
	{0x1FCB, UNICODE_NORM_QC_NO},
	{0x1FCD, UNICODE_NORM_QC_NO},
	{0x1FCE, UNICODE_NORM_QC_NO},
	{0x1FCF, UNICODE_NORM_QC_NO},
	{0x1FD3, UNICODE_NORM_QC_NO},
	{0x1FDB, UNICODE_NORM_QC_NO},
	{0x1FDD, UNICODE_NORM_QC_NO},
	{0x1FDE, UNICODE_NORM_QC_NO},
	{0x1FDF, UNICODE_NORM_QC_NO},
	{0x1FE3, UNICODE_NORM_QC_NO},
	{0x1FEB, UNICODE_NORM_QC_NO},
	{0x1FED, UNICODE_NORM_QC_NO},
	{0x1FEE, UNICODE_NORM_QC_NO},
	{0x1FEF, UNICODE_NORM_QC_NO},
	{0x1FF9, UNICODE_NORM_QC_NO},
	{0x1FFB, UNICODE_NORM_QC_NO},
	{0x1FFD, UNICODE_NORM_QC_NO},
	{0x1FFE, UNICODE_NORM_QC_NO},
	{0x2000, UNICODE_NORM_QC_NO},
	{0x2001, UNICODE_NORM_QC_NO},
	{0x2002, UNICODE_NORM_QC_NO},
	{0x2003, UNICODE_NORM_QC_NO},
	{0x2004, UNICODE_NORM_QC_NO},
	{0x2005, UNICODE_NORM_QC_NO},
	{0x2006, UNICODE_NORM_QC_NO},
	{0x2007, UNICODE_NORM_QC_NO},
	{0x2008, UNICODE_NORM_QC_NO},
	{0x2009, UNICODE_NORM_QC_NO},
	{0x200A, UNICODE_NORM_QC_NO},
	{0x2011, UNICODE_NORM_QC_NO},
	{0x2017, UNICODE_NORM_QC_NO},
	{0x2024, UNICODE_NORM_QC_NO},
	{0x2025, UNICODE_NORM_QC_NO},
	{0x2026, UNICODE_NORM_QC_NO},
	{0x202F, UNICODE_NORM_QC_NO},
	{0x2033, UNICODE_NORM_QC_NO},
	{0x2034, UNICODE_NORM_QC_NO},
	{0x2036, UNICODE_NORM_QC_NO},
	{0x2037, UNICODE_NORM_QC_NO},
	{0x203C, UNICODE_NORM_QC_NO},
	{0x203E, UNICODE_NORM_QC_NO},
	{0x2047, UNICODE_NORM_QC_NO},
	{0x2048, UNICODE_NORM_QC_NO},
	{0x2049, UNICODE_NORM_QC_NO},
	{0x2057, UNICODE_NORM_QC_NO},
	{0x205F, UNICODE_NORM_QC_NO},
	{0x2070, UNICODE_NORM_QC_NO},
	{0x2071, UNICODE_NORM_QC_NO},
	{0x2074, UNICODE_NORM_QC_NO},
	{0x2075, UNICODE_NORM_QC_NO},
	{0x2076, UNICODE_NORM_QC_NO},
	{0x2077, UNICODE_NORM_QC_NO},
	{0x2078, UNICODE_NORM_QC_NO},
	{0x2079, UNICODE_NORM_QC_NO},
	{0x207A, UNICODE_NORM_QC_NO},
	{0x207B, UNICODE_NORM_QC_NO},
	{0x207C, UNICODE_NORM_QC_NO},
	{0x207D, UNICODE_NORM_QC_NO},
	{0x207E, UNICODE_NORM_QC_NO},
	{0x207F, UNICODE_NORM_QC_NO},
	{0x2080, UNICODE_NORM_QC_NO},
	{0x2081, UNICODE_NORM_QC_NO},
	{0x2082, UNICODE_NORM_QC_NO},
	{0x2083, UNICODE_NORM_QC_NO},
	{0x2084, UNICODE_NORM_QC_NO},
	{0x2085, UNICODE_NORM_QC_NO},
	{0x2086, UNICODE_NORM_QC_NO},
	{0x2087, UNICODE_NORM_QC_NO},
	{0x2088, UNICODE_NORM_QC_NO},
	{0x2089, UNICODE_NORM_QC_NO},
	{0x208A, UNICODE_NORM_QC_NO},
	{0x208B, UNICODE_NORM_QC_NO},
	{0x208C, UNICODE_NORM_QC_NO},
	{0x208D, UNICODE_NORM_QC_NO},
	{0x208E, UNICODE_NORM_QC_NO},
	{0x2090, UNICODE_NORM_QC_NO},
	{0x2091, UNICODE_NORM_QC_NO},
	{0x2092, UNICODE_NORM_QC_NO},
	{0x2093, UNICODE_NORM_QC_NO},
	{0x2094, UNICODE_NORM_QC_NO},
	{0x2095, UNICODE_NORM_QC_NO},
	{0x2096, UNICODE_NORM_QC_NO},
	{0x2097, UNICODE_NORM_QC_NO},
	{0x2098, UNICODE_NORM_QC_NO},
	{0x2099, UNICODE_NORM_QC_NO},
	{0x209A, UNICODE_NORM_QC_NO},
	{0x209B, UNICODE_NORM_QC_NO},
	{0x209C, UNICODE_NORM_QC_NO},
	{0x20A8, UNICODE_NORM_QC_NO},
	{0x2100, UNICODE_NORM_QC_NO},
	{0x2101, UNICODE_NORM_QC_NO},
	{0x2102, UNICODE_NORM_QC_NO},
	{0x2103, UNICODE_NORM_QC_NO},
	{0x2105, UNICODE_NORM_QC_NO},
	{0x2106, UNICODE_NORM_QC_NO},
	{0x2107, UNICODE_NORM_QC_NO},
	{0x2109, UNICODE_NORM_QC_NO},
	{0x210A, UNICODE_NORM_QC_NO},
	{0x210B, UNICODE_NORM_QC_NO},
	{0x210C, UNICODE_NORM_QC_NO},
	{0x210D, UNICODE_NORM_QC_NO},
	{0x210E, UNICODE_NORM_QC_NO},
	{0x210F, UNICODE_NORM_QC_NO},
	{0x2110, UNICODE_NORM_QC_NO},
	{0x2111, UNICODE_NORM_QC_NO},
	{0x2112, UNICODE_NORM_QC_NO},
	{0x2113, UNICODE_NORM_QC_NO},
	{0x2115, UNICODE_NORM_QC_NO},
	{0x2116, UNICODE_NORM_QC_NO},
	{0x2119, UNICODE_NORM_QC_NO},
	{0x211A, UNICODE_NORM_QC_NO},
	{0x211B, UNICODE_NORM_QC_NO},
	{0x211C, UNICODE_NORM_QC_NO},
	{0x211D, UNICODE_NORM_QC_NO},
	{0x2120, UNICODE_NORM_QC_NO},
	{0x2121, UNICODE_NORM_QC_NO},
	{0x2122, UNICODE_NORM_QC_NO},
	{0x2124, UNICODE_NORM_QC_NO},
	{0x2126, UNICODE_NORM_QC_NO},
	{0x2128, UNICODE_NORM_QC_NO},
	{0x212A, UNICODE_NORM_QC_NO},
	{0x212B, UNICODE_NORM_QC_NO},
	{0x212C, UNICODE_NORM_QC_NO},
	{0x212D, UNICODE_NORM_QC_NO},
	{0x212F, UNICODE_NORM_QC_NO},
	{0x2130, UNICODE_NORM_QC_NO},
	{0x2131, UNICODE_NORM_QC_NO},
	{0x2133, UNICODE_NORM_QC_NO},
	{0x2134, UNICODE_NORM_QC_NO},
	{0x2135, UNICODE_NORM_QC_NO},
	{0x2136, UNICODE_NORM_QC_NO},
	{0x2137, UNICODE_NORM_QC_NO},
	{0x2138, UNICODE_NORM_QC_NO},
	{0x2139, UNICODE_NORM_QC_NO},
	{0x213B, UNICODE_NORM_QC_NO},
	{0x213C, UNICODE_NORM_QC_NO},
	{0x213D, UNICODE_NORM_QC_NO},
	{0x213E, UNICODE_NORM_QC_NO},
	{0x213F, UNICODE_NORM_QC_NO},
	{0x2140, UNICODE_NORM_QC_NO},
	{0x2145, UNICODE_NORM_QC_NO},
	{0x2146, UNICODE_NORM_QC_NO},
	{0x2147, UNICODE_NORM_QC_NO},
	{0x2148, UNICODE_NORM_QC_NO},
	{0x2149, UNICODE_NORM_QC_NO},
	{0x2150, UNICODE_NORM_QC_NO},
	{0x2151, UNICODE_NORM_QC_NO},
	{0x2152, UNICODE_NORM_QC_NO},
	{0x2153, UNICODE_NORM_QC_NO},
	{0x2154, UNICODE_NORM_QC_NO},
	{0x2155, UNICODE_NORM_QC_NO},
	{0x2156, UNICODE_NORM_QC_NO},
	{0x2157, UNICODE_NORM_QC_NO},
	{0x2158, UNICODE_NORM_QC_NO},
	{0x2159, UNICODE_NORM_QC_NO},
	{0x215A, UNICODE_NORM_QC_NO},
	{0x215B, UNICODE_NORM_QC_NO},
	{0x215C, UNICODE_NORM_QC_NO},
	{0x215D, UNICODE_NORM_QC_NO},
	{0x215E, UNICODE_NORM_QC_NO},
	{0x215F, UNICODE_NORM_QC_NO},
	{0x2160, UNICODE_NORM_QC_NO},
	{0x2161, UNICODE_NORM_QC_NO},
	{0x2162, UNICODE_NORM_QC_NO},
	{0x2163, UNICODE_NORM_QC_NO},
	{0x2164, UNICODE_NORM_QC_NO},
	{0x2165, UNICODE_NORM_QC_NO},
	{0x2166, UNICODE_NORM_QC_NO},
	{0x2167, UNICODE_NORM_QC_NO},
	{0x2168, UNICODE_NORM_QC_NO},
	{0x2169, UNICODE_NORM_QC_NO},
	{0x216A, UNICODE_NORM_QC_NO},
	{0x216B, UNICODE_NORM_QC_NO},
	{0x216C, UNICODE_NORM_QC_NO},
	{0x216D, UNICODE_NORM_QC_NO},
	{0x216E, UNICODE_NORM_QC_NO},
	{0x216F, UNICODE_NORM_QC_NO},
	{0x2170, UNICODE_NORM_QC_NO},
	{0x2171, UNICODE_NORM_QC_NO},
	{0x2172, UNICODE_NORM_QC_NO},
	{0x2173, UNICODE_NORM_QC_NO},
	{0x2174, UNICODE_NORM_QC_NO},
	{0x2175, UNICODE_NORM_QC_NO},
	{0x2176, UNICODE_NORM_QC_NO},
	{0x2177, UNICODE_NORM_QC_NO},
	{0x2178, UNICODE_NORM_QC_NO},
	{0x2179, UNICODE_NORM_QC_NO},
	{0x217A, UNICODE_NORM_QC_NO},
	{0x217B, UNICODE_NORM_QC_NO},
	{0x217C, UNICODE_NORM_QC_NO},
	{0x217D, UNICODE_NORM_QC_NO},
	{0x217E, UNICODE_NORM_QC_NO},
	{0x217F, UNICODE_NORM_QC_NO},
	{0x2189, UNICODE_NORM_QC_NO},
	{0x222C, UNICODE_NORM_QC_NO},
	{0x222D, UNICODE_NORM_QC_NO},
	{0x222F, UNICODE_NORM_QC_NO},
	{0x2230, UNICODE_NORM_QC_NO},
	{0x2329, UNICODE_NORM_QC_NO},
	{0x232A, UNICODE_NORM_QC_NO},
	{0x2460, UNICODE_NORM_QC_NO},
	{0x2461, UNICODE_NORM_QC_NO},
	{0x2462, UNICODE_NORM_QC_NO},
	{0x2463, UNICODE_NORM_QC_NO},
	{0x2464, UNICODE_NORM_QC_NO},
	{0x2465, UNICODE_NORM_QC_NO},
	{0x2466, UNICODE_NORM_QC_NO},
	{0x2467, UNICODE_NORM_QC_NO},
	{0x2468, UNICODE_NORM_QC_NO},
	{0x2469, UNICODE_NORM_QC_NO},
	{0x246A, UNICODE_NORM_QC_NO},
	{0x246B, UNICODE_NORM_QC_NO},
	{0x246C, UNICODE_NORM_QC_NO},
	{0x246D, UNICODE_NORM_QC_NO},
	{0x246E, UNICODE_NORM_QC_NO},
	{0x246F, UNICODE_NORM_QC_NO},
	{0x2470, UNICODE_NORM_QC_NO},
	{0x2471, UNICODE_NORM_QC_NO},
	{0x2472, UNICODE_NORM_QC_NO},
	{0x2473, UNICODE_NORM_QC_NO},
	{0x2474, UNICODE_NORM_QC_NO},
	{0x2475, UNICODE_NORM_QC_NO},
	{0x2476, UNICODE_NORM_QC_NO},
	{0x2477, UNICODE_NORM_QC_NO},
	{0x2478, UNICODE_NORM_QC_NO},
	{0x2479, UNICODE_NORM_QC_NO},
	{0x247A, UNICODE_NORM_QC_NO},
	{0x247B, UNICODE_NORM_QC_NO},
	{0x247C, UNICODE_NORM_QC_NO},
	{0x247D, UNICODE_NORM_QC_NO},
	{0x247E, UNICODE_NORM_QC_NO},
	{0x247F, UNICODE_NORM_QC_NO},
	{0x2480, UNICODE_NORM_QC_NO},
	{0x2481, UNICODE_NORM_QC_NO},
	{0x2482, UNICODE_NORM_QC_NO},
	{0x2483, UNICODE_NORM_QC_NO},
	{0x2484, UNICODE_NORM_QC_NO},
	{0x2485, UNICODE_NORM_QC_NO},
	{0x2486, UNICODE_NORM_QC_NO},
	{0x2487, UNICODE_NORM_QC_NO},
	{0x2488, UNICODE_NORM_QC_NO},
	{0x2489, UNICODE_NORM_QC_NO},
	{0x248A, UNICODE_NORM_QC_NO},
	{0x248B, UNICODE_NORM_QC_NO},
	{0x248C, UNICODE_NORM_QC_NO},
	{0x248D, UNICODE_NORM_QC_NO},
	{0x248E, UNICODE_NORM_QC_NO},
	{0x248F, UNICODE_NORM_QC_NO},
	{0x2490, UNICODE_NORM_QC_NO},
	{0x2491, UNICODE_NORM_QC_NO},
	{0x2492, UNICODE_NORM_QC_NO},
	{0x2493, UNICODE_NORM_QC_NO},
	{0x2494, UNICODE_NORM_QC_NO},
	{0x2495, UNICODE_NORM_QC_NO},
	{0x2496, UNICODE_NORM_QC_NO},
	{0x2497, UNICODE_NORM_QC_NO},
	{0x2498, UNICODE_NORM_QC_NO},
	{0x2499, UNICODE_NORM_QC_NO},
	{0x249A, UNICODE_NORM_QC_NO},
	{0x249B, UNICODE_NORM_QC_NO},
	{0x249C, UNICODE_NORM_QC_NO},
	{0x249D, UNICODE_NORM_QC_NO},
	{0x249E, UNICODE_NORM_QC_NO},
	{0x249F, UNICODE_NORM_QC_NO},
	{0x24A0, UNICODE_NORM_QC_NO},
	{0x24A1, UNICODE_NORM_QC_NO},
	{0x24A2, UNICODE_NORM_QC_NO},
	{0x24A3, UNICODE_NORM_QC_NO},
	{0x24A4, UNICODE_NORM_QC_NO},
	{0x24A5, UNICODE_NORM_QC_NO},
	{0x24A6, UNICODE_NORM_QC_NO},
	{0x24A7, UNICODE_NORM_QC_NO},
	{0x24A8, UNICODE_NORM_QC_NO},
	{0x24A9, UNICODE_NORM_QC_NO},
	{0x24AA, UNICODE_NORM_QC_NO},
	{0x24AB, UNICODE_NORM_QC_NO},
	{0x24AC, UNICODE_NORM_QC_NO},
	{0x24AD, UNICODE_NORM_QC_NO},
	{0x24AE, UNICODE_NORM_QC_NO},
	{0x24AF, UNICODE_NORM_QC_NO},
	{0x24B0, UNICODE_NORM_QC_NO},
	{0x24B1, UNICODE_NORM_QC_NO},
	{0x24B2, UNICODE_NORM_QC_NO},
	{0x24B3, UNICODE_NORM_QC_NO},
	{0x24B4, UNICODE_NORM_QC_NO},
	{0x24B5, UNICODE_NORM_QC_NO},
	{0x24B6, UNICODE_NORM_QC_NO},
	{0x24B7, UNICODE_NORM_QC_NO},
	{0x24B8, UNICODE_NORM_QC_NO},
	{0x24B9, UNICODE_NORM_QC_NO},
	{0x24BA, UNICODE_NORM_QC_NO},
	{0x24BB, UNICODE_NORM_QC_NO},
	{0x24BC, UNICODE_NORM_QC_NO},
	{0x24BD, UNICODE_NORM_QC_NO},
	{0x24BE, UNICODE_NORM_QC_NO},
	{0x24BF, UNICODE_NORM_QC_NO},
	{0x24C0, UNICODE_NORM_QC_NO},
	{0x24C1, UNICODE_NORM_QC_NO},
	{0x24C2, UNICODE_NORM_QC_NO},
	{0x24C3, UNICODE_NORM_QC_NO},
	{0x24C4, UNICODE_NORM_QC_NO},
	{0x24C5, UNICODE_NORM_QC_NO},
	{0x24C6, UNICODE_NORM_QC_NO},
	{0x24C7, UNICODE_NORM_QC_NO},
	{0x24C8, UNICODE_NORM_QC_NO},
	{0x24C9, UNICODE_NORM_QC_NO},
	{0x24CA, UNICODE_NORM_QC_NO},
	{0x24CB, UNICODE_NORM_QC_NO},
	{0x24CC, UNICODE_NORM_QC_NO},
	{0x24CD, UNICODE_NORM_QC_NO},
	{0x24CE, UNICODE_NORM_QC_NO},
	{0x24CF, UNICODE_NORM_QC_NO},
	{0x24D0, UNICODE_NORM_QC_NO},
	{0x24D1, UNICODE_NORM_QC_NO},
	{0x24D2, UNICODE_NORM_QC_NO},
	{0x24D3, UNICODE_NORM_QC_NO},
	{0x24D4, UNICODE_NORM_QC_NO},
	{0x24D5, UNICODE_NORM_QC_NO},
	{0x24D6, UNICODE_NORM_QC_NO},
	{0x24D7, UNICODE_NORM_QC_NO},
	{0x24D8, UNICODE_NORM_QC_NO},
	{0x24D9, UNICODE_NORM_QC_NO},
	{0x24DA, UNICODE_NORM_QC_NO},
	{0x24DB, UNICODE_NORM_QC_NO},
	{0x24DC, UNICODE_NORM_QC_NO},
	{0x24DD, UNICODE_NORM_QC_NO},
	{0x24DE, UNICODE_NORM_QC_NO},
	{0x24DF, UNICODE_NORM_QC_NO},
	{0x24E0, UNICODE_NORM_QC_NO},
	{0x24E1, UNICODE_NORM_QC_NO},
	{0x24E2, UNICODE_NORM_QC_NO},
	{0x24E3, UNICODE_NORM_QC_NO},
	{0x24E4, UNICODE_NORM_QC_NO},
	{0x24E5, UNICODE_NORM_QC_NO},
	{0x24E6, UNICODE_NORM_QC_NO},
	{0x24E7, UNICODE_NORM_QC_NO},
	{0x24E8, UNICODE_NORM_QC_NO},
	{0x24E9, UNICODE_NORM_QC_NO},
	{0x24EA, UNICODE_NORM_QC_NO},
	{0x2A0C, UNICODE_NORM_QC_NO},
	{0x2A74, UNICODE_NORM_QC_NO},
	{0x2A75, UNICODE_NORM_QC_NO},
	{0x2A76, UNICODE_NORM_QC_NO},
	{0x2ADC, UNICODE_NORM_QC_NO},
	{0x2C7C, UNICODE_NORM_QC_NO},
	{0x2C7D, UNICODE_NORM_QC_NO},
	{0x2D6F, UNICODE_NORM_QC_NO},
	{0x2E9F, UNICODE_NORM_QC_NO},
	{0x2EF3, UNICODE_NORM_QC_NO},
	{0x2F00, UNICODE_NORM_QC_NO},
	{0x2F01, UNICODE_NORM_QC_NO},
	{0x2F02, UNICODE_NORM_QC_NO},
	{0x2F03, UNICODE_NORM_QC_NO},
	{0x2F04, UNICODE_NORM_QC_NO},
	{0x2F05, UNICODE_NORM_QC_NO},
	{0x2F06, UNICODE_NORM_QC_NO},
	{0x2F07, UNICODE_NORM_QC_NO},
	{0x2F08, UNICODE_NORM_QC_NO},
	{0x2F09, UNICODE_NORM_QC_NO},
	{0x2F0A, UNICODE_NORM_QC_NO},
	{0x2F0B, UNICODE_NORM_QC_NO},
	{0x2F0C, UNICODE_NORM_QC_NO},
	{0x2F0D, UNICODE_NORM_QC_NO},
	{0x2F0E, UNICODE_NORM_QC_NO},
	{0x2F0F, UNICODE_NORM_QC_NO},
	{0x2F10, UNICODE_NORM_QC_NO},
	{0x2F11, UNICODE_NORM_QC_NO},
	{0x2F12, UNICODE_NORM_QC_NO},
	{0x2F13, UNICODE_NORM_QC_NO},
	{0x2F14, UNICODE_NORM_QC_NO},
	{0x2F15, UNICODE_NORM_QC_NO},
	{0x2F16, UNICODE_NORM_QC_NO},
	{0x2F17, UNICODE_NORM_QC_NO},
	{0x2F18, UNICODE_NORM_QC_NO},
	{0x2F19, UNICODE_NORM_QC_NO},
	{0x2F1A, UNICODE_NORM_QC_NO},
	{0x2F1B, UNICODE_NORM_QC_NO},
	{0x2F1C, UNICODE_NORM_QC_NO},
	{0x2F1D, UNICODE_NORM_QC_NO},
	{0x2F1E, UNICODE_NORM_QC_NO},
	{0x2F1F, UNICODE_NORM_QC_NO},
	{0x2F20, UNICODE_NORM_QC_NO},
	{0x2F21, UNICODE_NORM_QC_NO},
	{0x2F22, UNICODE_NORM_QC_NO},
	{0x2F23, UNICODE_NORM_QC_NO},
	{0x2F24, UNICODE_NORM_QC_NO},
	{0x2F25, UNICODE_NORM_QC_NO},
	{0x2F26, UNICODE_NORM_QC_NO},
	{0x2F27, UNICODE_NORM_QC_NO},
	{0x2F28, UNICODE_NORM_QC_NO},
	{0x2F29, UNICODE_NORM_QC_NO},
	{0x2F2A, UNICODE_NORM_QC_NO},
	{0x2F2B, UNICODE_NORM_QC_NO},
	{0x2F2C, UNICODE_NORM_QC_NO},
	{0x2F2D, UNICODE_NORM_QC_NO},
	{0x2F2E, UNICODE_NORM_QC_NO},
	{0x2F2F, UNICODE_NORM_QC_NO},
	{0x2F30, UNICODE_NORM_QC_NO},
	{0x2F31, UNICODE_NORM_QC_NO},
	{0x2F32, UNICODE_NORM_QC_NO},
	{0x2F33, UNICODE_NORM_QC_NO},
	{0x2F34, UNICODE_NORM_QC_NO},
	{0x2F35, UNICODE_NORM_QC_NO},
	{0x2F36, UNICODE_NORM_QC_NO},
	{0x2F37, UNICODE_NORM_QC_NO},
	{0x2F38, UNICODE_NORM_QC_NO},
	{0x2F39, UNICODE_NORM_QC_NO},
	{0x2F3A, UNICODE_NORM_QC_NO},
	{0x2F3B, UNICODE_NORM_QC_NO},
	{0x2F3C, UNICODE_NORM_QC_NO},
	{0x2F3D, UNICODE_NORM_QC_NO},
	{0x2F3E, UNICODE_NORM_QC_NO},
	{0x2F3F, UNICODE_NORM_QC_NO},
	{0x2F40, UNICODE_NORM_QC_NO},
	{0x2F41, UNICODE_NORM_QC_NO},
	{0x2F42, UNICODE_NORM_QC_NO},
	{0x2F43, UNICODE_NORM_QC_NO},
	{0x2F44, UNICODE_NORM_QC_NO},
	{0x2F45, UNICODE_NORM_QC_NO},
	{0x2F46, UNICODE_NORM_QC_NO},
	{0x2F47, UNICODE_NORM_QC_NO},
	{0x2F48, UNICODE_NORM_QC_NO},
	{0x2F49, UNICODE_NORM_QC_NO},
	{0x2F4A, UNICODE_NORM_QC_NO},
	{0x2F4B, UNICODE_NORM_QC_NO},
	{0x2F4C, UNICODE_NORM_QC_NO},
	{0x2F4D, UNICODE_NORM_QC_NO},
	{0x2F4E, UNICODE_NORM_QC_NO},
	{0x2F4F, UNICODE_NORM_QC_NO},
	{0x2F50, UNICODE_NORM_QC_NO},
	{0x2F51, UNICODE_NORM_QC_NO},
	{0x2F52, UNICODE_NORM_QC_NO},
	{0x2F53, UNICODE_NORM_QC_NO},
	{0x2F54, UNICODE_NORM_QC_NO},
	{0x2F55, UNICODE_NORM_QC_NO},
	{0x2F56, UNICODE_NORM_QC_NO},
	{0x2F57, UNICODE_NORM_QC_NO},
	{0x2F58, UNICODE_NORM_QC_NO},
	{0x2F59, UNICODE_NORM_QC_NO},
	{0x2F5A, UNICODE_NORM_QC_NO},
	{0x2F5B, UNICODE_NORM_QC_NO},
	{0x2F5C, UNICODE_NORM_QC_NO},
	{0x2F5D, UNICODE_NORM_QC_NO},
	{0x2F5E, UNICODE_NORM_QC_NO},
	{0x2F5F, UNICODE_NORM_QC_NO},
	{0x2F60, UNICODE_NORM_QC_NO},
	{0x2F61, UNICODE_NORM_QC_NO},
	{0x2F62, UNICODE_NORM_QC_NO},
	{0x2F63, UNICODE_NORM_QC_NO},
	{0x2F64, UNICODE_NORM_QC_NO},
	{0x2F65, UNICODE_NORM_QC_NO},
	{0x2F66, UNICODE_NORM_QC_NO},
	{0x2F67, UNICODE_NORM_QC_NO},
	{0x2F68, UNICODE_NORM_QC_NO},
	{0x2F69, UNICODE_NORM_QC_NO},
	{0x2F6A, UNICODE_NORM_QC_NO},
	{0x2F6B, UNICODE_NORM_QC_NO},
	{0x2F6C, UNICODE_NORM_QC_NO},
	{0x2F6D, UNICODE_NORM_QC_NO},
	{0x2F6E, UNICODE_NORM_QC_NO},
	{0x2F6F, UNICODE_NORM_QC_NO},
	{0x2F70, UNICODE_NORM_QC_NO},
	{0x2F71, UNICODE_NORM_QC_NO},
	{0x2F72, UNICODE_NORM_QC_NO},
	{0x2F73, UNICODE_NORM_QC_NO},
	{0x2F74, UNICODE_NORM_QC_NO},
	{0x2F75, UNICODE_NORM_QC_NO},
	{0x2F76, UNICODE_NORM_QC_NO},
	{0x2F77, UNICODE_NORM_QC_NO},
	{0x2F78, UNICODE_NORM_QC_NO},
	{0x2F79, UNICODE_NORM_QC_NO},
	{0x2F7A, UNICODE_NORM_QC_NO},
	{0x2F7B, UNICODE_NORM_QC_NO},
	{0x2F7C, UNICODE_NORM_QC_NO},
	{0x2F7D, UNICODE_NORM_QC_NO},
	{0x2F7E, UNICODE_NORM_QC_NO},
	{0x2F7F, UNICODE_NORM_QC_NO},
	{0x2F80, UNICODE_NORM_QC_NO},
	{0x2F81, UNICODE_NORM_QC_NO},
	{0x2F82, UNICODE_NORM_QC_NO},
	{0x2F83, UNICODE_NORM_QC_NO},
	{0x2F84, UNICODE_NORM_QC_NO},
	{0x2F85, UNICODE_NORM_QC_NO},
	{0x2F86, UNICODE_NORM_QC_NO},
	{0x2F87, UNICODE_NORM_QC_NO},
	{0x2F88, UNICODE_NORM_QC_NO},
	{0x2F89, UNICODE_NORM_QC_NO},
	{0x2F8A, UNICODE_NORM_QC_NO},
	{0x2F8B, UNICODE_NORM_QC_NO},
	{0x2F8C, UNICODE_NORM_QC_NO},
	{0x2F8D, UNICODE_NORM_QC_NO},
	{0x2F8E, UNICODE_NORM_QC_NO},
	{0x2F8F, UNICODE_NORM_QC_NO},
	{0x2F90, UNICODE_NORM_QC_NO},
	{0x2F91, UNICODE_NORM_QC_NO},
	{0x2F92, UNICODE_NORM_QC_NO},
	{0x2F93, UNICODE_NORM_QC_NO},
	{0x2F94, UNICODE_NORM_QC_NO},
	{0x2F95, UNICODE_NORM_QC_NO},
	{0x2F96, UNICODE_NORM_QC_NO},
	{0x2F97, UNICODE_NORM_QC_NO},
	{0x2F98, UNICODE_NORM_QC_NO},
	{0x2F99, UNICODE_NORM_QC_NO},
	{0x2F9A, UNICODE_NORM_QC_NO},
	{0x2F9B, UNICODE_NORM_QC_NO},
	{0x2F9C, UNICODE_NORM_QC_NO},
	{0x2F9D, UNICODE_NORM_QC_NO},
	{0x2F9E, UNICODE_NORM_QC_NO},
	{0x2F9F, UNICODE_NORM_QC_NO},
	{0x2FA0, UNICODE_NORM_QC_NO},
	{0x2FA1, UNICODE_NORM_QC_NO},
	{0x2FA2, UNICODE_NORM_QC_NO},
	{0x2FA3, UNICODE_NORM_QC_NO},
	{0x2FA4, UNICODE_NORM_QC_NO},
	{0x2FA5, UNICODE_NORM_QC_NO},
	{0x2FA6, UNICODE_NORM_QC_NO},
	{0x2FA7, UNICODE_NORM_QC_NO},
	{0x2FA8, UNICODE_NORM_QC_NO},
	{0x2FA9, UNICODE_NORM_QC_NO},
	{0x2FAA, UNICODE_NORM_QC_NO},
	{0x2FAB, UNICODE_NORM_QC_NO},
	{0x2FAC, UNICODE_NORM_QC_NO},
	{0x2FAD, UNICODE_NORM_QC_NO},
	{0x2FAE, UNICODE_NORM_QC_NO},
	{0x2FAF, UNICODE_NORM_QC_NO},
	{0x2FB0, UNICODE_NORM_QC_NO},
	{0x2FB1, UNICODE_NORM_QC_NO},
	{0x2FB2, UNICODE_NORM_QC_NO},
	{0x2FB3, UNICODE_NORM_QC_NO},
	{0x2FB4, UNICODE_NORM_QC_NO},
	{0x2FB5, UNICODE_NORM_QC_NO},
	{0x2FB6, UNICODE_NORM_QC_NO},
	{0x2FB7, UNICODE_NORM_QC_NO},
	{0x2FB8, UNICODE_NORM_QC_NO},
	{0x2FB9, UNICODE_NORM_QC_NO},
	{0x2FBA, UNICODE_NORM_QC_NO},
	{0x2FBB, UNICODE_NORM_QC_NO},
	{0x2FBC, UNICODE_NORM_QC_NO},
	{0x2FBD, UNICODE_NORM_QC_NO},
	{0x2FBE, UNICODE_NORM_QC_NO},
	{0x2FBF, UNICODE_NORM_QC_NO},
	{0x2FC0, UNICODE_NORM_QC_NO},
	{0x2FC1, UNICODE_NORM_QC_NO},
	{0x2FC2, UNICODE_NORM_QC_NO},
	{0x2FC3, UNICODE_NORM_QC_NO},
	{0x2FC4, UNICODE_NORM_QC_NO},
	{0x2FC5, UNICODE_NORM_QC_NO},
	{0x2FC6, UNICODE_NORM_QC_NO},
	{0x2FC7, UNICODE_NORM_QC_NO},
	{0x2FC8, UNICODE_NORM_QC_NO},
	{0x2FC9, UNICODE_NORM_QC_NO},
	{0x2FCA, UNICODE_NORM_QC_NO},
	{0x2FCB, UNICODE_NORM_QC_NO},
	{0x2FCC, UNICODE_NORM_QC_NO},
	{0x2FCD, UNICODE_NORM_QC_NO},
	{0x2FCE, UNICODE_NORM_QC_NO},
	{0x2FCF, UNICODE_NORM_QC_NO},
	{0x2FD0, UNICODE_NORM_QC_NO},
	{0x2FD1, UNICODE_NORM_QC_NO},
	{0x2FD2, UNICODE_NORM_QC_NO},
	{0x2FD3, UNICODE_NORM_QC_NO},
	{0x2FD4, UNICODE_NORM_QC_NO},
	{0x2FD5, UNICODE_NORM_QC_NO},
	{0x3000, UNICODE_NORM_QC_NO},
	{0x3036, UNICODE_NORM_QC_NO},
	{0x3038, UNICODE_NORM_QC_NO},
	{0x3039, UNICODE_NORM_QC_NO},
	{0x303A, UNICODE_NORM_QC_NO},
	{0x3099, UNICODE_NORM_QC_MAYBE},
	{0x309A, UNICODE_NORM_QC_MAYBE},
	{0x309B, UNICODE_NORM_QC_NO},
	{0x309C, UNICODE_NORM_QC_NO},
	{0x309F, UNICODE_NORM_QC_NO},
	{0x30FF, UNICODE_NORM_QC_NO},
	{0x3131, UNICODE_NORM_QC_NO},
	{0x3132, UNICODE_NORM_QC_NO},
	{0x3133, UNICODE_NORM_QC_NO},
	{0x3134, UNICODE_NORM_QC_NO},
	{0x3135, UNICODE_NORM_QC_NO},
	{0x3136, UNICODE_NORM_QC_NO},
	{0x3137, UNICODE_NORM_QC_NO},
	{0x3138, UNICODE_NORM_QC_NO},
	{0x3139, UNICODE_NORM_QC_NO},
	{0x313A, UNICODE_NORM_QC_NO},
	{0x313B, UNICODE_NORM_QC_NO},
	{0x313C, UNICODE_NORM_QC_NO},
	{0x313D, UNICODE_NORM_QC_NO},
	{0x313E, UNICODE_NORM_QC_NO},
	{0x313F, UNICODE_NORM_QC_NO},
	{0x3140, UNICODE_NORM_QC_NO},
	{0x3141, UNICODE_NORM_QC_NO},
	{0x3142, UNICODE_NORM_QC_NO},
	{0x3143, UNICODE_NORM_QC_NO},
	{0x3144, UNICODE_NORM_QC_NO},
	{0x3145, UNICODE_NORM_QC_NO},
	{0x3146, UNICODE_NORM_QC_NO},
	{0x3147, UNICODE_NORM_QC_NO},
	{0x3148, UNICODE_NORM_QC_NO},
	{0x3149, UNICODE_NORM_QC_NO},
	{0x314A, UNICODE_NORM_QC_NO},
	{0x314B, UNICODE_NORM_QC_NO},
	{0x314C, UNICODE_NORM_QC_NO},
	{0x314D, UNICODE_NORM_QC_NO},
	{0x314E, UNICODE_NORM_QC_NO},
	{0x314F, UNICODE_NORM_QC_NO},
	{0x3150, UNICODE_NORM_QC_NO},
	{0x3151, UNICODE_NORM_QC_NO},
	{0x3152, UNICODE_NORM_QC_NO},
	{0x3153, UNICODE_NORM_QC_NO},
	{0x3154, UNICODE_NORM_QC_NO},
	{0x3155, UNICODE_NORM_QC_NO},
	{0x3156, UNICODE_NORM_QC_NO},
	{0x3157, UNICODE_NORM_QC_NO},
	{0x3158, UNICODE_NORM_QC_NO},
	{0x3159, UNICODE_NORM_QC_NO},
	{0x315A, UNICODE_NORM_QC_NO},
	{0x315B, UNICODE_NORM_QC_NO},
	{0x315C, UNICODE_NORM_QC_NO},
	{0x315D, UNICODE_NORM_QC_NO},
	{0x315E, UNICODE_NORM_QC_NO},
	{0x315F, UNICODE_NORM_QC_NO},
	{0x3160, UNICODE_NORM_QC_NO},
	{0x3161, UNICODE_NORM_QC_NO},
	{0x3162, UNICODE_NORM_QC_NO},
	{0x3163, UNICODE_NORM_QC_NO},
	{0x3164, UNICODE_NORM_QC_NO},
	{0x3165, UNICODE_NORM_QC_NO},
	{0x3166, UNICODE_NORM_QC_NO},
	{0x3167, UNICODE_NORM_QC_NO},
	{0x3168, UNICODE_NORM_QC_NO},
	{0x3169, UNICODE_NORM_QC_NO},
	{0x316A, UNICODE_NORM_QC_NO},
	{0x316B, UNICODE_NORM_QC_NO},
	{0x316C, UNICODE_NORM_QC_NO},
	{0x316D, UNICODE_NORM_QC_NO},
	{0x316E, UNICODE_NORM_QC_NO},
	{0x316F, UNICODE_NORM_QC_NO},
	{0x3170, UNICODE_NORM_QC_NO},
	{0x3171, UNICODE_NORM_QC_NO},
	{0x3172, UNICODE_NORM_QC_NO},
	{0x3173, UNICODE_NORM_QC_NO},
	{0x3174, UNICODE_NORM_QC_NO},
	{0x3175, UNICODE_NORM_QC_NO},
	{0x3176, UNICODE_NORM_QC_NO},
	{0x3177, UNICODE_NORM_QC_NO},
	{0x3178, UNICODE_NORM_QC_NO},
	{0x3179, UNICODE_NORM_QC_NO},
	{0x317A, UNICODE_NORM_QC_NO},
	{0x317B, UNICODE_NORM_QC_NO},
	{0x317C, UNICODE_NORM_QC_NO},
	{0x317D, UNICODE_NORM_QC_NO},
	{0x317E, UNICODE_NORM_QC_NO},
	{0x317F, UNICODE_NORM_QC_NO},
	{0x3180, UNICODE_NORM_QC_NO},
	{0x3181, UNICODE_NORM_QC_NO},
	{0x3182, UNICODE_NORM_QC_NO},
	{0x3183, UNICODE_NORM_QC_NO},
	{0x3184, UNICODE_NORM_QC_NO},
	{0x3185, UNICODE_NORM_QC_NO},
	{0x3186, UNICODE_NORM_QC_NO},
	{0x3187, UNICODE_NORM_QC_NO},
	{0x3188, UNICODE_NORM_QC_NO},
	{0x3189, UNICODE_NORM_QC_NO},
	{0x318A, UNICODE_NORM_QC_NO},
	{0x318B, UNICODE_NORM_QC_NO},
	{0x318C, UNICODE_NORM_QC_NO},
	{0x318D, UNICODE_NORM_QC_NO},
	{0x318E, UNICODE_NORM_QC_NO},
	{0x3192, UNICODE_NORM_QC_NO},
	{0x3193, UNICODE_NORM_QC_NO},
	{0x3194, UNICODE_NORM_QC_NO},
	{0x3195, UNICODE_NORM_QC_NO},
	{0x3196, UNICODE_NORM_QC_NO},
	{0x3197, UNICODE_NORM_QC_NO},
	{0x3198, UNICODE_NORM_QC_NO},
	{0x3199, UNICODE_NORM_QC_NO},
	{0x319A, UNICODE_NORM_QC_NO},
	{0x319B, UNICODE_NORM_QC_NO},
	{0x319C, UNICODE_NORM_QC_NO},
	{0x319D, UNICODE_NORM_QC_NO},
	{0x319E, UNICODE_NORM_QC_NO},
	{0x319F, UNICODE_NORM_QC_NO},
	{0x3200, UNICODE_NORM_QC_NO},
	{0x3201, UNICODE_NORM_QC_NO},
	{0x3202, UNICODE_NORM_QC_NO},
	{0x3203, UNICODE_NORM_QC_NO},
	{0x3204, UNICODE_NORM_QC_NO},
	{0x3205, UNICODE_NORM_QC_NO},
	{0x3206, UNICODE_NORM_QC_NO},
	{0x3207, UNICODE_NORM_QC_NO},
	{0x3208, UNICODE_NORM_QC_NO},
	{0x3209, UNICODE_NORM_QC_NO},
	{0x320A, UNICODE_NORM_QC_NO},
	{0x320B, UNICODE_NORM_QC_NO},
	{0x320C, UNICODE_NORM_QC_NO},
	{0x320D, UNICODE_NORM_QC_NO},
	{0x320E, UNICODE_NORM_QC_NO},
	{0x320F, UNICODE_NORM_QC_NO},
	{0x3210, UNICODE_NORM_QC_NO},
	{0x3211, UNICODE_NORM_QC_NO},
	{0x3212, UNICODE_NORM_QC_NO},
	{0x3213, UNICODE_NORM_QC_NO},
	{0x3214, UNICODE_NORM_QC_NO},
	{0x3215, UNICODE_NORM_QC_NO},
	{0x3216, UNICODE_NORM_QC_NO},
	{0x3217, UNICODE_NORM_QC_NO},
	{0x3218, UNICODE_NORM_QC_NO},
	{0x3219, UNICODE_NORM_QC_NO},
	{0x321A, UNICODE_NORM_QC_NO},
	{0x321B, UNICODE_NORM_QC_NO},
	{0x321C, UNICODE_NORM_QC_NO},
	{0x321D, UNICODE_NORM_QC_NO},
	{0x321E, UNICODE_NORM_QC_NO},
	{0x3220, UNICODE_NORM_QC_NO},
	{0x3221, UNICODE_NORM_QC_NO},
	{0x3222, UNICODE_NORM_QC_NO},
	{0x3223, UNICODE_NORM_QC_NO},
	{0x3224, UNICODE_NORM_QC_NO},
	{0x3225, UNICODE_NORM_QC_NO},
	{0x3226, UNICODE_NORM_QC_NO},
	{0x3227, UNICODE_NORM_QC_NO},
	{0x3228, UNICODE_NORM_QC_NO},
	{0x3229, UNICODE_NORM_QC_NO},
	{0x322A, UNICODE_NORM_QC_NO},
	{0x322B, UNICODE_NORM_QC_NO},
	{0x322C, UNICODE_NORM_QC_NO},
	{0x322D, UNICODE_NORM_QC_NO},
	{0x322E, UNICODE_NORM_QC_NO},
	{0x322F, UNICODE_NORM_QC_NO},
	{0x3230, UNICODE_NORM_QC_NO},
	{0x3231, UNICODE_NORM_QC_NO},
	{0x3232, UNICODE_NORM_QC_NO},
	{0x3233, UNICODE_NORM_QC_NO},
	{0x3234, UNICODE_NORM_QC_NO},
	{0x3235, UNICODE_NORM_QC_NO},
	{0x3236, UNICODE_NORM_QC_NO},
	{0x3237, UNICODE_NORM_QC_NO},
	{0x3238, UNICODE_NORM_QC_NO},
	{0x3239, UNICODE_NORM_QC_NO},
	{0x323A, UNICODE_NORM_QC_NO},
	{0x323B, UNICODE_NORM_QC_NO},
	{0x323C, UNICODE_NORM_QC_NO},
	{0x323D, UNICODE_NORM_QC_NO},
	{0x323E, UNICODE_NORM_QC_NO},
	{0x323F, UNICODE_NORM_QC_NO},
	{0x3240, UNICODE_NORM_QC_NO},
	{0x3241, UNICODE_NORM_QC_NO},
	{0x3242, UNICODE_NORM_QC_NO},
	{0x3243, UNICODE_NORM_QC_NO},
	{0x3244, UNICODE_NORM_QC_NO},
	{0x3245, UNICODE_NORM_QC_NO},
	{0x3246, UNICODE_NORM_QC_NO},
	{0x3247, UNICODE_NORM_QC_NO},
	{0x3250, UNICODE_NORM_QC_NO},
	{0x3251, UNICODE_NORM_QC_NO},
	{0x3252, UNICODE_NORM_QC_NO},
	{0x3253, UNICODE_NORM_QC_NO},
	{0x3254, UNICODE_NORM_QC_NO},
	{0x3255, UNICODE_NORM_QC_NO},
	{0x3256, UNICODE_NORM_QC_NO},
	{0x3257, UNICODE_NORM_QC_NO},
	{0x3258, UNICODE_NORM_QC_NO},
	{0x3259, UNICODE_NORM_QC_NO},
	{0x325A, UNICODE_NORM_QC_NO},
	{0x325B, UNICODE_NORM_QC_NO},
	{0x325C, UNICODE_NORM_QC_NO},
	{0x325D, UNICODE_NORM_QC_NO},
	{0x325E, UNICODE_NORM_QC_NO},
	{0x325F, UNICODE_NORM_QC_NO},
	{0x3260, UNICODE_NORM_QC_NO},
	{0x3261, UNICODE_NORM_QC_NO},
	{0x3262, UNICODE_NORM_QC_NO},
	{0x3263, UNICODE_NORM_QC_NO},
	{0x3264, UNICODE_NORM_QC_NO},
	{0x3265, UNICODE_NORM_QC_NO},
	{0x3266, UNICODE_NORM_QC_NO},
	{0x3267, UNICODE_NORM_QC_NO},
	{0x3268, UNICODE_NORM_QC_NO},
	{0x3269, UNICODE_NORM_QC_NO},
	{0x326A, UNICODE_NORM_QC_NO},
	{0x326B, UNICODE_NORM_QC_NO},
	{0x326C, UNICODE_NORM_QC_NO},
	{0x326D, UNICODE_NORM_QC_NO},
	{0x326E, UNICODE_NORM_QC_NO},
	{0x326F, UNICODE_NORM_QC_NO},
	{0x3270, UNICODE_NORM_QC_NO},
	{0x3271, UNICODE_NORM_QC_NO},
	{0x3272, UNICODE_NORM_QC_NO},
	{0x3273, UNICODE_NORM_QC_NO},
	{0x3274, UNICODE_NORM_QC_NO},
	{0x3275, UNICODE_NORM_QC_NO},
	{0x3276, UNICODE_NORM_QC_NO},
	{0x3277, UNICODE_NORM_QC_NO},
	{0x3278, UNICODE_NORM_QC_NO},
	{0x3279, UNICODE_NORM_QC_NO},
	{0x327A, UNICODE_NORM_QC_NO},
	{0x327B, UNICODE_NORM_QC_NO},
	{0x327C, UNICODE_NORM_QC_NO},
	{0x327D, UNICODE_NORM_QC_NO},
	{0x327E, UNICODE_NORM_QC_NO},
	{0x3280, UNICODE_NORM_QC_NO},
	{0x3281, UNICODE_NORM_QC_NO},
	{0x3282, UNICODE_NORM_QC_NO},
	{0x3283, UNICODE_NORM_QC_NO},
	{0x3284, UNICODE_NORM_QC_NO},
	{0x3285, UNICODE_NORM_QC_NO},
	{0x3286, UNICODE_NORM_QC_NO},
	{0x3287, UNICODE_NORM_QC_NO},
	{0x3288, UNICODE_NORM_QC_NO},
	{0x3289, UNICODE_NORM_QC_NO},
	{0x328A, UNICODE_NORM_QC_NO},
	{0x328B, UNICODE_NORM_QC_NO},
	{0x328C, UNICODE_NORM_QC_NO},
	{0x328D, UNICODE_NORM_QC_NO},
	{0x328E, UNICODE_NORM_QC_NO},
	{0x328F, UNICODE_NORM_QC_NO},
	{0x3290, UNICODE_NORM_QC_NO},
	{0x3291, UNICODE_NORM_QC_NO},
	{0x3292, UNICODE_NORM_QC_NO},
	{0x3293, UNICODE_NORM_QC_NO},
	{0x3294, UNICODE_NORM_QC_NO},
	{0x3295, UNICODE_NORM_QC_NO},
	{0x3296, UNICODE_NORM_QC_NO},
	{0x3297, UNICODE_NORM_QC_NO},
	{0x3298, UNICODE_NORM_QC_NO},
	{0x3299, UNICODE_NORM_QC_NO},
	{0x329A, UNICODE_NORM_QC_NO},
	{0x329B, UNICODE_NORM_QC_NO},
	{0x329C, UNICODE_NORM_QC_NO},
	{0x329D, UNICODE_NORM_QC_NO},
	{0x329E, UNICODE_NORM_QC_NO},
	{0x329F, UNICODE_NORM_QC_NO},
	{0x32A0, UNICODE_NORM_QC_NO},
	{0x32A1, UNICODE_NORM_QC_NO},
	{0x32A2, UNICODE_NORM_QC_NO},
	{0x32A3, UNICODE_NORM_QC_NO},
	{0x32A4, UNICODE_NORM_QC_NO},
	{0x32A5, UNICODE_NORM_QC_NO},
	{0x32A6, UNICODE_NORM_QC_NO},
	{0x32A7, UNICODE_NORM_QC_NO},
	{0x32A8, UNICODE_NORM_QC_NO},
	{0x32A9, UNICODE_NORM_QC_NO},
	{0x32AA, UNICODE_NORM_QC_NO},
	{0x32AB, UNICODE_NORM_QC_NO},
	{0x32AC, UNICODE_NORM_QC_NO},
	{0x32AD, UNICODE_NORM_QC_NO},
	{0x32AE, UNICODE_NORM_QC_NO},
	{0x32AF, UNICODE_NORM_QC_NO},
	{0x32B0, UNICODE_NORM_QC_NO},
	{0x32B1, UNICODE_NORM_QC_NO},
	{0x32B2, UNICODE_NORM_QC_NO},
	{0x32B3, UNICODE_NORM_QC_NO},
	{0x32B4, UNICODE_NORM_QC_NO},
	{0x32B5, UNICODE_NORM_QC_NO},
	{0x32B6, UNICODE_NORM_QC_NO},
	{0x32B7, UNICODE_NORM_QC_NO},
	{0x32B8, UNICODE_NORM_QC_NO},
	{0x32B9, UNICODE_NORM_QC_NO},
	{0x32BA, UNICODE_NORM_QC_NO},
	{0x32BB, UNICODE_NORM_QC_NO},
	{0x32BC, UNICODE_NORM_QC_NO},
	{0x32BD, UNICODE_NORM_QC_NO},
	{0x32BE, UNICODE_NORM_QC_NO},
	{0x32BF, UNICODE_NORM_QC_NO},
	{0x32C0, UNICODE_NORM_QC_NO},
	{0x32C1, UNICODE_NORM_QC_NO},
	{0x32C2, UNICODE_NORM_QC_NO},
	{0x32C3, UNICODE_NORM_QC_NO},
	{0x32C4, UNICODE_NORM_QC_NO},
	{0x32C5, UNICODE_NORM_QC_NO},
	{0x32C6, UNICODE_NORM_QC_NO},
	{0x32C7, UNICODE_NORM_QC_NO},
	{0x32C8, UNICODE_NORM_QC_NO},
	{0x32C9, UNICODE_NORM_QC_NO},
	{0x32CA, UNICODE_NORM_QC_NO},
	{0x32CB, UNICODE_NORM_QC_NO},
	{0x32CC, UNICODE_NORM_QC_NO},
	{0x32CD, UNICODE_NORM_QC_NO},
	{0x32CE, UNICODE_NORM_QC_NO},
	{0x32CF, UNICODE_NORM_QC_NO},
	{0x32D0, UNICODE_NORM_QC_NO},
	{0x32D1, UNICODE_NORM_QC_NO},
	{0x32D2, UNICODE_NORM_QC_NO},
	{0x32D3, UNICODE_NORM_QC_NO},
	{0x32D4, UNICODE_NORM_QC_NO},
	{0x32D5, UNICODE_NORM_QC_NO},
	{0x32D6, UNICODE_NORM_QC_NO},
	{0x32D7, UNICODE_NORM_QC_NO},
	{0x32D8, UNICODE_NORM_QC_NO},
	{0x32D9, UNICODE_NORM_QC_NO},
	{0x32DA, UNICODE_NORM_QC_NO},
	{0x32DB, UNICODE_NORM_QC_NO},
	{0x32DC, UNICODE_NORM_QC_NO},
	{0x32DD, UNICODE_NORM_QC_NO},
	{0x32DE, UNICODE_NORM_QC_NO},
	{0x32DF, UNICODE_NORM_QC_NO},
	{0x32E0, UNICODE_NORM_QC_NO},
	{0x32E1, UNICODE_NORM_QC_NO},
	{0x32E2, UNICODE_NORM_QC_NO},
	{0x32E3, UNICODE_NORM_QC_NO},
	{0x32E4, UNICODE_NORM_QC_NO},
	{0x32E5, UNICODE_NORM_QC_NO},
	{0x32E6, UNICODE_NORM_QC_NO},
	{0x32E7, UNICODE_NORM_QC_NO},
	{0x32E8, UNICODE_NORM_QC_NO},
	{0x32E9, UNICODE_NORM_QC_NO},
	{0x32EA, UNICODE_NORM_QC_NO},
	{0x32EB, UNICODE_NORM_QC_NO},
	{0x32EC, UNICODE_NORM_QC_NO},
	{0x32ED, UNICODE_NORM_QC_NO},
	{0x32EE, UNICODE_NORM_QC_NO},
	{0x32EF, UNICODE_NORM_QC_NO},
	{0x32F0, UNICODE_NORM_QC_NO},
	{0x32F1, UNICODE_NORM_QC_NO},
	{0x32F2, UNICODE_NORM_QC_NO},
	{0x32F3, UNICODE_NORM_QC_NO},
	{0x32F4, UNICODE_NORM_QC_NO},
	{0x32F5, UNICODE_NORM_QC_NO},
	{0x32F6, UNICODE_NORM_QC_NO},
	{0x32F7, UNICODE_NORM_QC_NO},
	{0x32F8, UNICODE_NORM_QC_NO},
	{0x32F9, UNICODE_NORM_QC_NO},
	{0x32FA, UNICODE_NORM_QC_NO},
	{0x32FB, UNICODE_NORM_QC_NO},
	{0x32FC, UNICODE_NORM_QC_NO},
	{0x32FD, UNICODE_NORM_QC_NO},
	{0x32FE, UNICODE_NORM_QC_NO},
	{0x32FF, UNICODE_NORM_QC_NO},
	{0x3300, UNICODE_NORM_QC_NO},
	{0x3301, UNICODE_NORM_QC_NO},
	{0x3302, UNICODE_NORM_QC_NO},
	{0x3303, UNICODE_NORM_QC_NO},
	{0x3304, UNICODE_NORM_QC_NO},
	{0x3305, UNICODE_NORM_QC_NO},
	{0x3306, UNICODE_NORM_QC_NO},
	{0x3307, UNICODE_NORM_QC_NO},
	{0x3308, UNICODE_NORM_QC_NO},
	{0x3309, UNICODE_NORM_QC_NO},
	{0x330A, UNICODE_NORM_QC_NO},
	{0x330B, UNICODE_NORM_QC_NO},
	{0x330C, UNICODE_NORM_QC_NO},
	{0x330D, UNICODE_NORM_QC_NO},
	{0x330E, UNICODE_NORM_QC_NO},
	{0x330F, UNICODE_NORM_QC_NO},
	{0x3310, UNICODE_NORM_QC_NO},
	{0x3311, UNICODE_NORM_QC_NO},
	{0x3312, UNICODE_NORM_QC_NO},
	{0x3313, UNICODE_NORM_QC_NO},
	{0x3314, UNICODE_NORM_QC_NO},
	{0x3315, UNICODE_NORM_QC_NO},
	{0x3316, UNICODE_NORM_QC_NO},
	{0x3317, UNICODE_NORM_QC_NO},
	{0x3318, UNICODE_NORM_QC_NO},
	{0x3319, UNICODE_NORM_QC_NO},
	{0x331A, UNICODE_NORM_QC_NO},
	{0x331B, UNICODE_NORM_QC_NO},
	{0x331C, UNICODE_NORM_QC_NO},
	{0x331D, UNICODE_NORM_QC_NO},
	{0x331E, UNICODE_NORM_QC_NO},
	{0x331F, UNICODE_NORM_QC_NO},
	{0x3320, UNICODE_NORM_QC_NO},
	{0x3321, UNICODE_NORM_QC_NO},
	{0x3322, UNICODE_NORM_QC_NO},
	{0x3323, UNICODE_NORM_QC_NO},
	{0x3324, UNICODE_NORM_QC_NO},
	{0x3325, UNICODE_NORM_QC_NO},
	{0x3326, UNICODE_NORM_QC_NO},
	{0x3327, UNICODE_NORM_QC_NO},
	{0x3328, UNICODE_NORM_QC_NO},
	{0x3329, UNICODE_NORM_QC_NO},
	{0x332A, UNICODE_NORM_QC_NO},
	{0x332B, UNICODE_NORM_QC_NO},
	{0x332C, UNICODE_NORM_QC_NO},
	{0x332D, UNICODE_NORM_QC_NO},
	{0x332E, UNICODE_NORM_QC_NO},
	{0x332F, UNICODE_NORM_QC_NO},
	{0x3330, UNICODE_NORM_QC_NO},
	{0x3331, UNICODE_NORM_QC_NO},
	{0x3332, UNICODE_NORM_QC_NO},
	{0x3333, UNICODE_NORM_QC_NO},
	{0x3334, UNICODE_NORM_QC_NO},
	{0x3335, UNICODE_NORM_QC_NO},
	{0x3336, UNICODE_NORM_QC_NO},
	{0x3337, UNICODE_NORM_QC_NO},
	{0x3338, UNICODE_NORM_QC_NO},
	{0x3339, UNICODE_NORM_QC_NO},
	{0x333A, UNICODE_NORM_QC_NO},
	{0x333B, UNICODE_NORM_QC_NO},
	{0x333C, UNICODE_NORM_QC_NO},
	{0x333D, UNICODE_NORM_QC_NO},
	{0x333E, UNICODE_NORM_QC_NO},
	{0x333F, UNICODE_NORM_QC_NO},
	{0x3340, UNICODE_NORM_QC_NO},
	{0x3341, UNICODE_NORM_QC_NO},
	{0x3342, UNICODE_NORM_QC_NO},
	{0x3343, UNICODE_NORM_QC_NO},
	{0x3344, UNICODE_NORM_QC_NO},
	{0x3345, UNICODE_NORM_QC_NO},
	{0x3346, UNICODE_NORM_QC_NO},
	{0x3347, UNICODE_NORM_QC_NO},
	{0x3348, UNICODE_NORM_QC_NO},
	{0x3349, UNICODE_NORM_QC_NO},
	{0x334A, UNICODE_NORM_QC_NO},
	{0x334B, UNICODE_NORM_QC_NO},
	{0x334C, UNICODE_NORM_QC_NO},
	{0x334D, UNICODE_NORM_QC_NO},
	{0x334E, UNICODE_NORM_QC_NO},
	{0x334F, UNICODE_NORM_QC_NO},
	{0x3350, UNICODE_NORM_QC_NO},
	{0x3351, UNICODE_NORM_QC_NO},
	{0x3352, UNICODE_NORM_QC_NO},
	{0x3353, UNICODE_NORM_QC_NO},
	{0x3354, UNICODE_NORM_QC_NO},
	{0x3355, UNICODE_NORM_QC_NO},
	{0x3356, UNICODE_NORM_QC_NO},
	{0x3357, UNICODE_NORM_QC_NO},
	{0x3358, UNICODE_NORM_QC_NO},
	{0x3359, UNICODE_NORM_QC_NO},
	{0x335A, UNICODE_NORM_QC_NO},
	{0x335B, UNICODE_NORM_QC_NO},
	{0x335C, UNICODE_NORM_QC_NO},
	{0x335D, UNICODE_NORM_QC_NO},
	{0x335E, UNICODE_NORM_QC_NO},
	{0x335F, UNICODE_NORM_QC_NO},
	{0x3360, UNICODE_NORM_QC_NO},
	{0x3361, UNICODE_NORM_QC_NO},
	{0x3362, UNICODE_NORM_QC_NO},
	{0x3363, UNICODE_NORM_QC_NO},
	{0x3364, UNICODE_NORM_QC_NO},
	{0x3365, UNICODE_NORM_QC_NO},
	{0x3366, UNICODE_NORM_QC_NO},
	{0x3367, UNICODE_NORM_QC_NO},
	{0x3368, UNICODE_NORM_QC_NO},
	{0x3369, UNICODE_NORM_QC_NO},
	{0x336A, UNICODE_NORM_QC_NO},
	{0x336B, UNICODE_NORM_QC_NO},
	{0x336C, UNICODE_NORM_QC_NO},
	{0x336D, UNICODE_NORM_QC_NO},
	{0x336E, UNICODE_NORM_QC_NO},
	{0x336F, UNICODE_NORM_QC_NO},
	{0x3370, UNICODE_NORM_QC_NO},
	{0x3371, UNICODE_NORM_QC_NO},
	{0x3372, UNICODE_NORM_QC_NO},
	{0x3373, UNICODE_NORM_QC_NO},
	{0x3374, UNICODE_NORM_QC_NO},
	{0x3375, UNICODE_NORM_QC_NO},
	{0x3376, UNICODE_NORM_QC_NO},
	{0x3377, UNICODE_NORM_QC_NO},
	{0x3378, UNICODE_NORM_QC_NO},
	{0x3379, UNICODE_NORM_QC_NO},
	{0x337A, UNICODE_NORM_QC_NO},
	{0x337B, UNICODE_NORM_QC_NO},
	{0x337C, UNICODE_NORM_QC_NO},
	{0x337D, UNICODE_NORM_QC_NO},
	{0x337E, UNICODE_NORM_QC_NO},
	{0x337F, UNICODE_NORM_QC_NO},
	{0x3380, UNICODE_NORM_QC_NO},
	{0x3381, UNICODE_NORM_QC_NO},
	{0x3382, UNICODE_NORM_QC_NO},
	{0x3383, UNICODE_NORM_QC_NO},
	{0x3384, UNICODE_NORM_QC_NO},
	{0x3385, UNICODE_NORM_QC_NO},
	{0x3386, UNICODE_NORM_QC_NO},
	{0x3387, UNICODE_NORM_QC_NO},
	{0x3388, UNICODE_NORM_QC_NO},
	{0x3389, UNICODE_NORM_QC_NO},
	{0x338A, UNICODE_NORM_QC_NO},
	{0x338B, UNICODE_NORM_QC_NO},
	{0x338C, UNICODE_NORM_QC_NO},
	{0x338D, UNICODE_NORM_QC_NO},
	{0x338E, UNICODE_NORM_QC_NO},
	{0x338F, UNICODE_NORM_QC_NO},
	{0x3390, UNICODE_NORM_QC_NO},
	{0x3391, UNICODE_NORM_QC_NO},
	{0x3392, UNICODE_NORM_QC_NO},
	{0x3393, UNICODE_NORM_QC_NO},
	{0x3394, UNICODE_NORM_QC_NO},
	{0x3395, UNICODE_NORM_QC_NO},
	{0x3396, UNICODE_NORM_QC_NO},
	{0x3397, UNICODE_NORM_QC_NO},
	{0x3398, UNICODE_NORM_QC_NO},
	{0x3399, UNICODE_NORM_QC_NO},
	{0x339A, UNICODE_NORM_QC_NO},
	{0x339B, UNICODE_NORM_QC_NO},
	{0x339C, UNICODE_NORM_QC_NO},
	{0x339D, UNICODE_NORM_QC_NO},
	{0x339E, UNICODE_NORM_QC_NO},
	{0x339F, UNICODE_NORM_QC_NO},
	{0x33A0, UNICODE_NORM_QC_NO},
	{0x33A1, UNICODE_NORM_QC_NO},
	{0x33A2, UNICODE_NORM_QC_NO},
	{0x33A3, UNICODE_NORM_QC_NO},
	{0x33A4, UNICODE_NORM_QC_NO},
	{0x33A5, UNICODE_NORM_QC_NO},
	{0x33A6, UNICODE_NORM_QC_NO},
	{0x33A7, UNICODE_NORM_QC_NO},
	{0x33A8, UNICODE_NORM_QC_NO},
	{0x33A9, UNICODE_NORM_QC_NO},
	{0x33AA, UNICODE_NORM_QC_NO},
	{0x33AB, UNICODE_NORM_QC_NO},
	{0x33AC, UNICODE_NORM_QC_NO},
	{0x33AD, UNICODE_NORM_QC_NO},
	{0x33AE, UNICODE_NORM_QC_NO},
	{0x33AF, UNICODE_NORM_QC_NO},
	{0x33B0, UNICODE_NORM_QC_NO},
	{0x33B1, UNICODE_NORM_QC_NO},
	{0x33B2, UNICODE_NORM_QC_NO},
	{0x33B3, UNICODE_NORM_QC_NO},
	{0x33B4, UNICODE_NORM_QC_NO},
	{0x33B5, UNICODE_NORM_QC_NO},
	{0x33B6, UNICODE_NORM_QC_NO},
	{0x33B7, UNICODE_NORM_QC_NO},
	{0x33B8, UNICODE_NORM_QC_NO},
	{0x33B9, UNICODE_NORM_QC_NO},
	{0x33BA, UNICODE_NORM_QC_NO},
	{0x33BB, UNICODE_NORM_QC_NO},
	{0x33BC, UNICODE_NORM_QC_NO},
	{0x33BD, UNICODE_NORM_QC_NO},
	{0x33BE, UNICODE_NORM_QC_NO},
	{0x33BF, UNICODE_NORM_QC_NO},
	{0x33C0, UNICODE_NORM_QC_NO},
	{0x33C1, UNICODE_NORM_QC_NO},
	{0x33C2, UNICODE_NORM_QC_NO},
	{0x33C3, UNICODE_NORM_QC_NO},
	{0x33C4, UNICODE_NORM_QC_NO},
	{0x33C5, UNICODE_NORM_QC_NO},
	{0x33C6, UNICODE_NORM_QC_NO},
	{0x33C7, UNICODE_NORM_QC_NO},
	{0x33C8, UNICODE_NORM_QC_NO},
	{0x33C9, UNICODE_NORM_QC_NO},
	{0x33CA, UNICODE_NORM_QC_NO},
	{0x33CB, UNICODE_NORM_QC_NO},
	{0x33CC, UNICODE_NORM_QC_NO},
	{0x33CD, UNICODE_NORM_QC_NO},
	{0x33CE, UNICODE_NORM_QC_NO},
	{0x33CF, UNICODE_NORM_QC_NO},
	{0x33D0, UNICODE_NORM_QC_NO},
	{0x33D1, UNICODE_NORM_QC_NO},
	{0x33D2, UNICODE_NORM_QC_NO},
	{0x33D3, UNICODE_NORM_QC_NO},
	{0x33D4, UNICODE_NORM_QC_NO},
	{0x33D5, UNICODE_NORM_QC_NO},
	{0x33D6, UNICODE_NORM_QC_NO},
	{0x33D7, UNICODE_NORM_QC_NO},
	{0x33D8, UNICODE_NORM_QC_NO},
	{0x33D9, UNICODE_NORM_QC_NO},
	{0x33DA, UNICODE_NORM_QC_NO},
	{0x33DB, UNICODE_NORM_QC_NO},
	{0x33DC, UNICODE_NORM_QC_NO},
	{0x33DD, UNICODE_NORM_QC_NO},
	{0x33DE, UNICODE_NORM_QC_NO},
	{0x33DF, UNICODE_NORM_QC_NO},
	{0x33E0, UNICODE_NORM_QC_NO},
	{0x33E1, UNICODE_NORM_QC_NO},
	{0x33E2, UNICODE_NORM_QC_NO},
	{0x33E3, UNICODE_NORM_QC_NO},
	{0x33E4, UNICODE_NORM_QC_NO},
	{0x33E5, UNICODE_NORM_QC_NO},
	{0x33E6, UNICODE_NORM_QC_NO},
	{0x33E7, UNICODE_NORM_QC_NO},
	{0x33E8, UNICODE_NORM_QC_NO},
	{0x33E9, UNICODE_NORM_QC_NO},
	{0x33EA, UNICODE_NORM_QC_NO},
	{0x33EB, UNICODE_NORM_QC_NO},
	{0x33EC, UNICODE_NORM_QC_NO},
	{0x33ED, UNICODE_NORM_QC_NO},
	{0x33EE, UNICODE_NORM_QC_NO},
	{0x33EF, UNICODE_NORM_QC_NO},
	{0x33F0, UNICODE_NORM_QC_NO},
	{0x33F1, UNICODE_NORM_QC_NO},
	{0x33F2, UNICODE_NORM_QC_NO},
	{0x33F3, UNICODE_NORM_QC_NO},
	{0x33F4, UNICODE_NORM_QC_NO},
	{0x33F5, UNICODE_NORM_QC_NO},
	{0x33F6, UNICODE_NORM_QC_NO},
	{0x33F7, UNICODE_NORM_QC_NO},
	{0x33F8, UNICODE_NORM_QC_NO},
	{0x33F9, UNICODE_NORM_QC_NO},
	{0x33FA, UNICODE_NORM_QC_NO},
	{0x33FB, UNICODE_NORM_QC_NO},
	{0x33FC, UNICODE_NORM_QC_NO},
	{0x33FD, UNICODE_NORM_QC_NO},
	{0x33FE, UNICODE_NORM_QC_NO},
	{0x33FF, UNICODE_NORM_QC_NO},
	{0xA69C, UNICODE_NORM_QC_NO},
	{0xA69D, UNICODE_NORM_QC_NO},
	{0xA770, UNICODE_NORM_QC_NO},
	{0xA7F2, UNICODE_NORM_QC_NO},
	{0xA7F3, UNICODE_NORM_QC_NO},
	{0xA7F4, UNICODE_NORM_QC_NO},
	{0xA7F8, UNICODE_NORM_QC_NO},
	{0xA7F9, UNICODE_NORM_QC_NO},
	{0xAB5C, UNICODE_NORM_QC_NO},
	{0xAB5D, UNICODE_NORM_QC_NO},
	{0xAB5E, UNICODE_NORM_QC_NO},
	{0xAB5F, UNICODE_NORM_QC_NO},
	{0xAB69, UNICODE_NORM_QC_NO},
	{0xF900, UNICODE_NORM_QC_NO},
	{0xF901, UNICODE_NORM_QC_NO},
	{0xF902, UNICODE_NORM_QC_NO},
	{0xF903, UNICODE_NORM_QC_NO},
	{0xF904, UNICODE_NORM_QC_NO},
	{0xF905, UNICODE_NORM_QC_NO},
	{0xF906, UNICODE_NORM_QC_NO},
	{0xF907, UNICODE_NORM_QC_NO},
	{0xF908, UNICODE_NORM_QC_NO},
	{0xF909, UNICODE_NORM_QC_NO},
	{0xF90A, UNICODE_NORM_QC_NO},
	{0xF90B, UNICODE_NORM_QC_NO},
	{0xF90C, UNICODE_NORM_QC_NO},
	{0xF90D, UNICODE_NORM_QC_NO},
	{0xF90E, UNICODE_NORM_QC_NO},
	{0xF90F, UNICODE_NORM_QC_NO},
	{0xF910, UNICODE_NORM_QC_NO},
	{0xF911, UNICODE_NORM_QC_NO},
	{0xF912, UNICODE_NORM_QC_NO},
	{0xF913, UNICODE_NORM_QC_NO},
	{0xF914, UNICODE_NORM_QC_NO},
	{0xF915, UNICODE_NORM_QC_NO},
	{0xF916, UNICODE_NORM_QC_NO},
	{0xF917, UNICODE_NORM_QC_NO},
	{0xF918, UNICODE_NORM_QC_NO},
	{0xF919, UNICODE_NORM_QC_NO},
	{0xF91A, UNICODE_NORM_QC_NO},
	{0xF91B, UNICODE_NORM_QC_NO},
	{0xF91C, UNICODE_NORM_QC_NO},
	{0xF91D, UNICODE_NORM_QC_NO},
	{0xF91E, UNICODE_NORM_QC_NO},
	{0xF91F, UNICODE_NORM_QC_NO},
	{0xF920, UNICODE_NORM_QC_NO},
	{0xF921, UNICODE_NORM_QC_NO},
	{0xF922, UNICODE_NORM_QC_NO},
	{0xF923, UNICODE_NORM_QC_NO},
	{0xF924, UNICODE_NORM_QC_NO},
	{0xF925, UNICODE_NORM_QC_NO},
	{0xF926, UNICODE_NORM_QC_NO},
	{0xF927, UNICODE_NORM_QC_NO},
	{0xF928, UNICODE_NORM_QC_NO},
	{0xF929, UNICODE_NORM_QC_NO},
	{0xF92A, UNICODE_NORM_QC_NO},
	{0xF92B, UNICODE_NORM_QC_NO},
	{0xF92C, UNICODE_NORM_QC_NO},
	{0xF92D, UNICODE_NORM_QC_NO},
	{0xF92E, UNICODE_NORM_QC_NO},
	{0xF92F, UNICODE_NORM_QC_NO},
	{0xF930, UNICODE_NORM_QC_NO},
	{0xF931, UNICODE_NORM_QC_NO},
	{0xF932, UNICODE_NORM_QC_NO},
	{0xF933, UNICODE_NORM_QC_NO},
	{0xF934, UNICODE_NORM_QC_NO},
	{0xF935, UNICODE_NORM_QC_NO},
	{0xF936, UNICODE_NORM_QC_NO},
	{0xF937, UNICODE_NORM_QC_NO},
	{0xF938, UNICODE_NORM_QC_NO},
	{0xF939, UNICODE_NORM_QC_NO},
	{0xF93A, UNICODE_NORM_QC_NO},
	{0xF93B, UNICODE_NORM_QC_NO},
	{0xF93C, UNICODE_NORM_QC_NO},
	{0xF93D, UNICODE_NORM_QC_NO},
	{0xF93E, UNICODE_NORM_QC_NO},
	{0xF93F, UNICODE_NORM_QC_NO},
	{0xF940, UNICODE_NORM_QC_NO},
	{0xF941, UNICODE_NORM_QC_NO},
	{0xF942, UNICODE_NORM_QC_NO},
	{0xF943, UNICODE_NORM_QC_NO},
	{0xF944, UNICODE_NORM_QC_NO},
	{0xF945, UNICODE_NORM_QC_NO},
	{0xF946, UNICODE_NORM_QC_NO},
	{0xF947, UNICODE_NORM_QC_NO},
	{0xF948, UNICODE_NORM_QC_NO},
	{0xF949, UNICODE_NORM_QC_NO},
	{0xF94A, UNICODE_NORM_QC_NO},
	{0xF94B, UNICODE_NORM_QC_NO},
	{0xF94C, UNICODE_NORM_QC_NO},
	{0xF94D, UNICODE_NORM_QC_NO},
	{0xF94E, UNICODE_NORM_QC_NO},
	{0xF94F, UNICODE_NORM_QC_NO},
	{0xF950, UNICODE_NORM_QC_NO},
	{0xF951, UNICODE_NORM_QC_NO},
	{0xF952, UNICODE_NORM_QC_NO},
	{0xF953, UNICODE_NORM_QC_NO},
	{0xF954, UNICODE_NORM_QC_NO},
	{0xF955, UNICODE_NORM_QC_NO},
	{0xF956, UNICODE_NORM_QC_NO},
	{0xF957, UNICODE_NORM_QC_NO},
	{0xF958, UNICODE_NORM_QC_NO},
	{0xF959, UNICODE_NORM_QC_NO},
	{0xF95A, UNICODE_NORM_QC_NO},
	{0xF95B, UNICODE_NORM_QC_NO},
	{0xF95C, UNICODE_NORM_QC_NO},
	{0xF95D, UNICODE_NORM_QC_NO},
	{0xF95E, UNICODE_NORM_QC_NO},
	{0xF95F, UNICODE_NORM_QC_NO},
	{0xF960, UNICODE_NORM_QC_NO},
	{0xF961, UNICODE_NORM_QC_NO},
	{0xF962, UNICODE_NORM_QC_NO},
	{0xF963, UNICODE_NORM_QC_NO},
	{0xF964, UNICODE_NORM_QC_NO},
	{0xF965, UNICODE_NORM_QC_NO},
	{0xF966, UNICODE_NORM_QC_NO},
	{0xF967, UNICODE_NORM_QC_NO},
	{0xF968, UNICODE_NORM_QC_NO},
	{0xF969, UNICODE_NORM_QC_NO},
	{0xF96A, UNICODE_NORM_QC_NO},
	{0xF96B, UNICODE_NORM_QC_NO},
	{0xF96C, UNICODE_NORM_QC_NO},
	{0xF96D, UNICODE_NORM_QC_NO},
	{0xF96E, UNICODE_NORM_QC_NO},
	{0xF96F, UNICODE_NORM_QC_NO},
	{0xF970, UNICODE_NORM_QC_NO},
	{0xF971, UNICODE_NORM_QC_NO},
	{0xF972, UNICODE_NORM_QC_NO},
	{0xF973, UNICODE_NORM_QC_NO},
	{0xF974, UNICODE_NORM_QC_NO},
	{0xF975, UNICODE_NORM_QC_NO},
	{0xF976, UNICODE_NORM_QC_NO},
	{0xF977, UNICODE_NORM_QC_NO},
	{0xF978, UNICODE_NORM_QC_NO},
	{0xF979, UNICODE_NORM_QC_NO},
	{0xF97A, UNICODE_NORM_QC_NO},
	{0xF97B, UNICODE_NORM_QC_NO},
	{0xF97C, UNICODE_NORM_QC_NO},
	{0xF97D, UNICODE_NORM_QC_NO},
	{0xF97E, UNICODE_NORM_QC_NO},
	{0xF97F, UNICODE_NORM_QC_NO},
	{0xF980, UNICODE_NORM_QC_NO},
	{0xF981, UNICODE_NORM_QC_NO},
	{0xF982, UNICODE_NORM_QC_NO},
	{0xF983, UNICODE_NORM_QC_NO},
	{0xF984, UNICODE_NORM_QC_NO},
	{0xF985, UNICODE_NORM_QC_NO},
	{0xF986, UNICODE_NORM_QC_NO},
	{0xF987, UNICODE_NORM_QC_NO},
	{0xF988, UNICODE_NORM_QC_NO},
	{0xF989, UNICODE_NORM_QC_NO},
	{0xF98A, UNICODE_NORM_QC_NO},
	{0xF98B, UNICODE_NORM_QC_NO},
	{0xF98C, UNICODE_NORM_QC_NO},
	{0xF98D, UNICODE_NORM_QC_NO},
	{0xF98E, UNICODE_NORM_QC_NO},
	{0xF98F, UNICODE_NORM_QC_NO},
	{0xF990, UNICODE_NORM_QC_NO},
	{0xF991, UNICODE_NORM_QC_NO},
	{0xF992, UNICODE_NORM_QC_NO},
	{0xF993, UNICODE_NORM_QC_NO},
	{0xF994, UNICODE_NORM_QC_NO},
	{0xF995, UNICODE_NORM_QC_NO},
	{0xF996, UNICODE_NORM_QC_NO},
	{0xF997, UNICODE_NORM_QC_NO},
	{0xF998, UNICODE_NORM_QC_NO},
	{0xF999, UNICODE_NORM_QC_NO},
	{0xF99A, UNICODE_NORM_QC_NO},
	{0xF99B, UNICODE_NORM_QC_NO},
	{0xF99C, UNICODE_NORM_QC_NO},
	{0xF99D, UNICODE_NORM_QC_NO},
	{0xF99E, UNICODE_NORM_QC_NO},
	{0xF99F, UNICODE_NORM_QC_NO},
	{0xF9A0, UNICODE_NORM_QC_NO},
	{0xF9A1, UNICODE_NORM_QC_NO},
	{0xF9A2, UNICODE_NORM_QC_NO},
	{0xF9A3, UNICODE_NORM_QC_NO},
	{0xF9A4, UNICODE_NORM_QC_NO},
	{0xF9A5, UNICODE_NORM_QC_NO},
	{0xF9A6, UNICODE_NORM_QC_NO},
	{0xF9A7, UNICODE_NORM_QC_NO},
	{0xF9A8, UNICODE_NORM_QC_NO},
	{0xF9A9, UNICODE_NORM_QC_NO},
	{0xF9AA, UNICODE_NORM_QC_NO},
	{0xF9AB, UNICODE_NORM_QC_NO},
	{0xF9AC, UNICODE_NORM_QC_NO},
	{0xF9AD, UNICODE_NORM_QC_NO},
	{0xF9AE, UNICODE_NORM_QC_NO},
	{0xF9AF, UNICODE_NORM_QC_NO},
	{0xF9B0, UNICODE_NORM_QC_NO},
	{0xF9B1, UNICODE_NORM_QC_NO},
	{0xF9B2, UNICODE_NORM_QC_NO},
	{0xF9B3, UNICODE_NORM_QC_NO},
	{0xF9B4, UNICODE_NORM_QC_NO},
	{0xF9B5, UNICODE_NORM_QC_NO},
	{0xF9B6, UNICODE_NORM_QC_NO},
	{0xF9B7, UNICODE_NORM_QC_NO},
	{0xF9B8, UNICODE_NORM_QC_NO},
	{0xF9B9, UNICODE_NORM_QC_NO},
	{0xF9BA, UNICODE_NORM_QC_NO},
	{0xF9BB, UNICODE_NORM_QC_NO},
	{0xF9BC, UNICODE_NORM_QC_NO},
	{0xF9BD, UNICODE_NORM_QC_NO},
	{0xF9BE, UNICODE_NORM_QC_NO},
	{0xF9BF, UNICODE_NORM_QC_NO},
	{0xF9C0, UNICODE_NORM_QC_NO},
	{0xF9C1, UNICODE_NORM_QC_NO},
	{0xF9C2, UNICODE_NORM_QC_NO},
	{0xF9C3, UNICODE_NORM_QC_NO},
	{0xF9C4, UNICODE_NORM_QC_NO},
	{0xF9C5, UNICODE_NORM_QC_NO},
	{0xF9C6, UNICODE_NORM_QC_NO},
	{0xF9C7, UNICODE_NORM_QC_NO},
	{0xF9C8, UNICODE_NORM_QC_NO},
	{0xF9C9, UNICODE_NORM_QC_NO},
	{0xF9CA, UNICODE_NORM_QC_NO},
	{0xF9CB, UNICODE_NORM_QC_NO},
	{0xF9CC, UNICODE_NORM_QC_NO},
	{0xF9CD, UNICODE_NORM_QC_NO},
	{0xF9CE, UNICODE_NORM_QC_NO},
	{0xF9CF, UNICODE_NORM_QC_NO},
	{0xF9D0, UNICODE_NORM_QC_NO},
	{0xF9D1, UNICODE_NORM_QC_NO},
	{0xF9D2, UNICODE_NORM_QC_NO},
	{0xF9D3, UNICODE_NORM_QC_NO},
	{0xF9D4, UNICODE_NORM_QC_NO},
	{0xF9D5, UNICODE_NORM_QC_NO},
	{0xF9D6, UNICODE_NORM_QC_NO},
	{0xF9D7, UNICODE_NORM_QC_NO},
	{0xF9D8, UNICODE_NORM_QC_NO},
	{0xF9D9, UNICODE_NORM_QC_NO},
	{0xF9DA, UNICODE_NORM_QC_NO},
	{0xF9DB, UNICODE_NORM_QC_NO},
	{0xF9DC, UNICODE_NORM_QC_NO},
	{0xF9DD, UNICODE_NORM_QC_NO},
	{0xF9DE, UNICODE_NORM_QC_NO},
	{0xF9DF, UNICODE_NORM_QC_NO},
	{0xF9E0, UNICODE_NORM_QC_NO},
	{0xF9E1, UNICODE_NORM_QC_NO},
	{0xF9E2, UNICODE_NORM_QC_NO},
	{0xF9E3, UNICODE_NORM_QC_NO},
	{0xF9E4, UNICODE_NORM_QC_NO},
	{0xF9E5, UNICODE_NORM_QC_NO},
	{0xF9E6, UNICODE_NORM_QC_NO},
	{0xF9E7, UNICODE_NORM_QC_NO},
	{0xF9E8, UNICODE_NORM_QC_NO},
	{0xF9E9, UNICODE_NORM_QC_NO},
	{0xF9EA, UNICODE_NORM_QC_NO},
	{0xF9EB, UNICODE_NORM_QC_NO},
	{0xF9EC, UNICODE_NORM_QC_NO},
	{0xF9ED, UNICODE_NORM_QC_NO},
	{0xF9EE, UNICODE_NORM_QC_NO},
	{0xF9EF, UNICODE_NORM_QC_NO},
	{0xF9F0, UNICODE_NORM_QC_NO},
	{0xF9F1, UNICODE_NORM_QC_NO},
	{0xF9F2, UNICODE_NORM_QC_NO},
	{0xF9F3, UNICODE_NORM_QC_NO},
	{0xF9F4, UNICODE_NORM_QC_NO},
	{0xF9F5, UNICODE_NORM_QC_NO},
	{0xF9F6, UNICODE_NORM_QC_NO},
	{0xF9F7, UNICODE_NORM_QC_NO},
	{0xF9F8, UNICODE_NORM_QC_NO},
	{0xF9F9, UNICODE_NORM_QC_NO},
	{0xF9FA, UNICODE_NORM_QC_NO},
	{0xF9FB, UNICODE_NORM_QC_NO},
	{0xF9FC, UNICODE_NORM_QC_NO},
	{0xF9FD, UNICODE_NORM_QC_NO},
	{0xF9FE, UNICODE_NORM_QC_NO},
	{0xF9FF, UNICODE_NORM_QC_NO},
	{0xFA00, UNICODE_NORM_QC_NO},
	{0xFA01, UNICODE_NORM_QC_NO},
	{0xFA02, UNICODE_NORM_QC_NO},
	{0xFA03, UNICODE_NORM_QC_NO},
	{0xFA04, UNICODE_NORM_QC_NO},
	{0xFA05, UNICODE_NORM_QC_NO},
	{0xFA06, UNICODE_NORM_QC_NO},
	{0xFA07, UNICODE_NORM_QC_NO},
	{0xFA08, UNICODE_NORM_QC_NO},
	{0xFA09, UNICODE_NORM_QC_NO},
	{0xFA0A, UNICODE_NORM_QC_NO},
	{0xFA0B, UNICODE_NORM_QC_NO},
	{0xFA0C, UNICODE_NORM_QC_NO},
	{0xFA0D, UNICODE_NORM_QC_NO},
	{0xFA10, UNICODE_NORM_QC_NO},
	{0xFA12, UNICODE_NORM_QC_NO},
	{0xFA15, UNICODE_NORM_QC_NO},
	{0xFA16, UNICODE_NORM_QC_NO},
	{0xFA17, UNICODE_NORM_QC_NO},
	{0xFA18, UNICODE_NORM_QC_NO},
	{0xFA19, UNICODE_NORM_QC_NO},
	{0xFA1A, UNICODE_NORM_QC_NO},
	{0xFA1B, UNICODE_NORM_QC_NO},
	{0xFA1C, UNICODE_NORM_QC_NO},
	{0xFA1D, UNICODE_NORM_QC_NO},
	{0xFA1E, UNICODE_NORM_QC_NO},
	{0xFA20, UNICODE_NORM_QC_NO},
	{0xFA22, UNICODE_NORM_QC_NO},
	{0xFA25, UNICODE_NORM_QC_NO},
	{0xFA26, UNICODE_NORM_QC_NO},
	{0xFA2A, UNICODE_NORM_QC_NO},
	{0xFA2B, UNICODE_NORM_QC_NO},
	{0xFA2C, UNICODE_NORM_QC_NO},
	{0xFA2D, UNICODE_NORM_QC_NO},
	{0xFA2E, UNICODE_NORM_QC_NO},
	{0xFA2F, UNICODE_NORM_QC_NO},
	{0xFA30, UNICODE_NORM_QC_NO},
	{0xFA31, UNICODE_NORM_QC_NO},
	{0xFA32, UNICODE_NORM_QC_NO},
	{0xFA33, UNICODE_NORM_QC_NO},
	{0xFA34, UNICODE_NORM_QC_NO},
	{0xFA35, UNICODE_NORM_QC_NO},
	{0xFA36, UNICODE_NORM_QC_NO},
	{0xFA37, UNICODE_NORM_QC_NO},
	{0xFA38, UNICODE_NORM_QC_NO},
	{0xFA39, UNICODE_NORM_QC_NO},
	{0xFA3A, UNICODE_NORM_QC_NO},
	{0xFA3B, UNICODE_NORM_QC_NO},
	{0xFA3C, UNICODE_NORM_QC_NO},
	{0xFA3D, UNICODE_NORM_QC_NO},
	{0xFA3E, UNICODE_NORM_QC_NO},
	{0xFA3F, UNICODE_NORM_QC_NO},
	{0xFA40, UNICODE_NORM_QC_NO},
	{0xFA41, UNICODE_NORM_QC_NO},
	{0xFA42, UNICODE_NORM_QC_NO},
	{0xFA43, UNICODE_NORM_QC_NO},
	{0xFA44, UNICODE_NORM_QC_NO},
	{0xFA45, UNICODE_NORM_QC_NO},
	{0xFA46, UNICODE_NORM_QC_NO},
	{0xFA47, UNICODE_NORM_QC_NO},
	{0xFA48, UNICODE_NORM_QC_NO},
	{0xFA49, UNICODE_NORM_QC_NO},
	{0xFA4A, UNICODE_NORM_QC_NO},
	{0xFA4B, UNICODE_NORM_QC_NO},
	{0xFA4C, UNICODE_NORM_QC_NO},
	{0xFA4D, UNICODE_NORM_QC_NO},
	{0xFA4E, UNICODE_NORM_QC_NO},
	{0xFA4F, UNICODE_NORM_QC_NO},
	{0xFA50, UNICODE_NORM_QC_NO},
	{0xFA51, UNICODE_NORM_QC_NO},
	{0xFA52, UNICODE_NORM_QC_NO},
	{0xFA53, UNICODE_NORM_QC_NO},
	{0xFA54, UNICODE_NORM_QC_NO},
	{0xFA55, UNICODE_NORM_QC_NO},
	{0xFA56, UNICODE_NORM_QC_NO},
	{0xFA57, UNICODE_NORM_QC_NO},
	{0xFA58, UNICODE_NORM_QC_NO},
	{0xFA59, UNICODE_NORM_QC_NO},
	{0xFA5A, UNICODE_NORM_QC_NO},
	{0xFA5B, UNICODE_NORM_QC_NO},
	{0xFA5C, UNICODE_NORM_QC_NO},
	{0xFA5D, UNICODE_NORM_QC_NO},
	{0xFA5E, UNICODE_NORM_QC_NO},
	{0xFA5F, UNICODE_NORM_QC_NO},
	{0xFA60, UNICODE_NORM_QC_NO},
	{0xFA61, UNICODE_NORM_QC_NO},
	{0xFA62, UNICODE_NORM_QC_NO},
	{0xFA63, UNICODE_NORM_QC_NO},
	{0xFA64, UNICODE_NORM_QC_NO},
	{0xFA65, UNICODE_NORM_QC_NO},
	{0xFA66, UNICODE_NORM_QC_NO},
	{0xFA67, UNICODE_NORM_QC_NO},
	{0xFA68, UNICODE_NORM_QC_NO},
	{0xFA69, UNICODE_NORM_QC_NO},
	{0xFA6A, UNICODE_NORM_QC_NO},
	{0xFA6B, UNICODE_NORM_QC_NO},
	{0xFA6C, UNICODE_NORM_QC_NO},
	{0xFA6D, UNICODE_NORM_QC_NO},
	{0xFA70, UNICODE_NORM_QC_NO},
	{0xFA71, UNICODE_NORM_QC_NO},
	{0xFA72, UNICODE_NORM_QC_NO},
	{0xFA73, UNICODE_NORM_QC_NO},
	{0xFA74, UNICODE_NORM_QC_NO},
	{0xFA75, UNICODE_NORM_QC_NO},
	{0xFA76, UNICODE_NORM_QC_NO},
	{0xFA77, UNICODE_NORM_QC_NO},
	{0xFA78, UNICODE_NORM_QC_NO},
	{0xFA79, UNICODE_NORM_QC_NO},
	{0xFA7A, UNICODE_NORM_QC_NO},
	{0xFA7B, UNICODE_NORM_QC_NO},
	{0xFA7C, UNICODE_NORM_QC_NO},
	{0xFA7D, UNICODE_NORM_QC_NO},
	{0xFA7E, UNICODE_NORM_QC_NO},
	{0xFA7F, UNICODE_NORM_QC_NO},
	{0xFA80, UNICODE_NORM_QC_NO},
	{0xFA81, UNICODE_NORM_QC_NO},
	{0xFA82, UNICODE_NORM_QC_NO},
	{0xFA83, UNICODE_NORM_QC_NO},
	{0xFA84, UNICODE_NORM_QC_NO},
	{0xFA85, UNICODE_NORM_QC_NO},
	{0xFA86, UNICODE_NORM_QC_NO},
	{0xFA87, UNICODE_NORM_QC_NO},
	{0xFA88, UNICODE_NORM_QC_NO},
	{0xFA89, UNICODE_NORM_QC_NO},
	{0xFA8A, UNICODE_NORM_QC_NO},
	{0xFA8B, UNICODE_NORM_QC_NO},
	{0xFA8C, UNICODE_NORM_QC_NO},
	{0xFA8D, UNICODE_NORM_QC_NO},
	{0xFA8E, UNICODE_NORM_QC_NO},
	{0xFA8F, UNICODE_NORM_QC_NO},
	{0xFA90, UNICODE_NORM_QC_NO},
	{0xFA91, UNICODE_NORM_QC_NO},
	{0xFA92, UNICODE_NORM_QC_NO},
	{0xFA93, UNICODE_NORM_QC_NO},
	{0xFA94, UNICODE_NORM_QC_NO},
	{0xFA95, UNICODE_NORM_QC_NO},
	{0xFA96, UNICODE_NORM_QC_NO},
	{0xFA97, UNICODE_NORM_QC_NO},
	{0xFA98, UNICODE_NORM_QC_NO},
	{0xFA99, UNICODE_NORM_QC_NO},
	{0xFA9A, UNICODE_NORM_QC_NO},
	{0xFA9B, UNICODE_NORM_QC_NO},
	{0xFA9C, UNICODE_NORM_QC_NO},
	{0xFA9D, UNICODE_NORM_QC_NO},
	{0xFA9E, UNICODE_NORM_QC_NO},
	{0xFA9F, UNICODE_NORM_QC_NO},
	{0xFAA0, UNICODE_NORM_QC_NO},
	{0xFAA1, UNICODE_NORM_QC_NO},
	{0xFAA2, UNICODE_NORM_QC_NO},
	{0xFAA3, UNICODE_NORM_QC_NO},
	{0xFAA4, UNICODE_NORM_QC_NO},
	{0xFAA5, UNICODE_NORM_QC_NO},
	{0xFAA6, UNICODE_NORM_QC_NO},
	{0xFAA7, UNICODE_NORM_QC_NO},
	{0xFAA8, UNICODE_NORM_QC_NO},
	{0xFAA9, UNICODE_NORM_QC_NO},
	{0xFAAA, UNICODE_NORM_QC_NO},
	{0xFAAB, UNICODE_NORM_QC_NO},
	{0xFAAC, UNICODE_NORM_QC_NO},
	{0xFAAD, UNICODE_NORM_QC_NO},
	{0xFAAE, UNICODE_NORM_QC_NO},
	{0xFAAF, UNICODE_NORM_QC_NO},
	{0xFAB0, UNICODE_NORM_QC_NO},
	{0xFAB1, UNICODE_NORM_QC_NO},
	{0xFAB2, UNICODE_NORM_QC_NO},
	{0xFAB3, UNICODE_NORM_QC_NO},
	{0xFAB4, UNICODE_NORM_QC_NO},
	{0xFAB5, UNICODE_NORM_QC_NO},
	{0xFAB6, UNICODE_NORM_QC_NO},
	{0xFAB7, UNICODE_NORM_QC_NO},
	{0xFAB8, UNICODE_NORM_QC_NO},
	{0xFAB9, UNICODE_NORM_QC_NO},
	{0xFABA, UNICODE_NORM_QC_NO},
	{0xFABB, UNICODE_NORM_QC_NO},
	{0xFABC, UNICODE_NORM_QC_NO},
	{0xFABD, UNICODE_NORM_QC_NO},
	{0xFABE, UNICODE_NORM_QC_NO},
	{0xFABF, UNICODE_NORM_QC_NO},
	{0xFAC0, UNICODE_NORM_QC_NO},
	{0xFAC1, UNICODE_NORM_QC_NO},
	{0xFAC2, UNICODE_NORM_QC_NO},
	{0xFAC3, UNICODE_NORM_QC_NO},
	{0xFAC4, UNICODE_NORM_QC_NO},
	{0xFAC5, UNICODE_NORM_QC_NO},
	{0xFAC6, UNICODE_NORM_QC_NO},
	{0xFAC7, UNICODE_NORM_QC_NO},
	{0xFAC8, UNICODE_NORM_QC_NO},
	{0xFAC9, UNICODE_NORM_QC_NO},
	{0xFACA, UNICODE_NORM_QC_NO},
	{0xFACB, UNICODE_NORM_QC_NO},
	{0xFACC, UNICODE_NORM_QC_NO},
	{0xFACD, UNICODE_NORM_QC_NO},
	{0xFACE, UNICODE_NORM_QC_NO},
	{0xFACF, UNICODE_NORM_QC_NO},
	{0xFAD0, UNICODE_NORM_QC_NO},
	{0xFAD1, UNICODE_NORM_QC_NO},
	{0xFAD2, UNICODE_NORM_QC_NO},
	{0xFAD3, UNICODE_NORM_QC_NO},
	{0xFAD4, UNICODE_NORM_QC_NO},
	{0xFAD5, UNICODE_NORM_QC_NO},
	{0xFAD6, UNICODE_NORM_QC_NO},
	{0xFAD7, UNICODE_NORM_QC_NO},
	{0xFAD8, UNICODE_NORM_QC_NO},
	{0xFAD9, UNICODE_NORM_QC_NO},
	{0xFB00, UNICODE_NORM_QC_NO},
	{0xFB01, UNICODE_NORM_QC_NO},
	{0xFB02, UNICODE_NORM_QC_NO},
	{0xFB03, UNICODE_NORM_QC_NO},
	{0xFB04, UNICODE_NORM_QC_NO},
	{0xFB05, UNICODE_NORM_QC_NO},
	{0xFB06, UNICODE_NORM_QC_NO},
	{0xFB13, UNICODE_NORM_QC_NO},
	{0xFB14, UNICODE_NORM_QC_NO},
	{0xFB15, UNICODE_NORM_QC_NO},
	{0xFB16, UNICODE_NORM_QC_NO},
	{0xFB17, UNICODE_NORM_QC_NO},
	{0xFB1D, UNICODE_NORM_QC_NO},
	{0xFB1F, UNICODE_NORM_QC_NO},
	{0xFB20, UNICODE_NORM_QC_NO},
	{0xFB21, UNICODE_NORM_QC_NO},
	{0xFB22, UNICODE_NORM_QC_NO},
	{0xFB23, UNICODE_NORM_QC_NO},
	{0xFB24, UNICODE_NORM_QC_NO},
	{0xFB25, UNICODE_NORM_QC_NO},
	{0xFB26, UNICODE_NORM_QC_NO},
	{0xFB27, UNICODE_NORM_QC_NO},
	{0xFB28, UNICODE_NORM_QC_NO},
	{0xFB29, UNICODE_NORM_QC_NO},
	{0xFB2A, UNICODE_NORM_QC_NO},
	{0xFB2B, UNICODE_NORM_QC_NO},
	{0xFB2C, UNICODE_NORM_QC_NO},
	{0xFB2D, UNICODE_NORM_QC_NO},
	{0xFB2E, UNICODE_NORM_QC_NO},
	{0xFB2F, UNICODE_NORM_QC_NO},
	{0xFB30, UNICODE_NORM_QC_NO},
	{0xFB31, UNICODE_NORM_QC_NO},
	{0xFB32, UNICODE_NORM_QC_NO},
	{0xFB33, UNICODE_NORM_QC_NO},
	{0xFB34, UNICODE_NORM_QC_NO},
	{0xFB35, UNICODE_NORM_QC_NO},
	{0xFB36, UNICODE_NORM_QC_NO},
	{0xFB38, UNICODE_NORM_QC_NO},
	{0xFB39, UNICODE_NORM_QC_NO},
	{0xFB3A, UNICODE_NORM_QC_NO},
	{0xFB3B, UNICODE_NORM_QC_NO},
	{0xFB3C, UNICODE_NORM_QC_NO},
	{0xFB3E, UNICODE_NORM_QC_NO},
	{0xFB40, UNICODE_NORM_QC_NO},
	{0xFB41, UNICODE_NORM_QC_NO},
	{0xFB43, UNICODE_NORM_QC_NO},
	{0xFB44, UNICODE_NORM_QC_NO},
	{0xFB46, UNICODE_NORM_QC_NO},
	{0xFB47, UNICODE_NORM_QC_NO},
	{0xFB48, UNICODE_NORM_QC_NO},
	{0xFB49, UNICODE_NORM_QC_NO},
	{0xFB4A, UNICODE_NORM_QC_NO},
	{0xFB4B, UNICODE_NORM_QC_NO},
	{0xFB4C, UNICODE_NORM_QC_NO},
	{0xFB4D, UNICODE_NORM_QC_NO},
	{0xFB4E, UNICODE_NORM_QC_NO},
	{0xFB4F, UNICODE_NORM_QC_NO},
	{0xFB50, UNICODE_NORM_QC_NO},
	{0xFB51, UNICODE_NORM_QC_NO},
	{0xFB52, UNICODE_NORM_QC_NO},
	{0xFB53, UNICODE_NORM_QC_NO},
	{0xFB54, UNICODE_NORM_QC_NO},
	{0xFB55, UNICODE_NORM_QC_NO},
	{0xFB56, UNICODE_NORM_QC_NO},
	{0xFB57, UNICODE_NORM_QC_NO},
	{0xFB58, UNICODE_NORM_QC_NO},
	{0xFB59, UNICODE_NORM_QC_NO},
	{0xFB5A, UNICODE_NORM_QC_NO},
	{0xFB5B, UNICODE_NORM_QC_NO},
	{0xFB5C, UNICODE_NORM_QC_NO},
	{0xFB5D, UNICODE_NORM_QC_NO},
	{0xFB5E, UNICODE_NORM_QC_NO},
	{0xFB5F, UNICODE_NORM_QC_NO},
	{0xFB60, UNICODE_NORM_QC_NO},
	{0xFB61, UNICODE_NORM_QC_NO},
	{0xFB62, UNICODE_NORM_QC_NO},
	{0xFB63, UNICODE_NORM_QC_NO},
	{0xFB64, UNICODE_NORM_QC_NO},
	{0xFB65, UNICODE_NORM_QC_NO},
	{0xFB66, UNICODE_NORM_QC_NO},
	{0xFB67, UNICODE_NORM_QC_NO},
	{0xFB68, UNICODE_NORM_QC_NO},
	{0xFB69, UNICODE_NORM_QC_NO},
	{0xFB6A, UNICODE_NORM_QC_NO},
	{0xFB6B, UNICODE_NORM_QC_NO},
	{0xFB6C, UNICODE_NORM_QC_NO},
	{0xFB6D, UNICODE_NORM_QC_NO},
	{0xFB6E, UNICODE_NORM_QC_NO},
	{0xFB6F, UNICODE_NORM_QC_NO},
	{0xFB70, UNICODE_NORM_QC_NO},
	{0xFB71, UNICODE_NORM_QC_NO},
	{0xFB72, UNICODE_NORM_QC_NO},
	{0xFB73, UNICODE_NORM_QC_NO},
	{0xFB74, UNICODE_NORM_QC_NO},
	{0xFB75, UNICODE_NORM_QC_NO},
	{0xFB76, UNICODE_NORM_QC_NO},
	{0xFB77, UNICODE_NORM_QC_NO},
	{0xFB78, UNICODE_NORM_QC_NO},
	{0xFB79, UNICODE_NORM_QC_NO},
	{0xFB7A, UNICODE_NORM_QC_NO},
	{0xFB7B, UNICODE_NORM_QC_NO},
	{0xFB7C, UNICODE_NORM_QC_NO},
	{0xFB7D, UNICODE_NORM_QC_NO},
	{0xFB7E, UNICODE_NORM_QC_NO},
	{0xFB7F, UNICODE_NORM_QC_NO},
	{0xFB80, UNICODE_NORM_QC_NO},
	{0xFB81, UNICODE_NORM_QC_NO},
	{0xFB82, UNICODE_NORM_QC_NO},
	{0xFB83, UNICODE_NORM_QC_NO},
	{0xFB84, UNICODE_NORM_QC_NO},
	{0xFB85, UNICODE_NORM_QC_NO},
	{0xFB86, UNICODE_NORM_QC_NO},
	{0xFB87, UNICODE_NORM_QC_NO},
	{0xFB88, UNICODE_NORM_QC_NO},
	{0xFB89, UNICODE_NORM_QC_NO},
	{0xFB8A, UNICODE_NORM_QC_NO},
	{0xFB8B, UNICODE_NORM_QC_NO},
	{0xFB8C, UNICODE_NORM_QC_NO},
	{0xFB8D, UNICODE_NORM_QC_NO},
	{0xFB8E, UNICODE_NORM_QC_NO},
	{0xFB8F, UNICODE_NORM_QC_NO},
	{0xFB90, UNICODE_NORM_QC_NO},
	{0xFB91, UNICODE_NORM_QC_NO},
	{0xFB92, UNICODE_NORM_QC_NO},
	{0xFB93, UNICODE_NORM_QC_NO},
	{0xFB94, UNICODE_NORM_QC_NO},
	{0xFB95, UNICODE_NORM_QC_NO},
	{0xFB96, UNICODE_NORM_QC_NO},
	{0xFB97, UNICODE_NORM_QC_NO},
	{0xFB98, UNICODE_NORM_QC_NO},
	{0xFB99, UNICODE_NORM_QC_NO},
	{0xFB9A, UNICODE_NORM_QC_NO},
	{0xFB9B, UNICODE_NORM_QC_NO},
	{0xFB9C, UNICODE_NORM_QC_NO},
	{0xFB9D, UNICODE_NORM_QC_NO},
	{0xFB9E, UNICODE_NORM_QC_NO},
	{0xFB9F, UNICODE_NORM_QC_NO},
	{0xFBA0, UNICODE_NORM_QC_NO},
	{0xFBA1, UNICODE_NORM_QC_NO},
	{0xFBA2, UNICODE_NORM_QC_NO},
	{0xFBA3, UNICODE_NORM_QC_NO},
	{0xFBA4, UNICODE_NORM_QC_NO},
	{0xFBA5, UNICODE_NORM_QC_NO},
	{0xFBA6, UNICODE_NORM_QC_NO},
	{0xFBA7, UNICODE_NORM_QC_NO},
	{0xFBA8, UNICODE_NORM_QC_NO},
	{0xFBA9, UNICODE_NORM_QC_NO},
	{0xFBAA, UNICODE_NORM_QC_NO},
	{0xFBAB, UNICODE_NORM_QC_NO},
	{0xFBAC, UNICODE_NORM_QC_NO},
	{0xFBAD, UNICODE_NORM_QC_NO},
	{0xFBAE, UNICODE_NORM_QC_NO},
	{0xFBAF, UNICODE_NORM_QC_NO},
	{0xFBB0, UNICODE_NORM_QC_NO},
	{0xFBB1, UNICODE_NORM_QC_NO},
	{0xFBD3, UNICODE_NORM_QC_NO},
	{0xFBD4, UNICODE_NORM_QC_NO},
	{0xFBD5, UNICODE_NORM_QC_NO},
	{0xFBD6, UNICODE_NORM_QC_NO},
	{0xFBD7, UNICODE_NORM_QC_NO},
	{0xFBD8, UNICODE_NORM_QC_NO},
	{0xFBD9, UNICODE_NORM_QC_NO},
	{0xFBDA, UNICODE_NORM_QC_NO},
	{0xFBDB, UNICODE_NORM_QC_NO},
	{0xFBDC, UNICODE_NORM_QC_NO},
	{0xFBDD, UNICODE_NORM_QC_NO},
	{0xFBDE, UNICODE_NORM_QC_NO},
	{0xFBDF, UNICODE_NORM_QC_NO},
	{0xFBE0, UNICODE_NORM_QC_NO},
	{0xFBE1, UNICODE_NORM_QC_NO},
	{0xFBE2, UNICODE_NORM_QC_NO},
	{0xFBE3, UNICODE_NORM_QC_NO},
	{0xFBE4, UNICODE_NORM_QC_NO},
	{0xFBE5, UNICODE_NORM_QC_NO},
	{0xFBE6, UNICODE_NORM_QC_NO},
	{0xFBE7, UNICODE_NORM_QC_NO},
	{0xFBE8, UNICODE_NORM_QC_NO},
	{0xFBE9, UNICODE_NORM_QC_NO},
	{0xFBEA, UNICODE_NORM_QC_NO},
	{0xFBEB, UNICODE_NORM_QC_NO},
	{0xFBEC, UNICODE_NORM_QC_NO},
	{0xFBED, UNICODE_NORM_QC_NO},
	{0xFBEE, UNICODE_NORM_QC_NO},
	{0xFBEF, UNICODE_NORM_QC_NO},
	{0xFBF0, UNICODE_NORM_QC_NO},
	{0xFBF1, UNICODE_NORM_QC_NO},
	{0xFBF2, UNICODE_NORM_QC_NO},
	{0xFBF3, UNICODE_NORM_QC_NO},
	{0xFBF4, UNICODE_NORM_QC_NO},
	{0xFBF5, UNICODE_NORM_QC_NO},
	{0xFBF6, UNICODE_NORM_QC_NO},
	{0xFBF7, UNICODE_NORM_QC_NO},
	{0xFBF8, UNICODE_NORM_QC_NO},
	{0xFBF9, UNICODE_NORM_QC_NO},
	{0xFBFA, UNICODE_NORM_QC_NO},
	{0xFBFB, UNICODE_NORM_QC_NO},
	{0xFBFC, UNICODE_NORM_QC_NO},
	{0xFBFD, UNICODE_NORM_QC_NO},
	{0xFBFE, UNICODE_NORM_QC_NO},
	{0xFBFF, UNICODE_NORM_QC_NO},
	{0xFC00, UNICODE_NORM_QC_NO},
	{0xFC01, UNICODE_NORM_QC_NO},
	{0xFC02, UNICODE_NORM_QC_NO},
	{0xFC03, UNICODE_NORM_QC_NO},
	{0xFC04, UNICODE_NORM_QC_NO},
	{0xFC05, UNICODE_NORM_QC_NO},
	{0xFC06, UNICODE_NORM_QC_NO},
	{0xFC07, UNICODE_NORM_QC_NO},
	{0xFC08, UNICODE_NORM_QC_NO},
	{0xFC09, UNICODE_NORM_QC_NO},
	{0xFC0A, UNICODE_NORM_QC_NO},
	{0xFC0B, UNICODE_NORM_QC_NO},
	{0xFC0C, UNICODE_NORM_QC_NO},
	{0xFC0D, UNICODE_NORM_QC_NO},
	{0xFC0E, UNICODE_NORM_QC_NO},
	{0xFC0F, UNICODE_NORM_QC_NO},
	{0xFC10, UNICODE_NORM_QC_NO},
	{0xFC11, UNICODE_NORM_QC_NO},
	{0xFC12, UNICODE_NORM_QC_NO},
	{0xFC13, UNICODE_NORM_QC_NO},
	{0xFC14, UNICODE_NORM_QC_NO},
	{0xFC15, UNICODE_NORM_QC_NO},
	{0xFC16, UNICODE_NORM_QC_NO},
	{0xFC17, UNICODE_NORM_QC_NO},
	{0xFC18, UNICODE_NORM_QC_NO},
	{0xFC19, UNICODE_NORM_QC_NO},
	{0xFC1A, UNICODE_NORM_QC_NO},
	{0xFC1B, UNICODE_NORM_QC_NO},
	{0xFC1C, UNICODE_NORM_QC_NO},
	{0xFC1D, UNICODE_NORM_QC_NO},
	{0xFC1E, UNICODE_NORM_QC_NO},
	{0xFC1F, UNICODE_NORM_QC_NO},
	{0xFC20, UNICODE_NORM_QC_NO},
	{0xFC21, UNICODE_NORM_QC_NO},
	{0xFC22, UNICODE_NORM_QC_NO},
	{0xFC23, UNICODE_NORM_QC_NO},
	{0xFC24, UNICODE_NORM_QC_NO},
	{0xFC25, UNICODE_NORM_QC_NO},
	{0xFC26, UNICODE_NORM_QC_NO},
	{0xFC27, UNICODE_NORM_QC_NO},
	{0xFC28, UNICODE_NORM_QC_NO},
	{0xFC29, UNICODE_NORM_QC_NO},
	{0xFC2A, UNICODE_NORM_QC_NO},
	{0xFC2B, UNICODE_NORM_QC_NO},
	{0xFC2C, UNICODE_NORM_QC_NO},
	{0xFC2D, UNICODE_NORM_QC_NO},
	{0xFC2E, UNICODE_NORM_QC_NO},
	{0xFC2F, UNICODE_NORM_QC_NO},
	{0xFC30, UNICODE_NORM_QC_NO},
	{0xFC31, UNICODE_NORM_QC_NO},
	{0xFC32, UNICODE_NORM_QC_NO},
	{0xFC33, UNICODE_NORM_QC_NO},
	{0xFC34, UNICODE_NORM_QC_NO},
	{0xFC35, UNICODE_NORM_QC_NO},
	{0xFC36, UNICODE_NORM_QC_NO},
	{0xFC37, UNICODE_NORM_QC_NO},
	{0xFC38, UNICODE_NORM_QC_NO},
	{0xFC39, UNICODE_NORM_QC_NO},
	{0xFC3A, UNICODE_NORM_QC_NO},
	{0xFC3B, UNICODE_NORM_QC_NO},
	{0xFC3C, UNICODE_NORM_QC_NO},
	{0xFC3D, UNICODE_NORM_QC_NO},
	{0xFC3E, UNICODE_NORM_QC_NO},
	{0xFC3F, UNICODE_NORM_QC_NO},
	{0xFC40, UNICODE_NORM_QC_NO},
	{0xFC41, UNICODE_NORM_QC_NO},
	{0xFC42, UNICODE_NORM_QC_NO},
	{0xFC43, UNICODE_NORM_QC_NO},
	{0xFC44, UNICODE_NORM_QC_NO},
	{0xFC45, UNICODE_NORM_QC_NO},
	{0xFC46, UNICODE_NORM_QC_NO},
	{0xFC47, UNICODE_NORM_QC_NO},
	{0xFC48, UNICODE_NORM_QC_NO},
	{0xFC49, UNICODE_NORM_QC_NO},
	{0xFC4A, UNICODE_NORM_QC_NO},
	{0xFC4B, UNICODE_NORM_QC_NO},
	{0xFC4C, UNICODE_NORM_QC_NO},
	{0xFC4D, UNICODE_NORM_QC_NO},
	{0xFC4E, UNICODE_NORM_QC_NO},
	{0xFC4F, UNICODE_NORM_QC_NO},
	{0xFC50, UNICODE_NORM_QC_NO},
	{0xFC51, UNICODE_NORM_QC_NO},
	{0xFC52, UNICODE_NORM_QC_NO},
	{0xFC53, UNICODE_NORM_QC_NO},
	{0xFC54, UNICODE_NORM_QC_NO},
	{0xFC55, UNICODE_NORM_QC_NO},
	{0xFC56, UNICODE_NORM_QC_NO},
	{0xFC57, UNICODE_NORM_QC_NO},
	{0xFC58, UNICODE_NORM_QC_NO},
	{0xFC59, UNICODE_NORM_QC_NO},
	{0xFC5A, UNICODE_NORM_QC_NO},
	{0xFC5B, UNICODE_NORM_QC_NO},
	{0xFC5C, UNICODE_NORM_QC_NO},
	{0xFC5D, UNICODE_NORM_QC_NO},
	{0xFC5E, UNICODE_NORM_QC_NO},
	{0xFC5F, UNICODE_NORM_QC_NO},
	{0xFC60, UNICODE_NORM_QC_NO},
	{0xFC61, UNICODE_NORM_QC_NO},
	{0xFC62, UNICODE_NORM_QC_NO},
	{0xFC63, UNICODE_NORM_QC_NO},
	{0xFC64, UNICODE_NORM_QC_NO},
	{0xFC65, UNICODE_NORM_QC_NO},
	{0xFC66, UNICODE_NORM_QC_NO},
	{0xFC67, UNICODE_NORM_QC_NO},
	{0xFC68, UNICODE_NORM_QC_NO},
	{0xFC69, UNICODE_NORM_QC_NO},
	{0xFC6A, UNICODE_NORM_QC_NO},
	{0xFC6B, UNICODE_NORM_QC_NO},
	{0xFC6C, UNICODE_NORM_QC_NO},
	{0xFC6D, UNICODE_NORM_QC_NO},
	{0xFC6E, UNICODE_NORM_QC_NO},
	{0xFC6F, UNICODE_NORM_QC_NO},
	{0xFC70, UNICODE_NORM_QC_NO},
	{0xFC71, UNICODE_NORM_QC_NO},
	{0xFC72, UNICODE_NORM_QC_NO},
	{0xFC73, UNICODE_NORM_QC_NO},
	{0xFC74, UNICODE_NORM_QC_NO},
	{0xFC75, UNICODE_NORM_QC_NO},
	{0xFC76, UNICODE_NORM_QC_NO},
	{0xFC77, UNICODE_NORM_QC_NO},
	{0xFC78, UNICODE_NORM_QC_NO},
	{0xFC79, UNICODE_NORM_QC_NO},
	{0xFC7A, UNICODE_NORM_QC_NO},
	{0xFC7B, UNICODE_NORM_QC_NO},
	{0xFC7C, UNICODE_NORM_QC_NO},
	{0xFC7D, UNICODE_NORM_QC_NO},
	{0xFC7E, UNICODE_NORM_QC_NO},
	{0xFC7F, UNICODE_NORM_QC_NO},
	{0xFC80, UNICODE_NORM_QC_NO},
	{0xFC81, UNICODE_NORM_QC_NO},
	{0xFC82, UNICODE_NORM_QC_NO},
	{0xFC83, UNICODE_NORM_QC_NO},
	{0xFC84, UNICODE_NORM_QC_NO},
	{0xFC85, UNICODE_NORM_QC_NO},
	{0xFC86, UNICODE_NORM_QC_NO},
	{0xFC87, UNICODE_NORM_QC_NO},
	{0xFC88, UNICODE_NORM_QC_NO},
	{0xFC89, UNICODE_NORM_QC_NO},
	{0xFC8A, UNICODE_NORM_QC_NO},
	{0xFC8B, UNICODE_NORM_QC_NO},
	{0xFC8C, UNICODE_NORM_QC_NO},
	{0xFC8D, UNICODE_NORM_QC_NO},
	{0xFC8E, UNICODE_NORM_QC_NO},
	{0xFC8F, UNICODE_NORM_QC_NO},
	{0xFC90, UNICODE_NORM_QC_NO},
	{0xFC91, UNICODE_NORM_QC_NO},
	{0xFC92, UNICODE_NORM_QC_NO},
	{0xFC93, UNICODE_NORM_QC_NO},
	{0xFC94, UNICODE_NORM_QC_NO},
	{0xFC95, UNICODE_NORM_QC_NO},
	{0xFC96, UNICODE_NORM_QC_NO},
	{0xFC97, UNICODE_NORM_QC_NO},
	{0xFC98, UNICODE_NORM_QC_NO},
	{0xFC99, UNICODE_NORM_QC_NO},
	{0xFC9A, UNICODE_NORM_QC_NO},
	{0xFC9B, UNICODE_NORM_QC_NO},
	{0xFC9C, UNICODE_NORM_QC_NO},
	{0xFC9D, UNICODE_NORM_QC_NO},
	{0xFC9E, UNICODE_NORM_QC_NO},
	{0xFC9F, UNICODE_NORM_QC_NO},
	{0xFCA0, UNICODE_NORM_QC_NO},
	{0xFCA1, UNICODE_NORM_QC_NO},
	{0xFCA2, UNICODE_NORM_QC_NO},
	{0xFCA3, UNICODE_NORM_QC_NO},
	{0xFCA4, UNICODE_NORM_QC_NO},
	{0xFCA5, UNICODE_NORM_QC_NO},
	{0xFCA6, UNICODE_NORM_QC_NO},
	{0xFCA7, UNICODE_NORM_QC_NO},
	{0xFCA8, UNICODE_NORM_QC_NO},
	{0xFCA9, UNICODE_NORM_QC_NO},
	{0xFCAA, UNICODE_NORM_QC_NO},
	{0xFCAB, UNICODE_NORM_QC_NO},
	{0xFCAC, UNICODE_NORM_QC_NO},
	{0xFCAD, UNICODE_NORM_QC_NO},
	{0xFCAE, UNICODE_NORM_QC_NO},
	{0xFCAF, UNICODE_NORM_QC_NO},
	{0xFCB0, UNICODE_NORM_QC_NO},
	{0xFCB1, UNICODE_NORM_QC_NO},
	{0xFCB2, UNICODE_NORM_QC_NO},
	{0xFCB3, UNICODE_NORM_QC_NO},
	{0xFCB4, UNICODE_NORM_QC_NO},
	{0xFCB5, UNICODE_NORM_QC_NO},
	{0xFCB6, UNICODE_NORM_QC_NO},
	{0xFCB7, UNICODE_NORM_QC_NO},
	{0xFCB8, UNICODE_NORM_QC_NO},
	{0xFCB9, UNICODE_NORM_QC_NO},
	{0xFCBA, UNICODE_NORM_QC_NO},
	{0xFCBB, UNICODE_NORM_QC_NO},
	{0xFCBC, UNICODE_NORM_QC_NO},
	{0xFCBD, UNICODE_NORM_QC_NO},
	{0xFCBE, UNICODE_NORM_QC_NO},
	{0xFCBF, UNICODE_NORM_QC_NO},
	{0xFCC0, UNICODE_NORM_QC_NO},
	{0xFCC1, UNICODE_NORM_QC_NO},
	{0xFCC2, UNICODE_NORM_QC_NO},
	{0xFCC3, UNICODE_NORM_QC_NO},
	{0xFCC4, UNICODE_NORM_QC_NO},
	{0xFCC5, UNICODE_NORM_QC_NO},
	{0xFCC6, UNICODE_NORM_QC_NO},
	{0xFCC7, UNICODE_NORM_QC_NO},
	{0xFCC8, UNICODE_NORM_QC_NO},
	{0xFCC9, UNICODE_NORM_QC_NO},
	{0xFCCA, UNICODE_NORM_QC_NO},
	{0xFCCB, UNICODE_NORM_QC_NO},
	{0xFCCC, UNICODE_NORM_QC_NO},
	{0xFCCD, UNICODE_NORM_QC_NO},
	{0xFCCE, UNICODE_NORM_QC_NO},
	{0xFCCF, UNICODE_NORM_QC_NO},
	{0xFCD0, UNICODE_NORM_QC_NO},
	{0xFCD1, UNICODE_NORM_QC_NO},
	{0xFCD2, UNICODE_NORM_QC_NO},
	{0xFCD3, UNICODE_NORM_QC_NO},
	{0xFCD4, UNICODE_NORM_QC_NO},
	{0xFCD5, UNICODE_NORM_QC_NO},
	{0xFCD6, UNICODE_NORM_QC_NO},
	{0xFCD7, UNICODE_NORM_QC_NO},
	{0xFCD8, UNICODE_NORM_QC_NO},
	{0xFCD9, UNICODE_NORM_QC_NO},
	{0xFCDA, UNICODE_NORM_QC_NO},
	{0xFCDB, UNICODE_NORM_QC_NO},
	{0xFCDC, UNICODE_NORM_QC_NO},
	{0xFCDD, UNICODE_NORM_QC_NO},
	{0xFCDE, UNICODE_NORM_QC_NO},
	{0xFCDF, UNICODE_NORM_QC_NO},
	{0xFCE0, UNICODE_NORM_QC_NO},
	{0xFCE1, UNICODE_NORM_QC_NO},
	{0xFCE2, UNICODE_NORM_QC_NO},
	{0xFCE3, UNICODE_NORM_QC_NO},
	{0xFCE4, UNICODE_NORM_QC_NO},
	{0xFCE5, UNICODE_NORM_QC_NO},
	{0xFCE6, UNICODE_NORM_QC_NO},
	{0xFCE7, UNICODE_NORM_QC_NO},
	{0xFCE8, UNICODE_NORM_QC_NO},
	{0xFCE9, UNICODE_NORM_QC_NO},
	{0xFCEA, UNICODE_NORM_QC_NO},
	{0xFCEB, UNICODE_NORM_QC_NO},
	{0xFCEC, UNICODE_NORM_QC_NO},
	{0xFCED, UNICODE_NORM_QC_NO},
	{0xFCEE, UNICODE_NORM_QC_NO},
	{0xFCEF, UNICODE_NORM_QC_NO},
	{0xFCF0, UNICODE_NORM_QC_NO},
	{0xFCF1, UNICODE_NORM_QC_NO},
	{0xFCF2, UNICODE_NORM_QC_NO},
	{0xFCF3, UNICODE_NORM_QC_NO},
	{0xFCF4, UNICODE_NORM_QC_NO},
	{0xFCF5, UNICODE_NORM_QC_NO},
	{0xFCF6, UNICODE_NORM_QC_NO},
	{0xFCF7, UNICODE_NORM_QC_NO},
	{0xFCF8, UNICODE_NORM_QC_NO},
	{0xFCF9, UNICODE_NORM_QC_NO},
	{0xFCFA, UNICODE_NORM_QC_NO},
	{0xFCFB, UNICODE_NORM_QC_NO},
	{0xFCFC, UNICODE_NORM_QC_NO},
	{0xFCFD, UNICODE_NORM_QC_NO},
	{0xFCFE, UNICODE_NORM_QC_NO},
	{0xFCFF, UNICODE_NORM_QC_NO},
	{0xFD00, UNICODE_NORM_QC_NO},
	{0xFD01, UNICODE_NORM_QC_NO},
	{0xFD02, UNICODE_NORM_QC_NO},
	{0xFD03, UNICODE_NORM_QC_NO},
	{0xFD04, UNICODE_NORM_QC_NO},
	{0xFD05, UNICODE_NORM_QC_NO},
	{0xFD06, UNICODE_NORM_QC_NO},
	{0xFD07, UNICODE_NORM_QC_NO},
	{0xFD08, UNICODE_NORM_QC_NO},
	{0xFD09, UNICODE_NORM_QC_NO},
	{0xFD0A, UNICODE_NORM_QC_NO},
	{0xFD0B, UNICODE_NORM_QC_NO},
	{0xFD0C, UNICODE_NORM_QC_NO},
	{0xFD0D, UNICODE_NORM_QC_NO},
	{0xFD0E, UNICODE_NORM_QC_NO},
	{0xFD0F, UNICODE_NORM_QC_NO},
	{0xFD10, UNICODE_NORM_QC_NO},
	{0xFD11, UNICODE_NORM_QC_NO},
	{0xFD12, UNICODE_NORM_QC_NO},
	{0xFD13, UNICODE_NORM_QC_NO},
	{0xFD14, UNICODE_NORM_QC_NO},
	{0xFD15, UNICODE_NORM_QC_NO},
	{0xFD16, UNICODE_NORM_QC_NO},
	{0xFD17, UNICODE_NORM_QC_NO},
	{0xFD18, UNICODE_NORM_QC_NO},
	{0xFD19, UNICODE_NORM_QC_NO},
	{0xFD1A, UNICODE_NORM_QC_NO},
	{0xFD1B, UNICODE_NORM_QC_NO},
	{0xFD1C, UNICODE_NORM_QC_NO},
	{0xFD1D, UNICODE_NORM_QC_NO},
	{0xFD1E, UNICODE_NORM_QC_NO},
	{0xFD1F, UNICODE_NORM_QC_NO},
	{0xFD20, UNICODE_NORM_QC_NO},
	{0xFD21, UNICODE_NORM_QC_NO},
	{0xFD22, UNICODE_NORM_QC_NO},
	{0xFD23, UNICODE_NORM_QC_NO},
	{0xFD24, UNICODE_NORM_QC_NO},
	{0xFD25, UNICODE_NORM_QC_NO},
	{0xFD26, UNICODE_NORM_QC_NO},
	{0xFD27, UNICODE_NORM_QC_NO},
	{0xFD28, UNICODE_NORM_QC_NO},
	{0xFD29, UNICODE_NORM_QC_NO},
	{0xFD2A, UNICODE_NORM_QC_NO},
	{0xFD2B, UNICODE_NORM_QC_NO},
	{0xFD2C, UNICODE_NORM_QC_NO},
	{0xFD2D, UNICODE_NORM_QC_NO},
	{0xFD2E, UNICODE_NORM_QC_NO},
	{0xFD2F, UNICODE_NORM_QC_NO},
	{0xFD30, UNICODE_NORM_QC_NO},
	{0xFD31, UNICODE_NORM_QC_NO},
	{0xFD32, UNICODE_NORM_QC_NO},
	{0xFD33, UNICODE_NORM_QC_NO},
	{0xFD34, UNICODE_NORM_QC_NO},
	{0xFD35, UNICODE_NORM_QC_NO},
	{0xFD36, UNICODE_NORM_QC_NO},
	{0xFD37, UNICODE_NORM_QC_NO},
	{0xFD38, UNICODE_NORM_QC_NO},
	{0xFD39, UNICODE_NORM_QC_NO},
	{0xFD3A, UNICODE_NORM_QC_NO},
	{0xFD3B, UNICODE_NORM_QC_NO},
	{0xFD3C, UNICODE_NORM_QC_NO},
	{0xFD3D, UNICODE_NORM_QC_NO},
	{0xFD50, UNICODE_NORM_QC_NO},
	{0xFD51, UNICODE_NORM_QC_NO},
	{0xFD52, UNICODE_NORM_QC_NO},
	{0xFD53, UNICODE_NORM_QC_NO},
	{0xFD54, UNICODE_NORM_QC_NO},
	{0xFD55, UNICODE_NORM_QC_NO},
	{0xFD56, UNICODE_NORM_QC_NO},
	{0xFD57, UNICODE_NORM_QC_NO},
	{0xFD58, UNICODE_NORM_QC_NO},
	{0xFD59, UNICODE_NORM_QC_NO},
	{0xFD5A, UNICODE_NORM_QC_NO},
	{0xFD5B, UNICODE_NORM_QC_NO},
	{0xFD5C, UNICODE_NORM_QC_NO},
	{0xFD5D, UNICODE_NORM_QC_NO},
	{0xFD5E, UNICODE_NORM_QC_NO},
	{0xFD5F, UNICODE_NORM_QC_NO},
	{0xFD60, UNICODE_NORM_QC_NO},
	{0xFD61, UNICODE_NORM_QC_NO},
	{0xFD62, UNICODE_NORM_QC_NO},
	{0xFD63, UNICODE_NORM_QC_NO},
	{0xFD64, UNICODE_NORM_QC_NO},
	{0xFD65, UNICODE_NORM_QC_NO},
	{0xFD66, UNICODE_NORM_QC_NO},
	{0xFD67, UNICODE_NORM_QC_NO},
	{0xFD68, UNICODE_NORM_QC_NO},
	{0xFD69, UNICODE_NORM_QC_NO},
	{0xFD6A, UNICODE_NORM_QC_NO},
	{0xFD6B, UNICODE_NORM_QC_NO},
	{0xFD6C, UNICODE_NORM_QC_NO},
	{0xFD6D, UNICODE_NORM_QC_NO},
	{0xFD6E, UNICODE_NORM_QC_NO},
	{0xFD6F, UNICODE_NORM_QC_NO},
	{0xFD70, UNICODE_NORM_QC_NO},
	{0xFD71, UNICODE_NORM_QC_NO},
	{0xFD72, UNICODE_NORM_QC_NO},
	{0xFD73, UNICODE_NORM_QC_NO},
	{0xFD74, UNICODE_NORM_QC_NO},
	{0xFD75, UNICODE_NORM_QC_NO},
	{0xFD76, UNICODE_NORM_QC_NO},
	{0xFD77, UNICODE_NORM_QC_NO},
	{0xFD78, UNICODE_NORM_QC_NO},
	{0xFD79, UNICODE_NORM_QC_NO},
	{0xFD7A, UNICODE_NORM_QC_NO},
	{0xFD7B, UNICODE_NORM_QC_NO},
	{0xFD7C, UNICODE_NORM_QC_NO},
	{0xFD7D, UNICODE_NORM_QC_NO},
	{0xFD7E, UNICODE_NORM_QC_NO},
	{0xFD7F, UNICODE_NORM_QC_NO},
	{0xFD80, UNICODE_NORM_QC_NO},
	{0xFD81, UNICODE_NORM_QC_NO},
	{0xFD82, UNICODE_NORM_QC_NO},
	{0xFD83, UNICODE_NORM_QC_NO},
	{0xFD84, UNICODE_NORM_QC_NO},
	{0xFD85, UNICODE_NORM_QC_NO},
	{0xFD86, UNICODE_NORM_QC_NO},
	{0xFD87, UNICODE_NORM_QC_NO},
	{0xFD88, UNICODE_NORM_QC_NO},
	{0xFD89, UNICODE_NORM_QC_NO},
	{0xFD8A, UNICODE_NORM_QC_NO},
	{0xFD8B, UNICODE_NORM_QC_NO},
	{0xFD8C, UNICODE_NORM_QC_NO},
	{0xFD8D, UNICODE_NORM_QC_NO},
	{0xFD8E, UNICODE_NORM_QC_NO},
	{0xFD8F, UNICODE_NORM_QC_NO},
	{0xFD92, UNICODE_NORM_QC_NO},
	{0xFD93, UNICODE_NORM_QC_NO},
	{0xFD94, UNICODE_NORM_QC_NO},
	{0xFD95, UNICODE_NORM_QC_NO},
	{0xFD96, UNICODE_NORM_QC_NO},
	{0xFD97, UNICODE_NORM_QC_NO},
	{0xFD98, UNICODE_NORM_QC_NO},
	{0xFD99, UNICODE_NORM_QC_NO},
	{0xFD9A, UNICODE_NORM_QC_NO},
	{0xFD9B, UNICODE_NORM_QC_NO},
	{0xFD9C, UNICODE_NORM_QC_NO},
	{0xFD9D, UNICODE_NORM_QC_NO},
	{0xFD9E, UNICODE_NORM_QC_NO},
	{0xFD9F, UNICODE_NORM_QC_NO},
	{0xFDA0, UNICODE_NORM_QC_NO},
	{0xFDA1, UNICODE_NORM_QC_NO},
	{0xFDA2, UNICODE_NORM_QC_NO},
	{0xFDA3, UNICODE_NORM_QC_NO},
	{0xFDA4, UNICODE_NORM_QC_NO},
	{0xFDA5, UNICODE_NORM_QC_NO},
	{0xFDA6, UNICODE_NORM_QC_NO},
	{0xFDA7, UNICODE_NORM_QC_NO},
	{0xFDA8, UNICODE_NORM_QC_NO},
	{0xFDA9, UNICODE_NORM_QC_NO},
	{0xFDAA, UNICODE_NORM_QC_NO},
	{0xFDAB, UNICODE_NORM_QC_NO},
	{0xFDAC, UNICODE_NORM_QC_NO},
	{0xFDAD, UNICODE_NORM_QC_NO},
	{0xFDAE, UNICODE_NORM_QC_NO},
	{0xFDAF, UNICODE_NORM_QC_NO},
	{0xFDB0, UNICODE_NORM_QC_NO},
	{0xFDB1, UNICODE_NORM_QC_NO},
	{0xFDB2, UNICODE_NORM_QC_NO},
	{0xFDB3, UNICODE_NORM_QC_NO},
	{0xFDB4, UNICODE_NORM_QC_NO},
	{0xFDB5, UNICODE_NORM_QC_NO},
	{0xFDB6, UNICODE_NORM_QC_NO},
	{0xFDB7, UNICODE_NORM_QC_NO},
	{0xFDB8, UNICODE_NORM_QC_NO},
	{0xFDB9, UNICODE_NORM_QC_NO},
	{0xFDBA, UNICODE_NORM_QC_NO},
	{0xFDBB, UNICODE_NORM_QC_NO},
	{0xFDBC, UNICODE_NORM_QC_NO},
	{0xFDBD, UNICODE_NORM_QC_NO},
	{0xFDBE, UNICODE_NORM_QC_NO},
	{0xFDBF, UNICODE_NORM_QC_NO},
	{0xFDC0, UNICODE_NORM_QC_NO},
	{0xFDC1, UNICODE_NORM_QC_NO},
	{0xFDC2, UNICODE_NORM_QC_NO},
	{0xFDC3, UNICODE_NORM_QC_NO},
	{0xFDC4, UNICODE_NORM_QC_NO},
	{0xFDC5, UNICODE_NORM_QC_NO},
	{0xFDC6, UNICODE_NORM_QC_NO},
	{0xFDC7, UNICODE_NORM_QC_NO},
	{0xFDF0, UNICODE_NORM_QC_NO},
	{0xFDF1, UNICODE_NORM_QC_NO},
	{0xFDF2, UNICODE_NORM_QC_NO},
	{0xFDF3, UNICODE_NORM_QC_NO},
	{0xFDF4, UNICODE_NORM_QC_NO},
	{0xFDF5, UNICODE_NORM_QC_NO},
	{0xFDF6, UNICODE_NORM_QC_NO},
	{0xFDF7, UNICODE_NORM_QC_NO},
	{0xFDF8, UNICODE_NORM_QC_NO},
	{0xFDF9, UNICODE_NORM_QC_NO},
	{0xFDFA, UNICODE_NORM_QC_NO},
	{0xFDFB, UNICODE_NORM_QC_NO},
	{0xFDFC, UNICODE_NORM_QC_NO},
	{0xFE10, UNICODE_NORM_QC_NO},
	{0xFE11, UNICODE_NORM_QC_NO},
	{0xFE12, UNICODE_NORM_QC_NO},
	{0xFE13, UNICODE_NORM_QC_NO},
	{0xFE14, UNICODE_NORM_QC_NO},
	{0xFE15, UNICODE_NORM_QC_NO},
	{0xFE16, UNICODE_NORM_QC_NO},
	{0xFE17, UNICODE_NORM_QC_NO},
	{0xFE18, UNICODE_NORM_QC_NO},
	{0xFE19, UNICODE_NORM_QC_NO},
	{0xFE30, UNICODE_NORM_QC_NO},
	{0xFE31, UNICODE_NORM_QC_NO},
	{0xFE32, UNICODE_NORM_QC_NO},
	{0xFE33, UNICODE_NORM_QC_NO},
	{0xFE34, UNICODE_NORM_QC_NO},
	{0xFE35, UNICODE_NORM_QC_NO},
	{0xFE36, UNICODE_NORM_QC_NO},
	{0xFE37, UNICODE_NORM_QC_NO},
	{0xFE38, UNICODE_NORM_QC_NO},
	{0xFE39, UNICODE_NORM_QC_NO},
	{0xFE3A, UNICODE_NORM_QC_NO},
	{0xFE3B, UNICODE_NORM_QC_NO},
	{0xFE3C, UNICODE_NORM_QC_NO},
	{0xFE3D, UNICODE_NORM_QC_NO},
	{0xFE3E, UNICODE_NORM_QC_NO},
	{0xFE3F, UNICODE_NORM_QC_NO},
	{0xFE40, UNICODE_NORM_QC_NO},
	{0xFE41, UNICODE_NORM_QC_NO},
	{0xFE42, UNICODE_NORM_QC_NO},
	{0xFE43, UNICODE_NORM_QC_NO},
	{0xFE44, UNICODE_NORM_QC_NO},
	{0xFE47, UNICODE_NORM_QC_NO},
	{0xFE48, UNICODE_NORM_QC_NO},
	{0xFE49, UNICODE_NORM_QC_NO},
	{0xFE4A, UNICODE_NORM_QC_NO},
	{0xFE4B, UNICODE_NORM_QC_NO},
	{0xFE4C, UNICODE_NORM_QC_NO},
	{0xFE4D, UNICODE_NORM_QC_NO},
	{0xFE4E, UNICODE_NORM_QC_NO},
	{0xFE4F, UNICODE_NORM_QC_NO},
	{0xFE50, UNICODE_NORM_QC_NO},
	{0xFE51, UNICODE_NORM_QC_NO},
	{0xFE52, UNICODE_NORM_QC_NO},
	{0xFE54, UNICODE_NORM_QC_NO},
	{0xFE55, UNICODE_NORM_QC_NO},
	{0xFE56, UNICODE_NORM_QC_NO},
	{0xFE57, UNICODE_NORM_QC_NO},
	{0xFE58, UNICODE_NORM_QC_NO},
	{0xFE59, UNICODE_NORM_QC_NO},
	{0xFE5A, UNICODE_NORM_QC_NO},
	{0xFE5B, UNICODE_NORM_QC_NO},
	{0xFE5C, UNICODE_NORM_QC_NO},
	{0xFE5D, UNICODE_NORM_QC_NO},
	{0xFE5E, UNICODE_NORM_QC_NO},
	{0xFE5F, UNICODE_NORM_QC_NO},
	{0xFE60, UNICODE_NORM_QC_NO},
	{0xFE61, UNICODE_NORM_QC_NO},
	{0xFE62, UNICODE_NORM_QC_NO},
	{0xFE63, UNICODE_NORM_QC_NO},
	{0xFE64, UNICODE_NORM_QC_NO},
	{0xFE65, UNICODE_NORM_QC_NO},
	{0xFE66, UNICODE_NORM_QC_NO},
	{0xFE68, UNICODE_NORM_QC_NO},
	{0xFE69, UNICODE_NORM_QC_NO},
	{0xFE6A, UNICODE_NORM_QC_NO},
	{0xFE6B, UNICODE_NORM_QC_NO},
	{0xFE70, UNICODE_NORM_QC_NO},
	{0xFE71, UNICODE_NORM_QC_NO},
	{0xFE72, UNICODE_NORM_QC_NO},
	{0xFE74, UNICODE_NORM_QC_NO},
	{0xFE76, UNICODE_NORM_QC_NO},
	{0xFE77, UNICODE_NORM_QC_NO},
	{0xFE78, UNICODE_NORM_QC_NO},
	{0xFE79, UNICODE_NORM_QC_NO},
	{0xFE7A, UNICODE_NORM_QC_NO},
	{0xFE7B, UNICODE_NORM_QC_NO},
	{0xFE7C, UNICODE_NORM_QC_NO},
	{0xFE7D, UNICODE_NORM_QC_NO},
	{0xFE7E, UNICODE_NORM_QC_NO},
	{0xFE7F, UNICODE_NORM_QC_NO},
	{0xFE80, UNICODE_NORM_QC_NO},
	{0xFE81, UNICODE_NORM_QC_NO},
	{0xFE82, UNICODE_NORM_QC_NO},
	{0xFE83, UNICODE_NORM_QC_NO},
	{0xFE84, UNICODE_NORM_QC_NO},
	{0xFE85, UNICODE_NORM_QC_NO},
	{0xFE86, UNICODE_NORM_QC_NO},
	{0xFE87, UNICODE_NORM_QC_NO},
	{0xFE88, UNICODE_NORM_QC_NO},
	{0xFE89, UNICODE_NORM_QC_NO},
	{0xFE8A, UNICODE_NORM_QC_NO},
	{0xFE8B, UNICODE_NORM_QC_NO},
	{0xFE8C, UNICODE_NORM_QC_NO},
	{0xFE8D, UNICODE_NORM_QC_NO},
	{0xFE8E, UNICODE_NORM_QC_NO},
	{0xFE8F, UNICODE_NORM_QC_NO},
	{0xFE90, UNICODE_NORM_QC_NO},
	{0xFE91, UNICODE_NORM_QC_NO},
	{0xFE92, UNICODE_NORM_QC_NO},
	{0xFE93, UNICODE_NORM_QC_NO},
	{0xFE94, UNICODE_NORM_QC_NO},
	{0xFE95, UNICODE_NORM_QC_NO},
	{0xFE96, UNICODE_NORM_QC_NO},
	{0xFE97, UNICODE_NORM_QC_NO},
	{0xFE98, UNICODE_NORM_QC_NO},
	{0xFE99, UNICODE_NORM_QC_NO},
	{0xFE9A, UNICODE_NORM_QC_NO},
	{0xFE9B, UNICODE_NORM_QC_NO},
	{0xFE9C, UNICODE_NORM_QC_NO},
	{0xFE9D, UNICODE_NORM_QC_NO},
	{0xFE9E, UNICODE_NORM_QC_NO},
	{0xFE9F, UNICODE_NORM_QC_NO},
	{0xFEA0, UNICODE_NORM_QC_NO},
	{0xFEA1, UNICODE_NORM_QC_NO},
	{0xFEA2, UNICODE_NORM_QC_NO},
	{0xFEA3, UNICODE_NORM_QC_NO},
	{0xFEA4, UNICODE_NORM_QC_NO},
	{0xFEA5, UNICODE_NORM_QC_NO},
	{0xFEA6, UNICODE_NORM_QC_NO},
	{0xFEA7, UNICODE_NORM_QC_NO},
	{0xFEA8, UNICODE_NORM_QC_NO},
	{0xFEA9, UNICODE_NORM_QC_NO},
	{0xFEAA, UNICODE_NORM_QC_NO},
	{0xFEAB, UNICODE_NORM_QC_NO},
	{0xFEAC, UNICODE_NORM_QC_NO},
	{0xFEAD, UNICODE_NORM_QC_NO},
	{0xFEAE, UNICODE_NORM_QC_NO},
	{0xFEAF, UNICODE_NORM_QC_NO},
	{0xFEB0, UNICODE_NORM_QC_NO},
	{0xFEB1, UNICODE_NORM_QC_NO},
	{0xFEB2, UNICODE_NORM_QC_NO},
	{0xFEB3, UNICODE_NORM_QC_NO},
	{0xFEB4, UNICODE_NORM_QC_NO},
	{0xFEB5, UNICODE_NORM_QC_NO},
	{0xFEB6, UNICODE_NORM_QC_NO},
	{0xFEB7, UNICODE_NORM_QC_NO},
	{0xFEB8, UNICODE_NORM_QC_NO},
	{0xFEB9, UNICODE_NORM_QC_NO},
	{0xFEBA, UNICODE_NORM_QC_NO},
	{0xFEBB, UNICODE_NORM_QC_NO},
	{0xFEBC, UNICODE_NORM_QC_NO},
	{0xFEBD, UNICODE_NORM_QC_NO},
	{0xFEBE, UNICODE_NORM_QC_NO},
	{0xFEBF, UNICODE_NORM_QC_NO},
	{0xFEC0, UNICODE_NORM_QC_NO},
	{0xFEC1, UNICODE_NORM_QC_NO},
	{0xFEC2, UNICODE_NORM_QC_NO},
	{0xFEC3, UNICODE_NORM_QC_NO},
	{0xFEC4, UNICODE_NORM_QC_NO},
	{0xFEC5, UNICODE_NORM_QC_NO},
	{0xFEC6, UNICODE_NORM_QC_NO},
	{0xFEC7, UNICODE_NORM_QC_NO},
	{0xFEC8, UNICODE_NORM_QC_NO},
	{0xFEC9, UNICODE_NORM_QC_NO},
	{0xFECA, UNICODE_NORM_QC_NO},
	{0xFECB, UNICODE_NORM_QC_NO},
	{0xFECC, UNICODE_NORM_QC_NO},
	{0xFECD, UNICODE_NORM_QC_NO},
	{0xFECE, UNICODE_NORM_QC_NO},
	{0xFECF, UNICODE_NORM_QC_NO},
	{0xFED0, UNICODE_NORM_QC_NO},
	{0xFED1, UNICODE_NORM_QC_NO},
	{0xFED2, UNICODE_NORM_QC_NO},
	{0xFED3, UNICODE_NORM_QC_NO},
	{0xFED4, UNICODE_NORM_QC_NO},
	{0xFED5, UNICODE_NORM_QC_NO},
	{0xFED6, UNICODE_NORM_QC_NO},
	{0xFED7, UNICODE_NORM_QC_NO},
	{0xFED8, UNICODE_NORM_QC_NO},
	{0xFED9, UNICODE_NORM_QC_NO},
	{0xFEDA, UNICODE_NORM_QC_NO},
	{0xFEDB, UNICODE_NORM_QC_NO},
	{0xFEDC, UNICODE_NORM_QC_NO},
	{0xFEDD, UNICODE_NORM_QC_NO},
	{0xFEDE, UNICODE_NORM_QC_NO},
	{0xFEDF, UNICODE_NORM_QC_NO},
	{0xFEE0, UNICODE_NORM_QC_NO},
	{0xFEE1, UNICODE_NORM_QC_NO},
	{0xFEE2, UNICODE_NORM_QC_NO},
	{0xFEE3, UNICODE_NORM_QC_NO},
	{0xFEE4, UNICODE_NORM_QC_NO},
	{0xFEE5, UNICODE_NORM_QC_NO},
	{0xFEE6, UNICODE_NORM_QC_NO},
	{0xFEE7, UNICODE_NORM_QC_NO},
	{0xFEE8, UNICODE_NORM_QC_NO},
	{0xFEE9, UNICODE_NORM_QC_NO},
	{0xFEEA, UNICODE_NORM_QC_NO},
	{0xFEEB, UNICODE_NORM_QC_NO},
	{0xFEEC, UNICODE_NORM_QC_NO},
	{0xFEED, UNICODE_NORM_QC_NO},
	{0xFEEE, UNICODE_NORM_QC_NO},
	{0xFEEF, UNICODE_NORM_QC_NO},
	{0xFEF0, UNICODE_NORM_QC_NO},
	{0xFEF1, UNICODE_NORM_QC_NO},
	{0xFEF2, UNICODE_NORM_QC_NO},
	{0xFEF3, UNICODE_NORM_QC_NO},
	{0xFEF4, UNICODE_NORM_QC_NO},
	{0xFEF5, UNICODE_NORM_QC_NO},
	{0xFEF6, UNICODE_NORM_QC_NO},
	{0xFEF7, UNICODE_NORM_QC_NO},
	{0xFEF8, UNICODE_NORM_QC_NO},
	{0xFEF9, UNICODE_NORM_QC_NO},
	{0xFEFA, UNICODE_NORM_QC_NO},
	{0xFEFB, UNICODE_NORM_QC_NO},
	{0xFEFC, UNICODE_NORM_QC_NO},
	{0xFF01, UNICODE_NORM_QC_NO},
	{0xFF02, UNICODE_NORM_QC_NO},
	{0xFF03, UNICODE_NORM_QC_NO},
	{0xFF04, UNICODE_NORM_QC_NO},
	{0xFF05, UNICODE_NORM_QC_NO},
	{0xFF06, UNICODE_NORM_QC_NO},
	{0xFF07, UNICODE_NORM_QC_NO},
	{0xFF08, UNICODE_NORM_QC_NO},
	{0xFF09, UNICODE_NORM_QC_NO},
	{0xFF0A, UNICODE_NORM_QC_NO},
	{0xFF0B, UNICODE_NORM_QC_NO},
	{0xFF0C, UNICODE_NORM_QC_NO},
	{0xFF0D, UNICODE_NORM_QC_NO},
	{0xFF0E, UNICODE_NORM_QC_NO},
	{0xFF0F, UNICODE_NORM_QC_NO},
	{0xFF10, UNICODE_NORM_QC_NO},
	{0xFF11, UNICODE_NORM_QC_NO},
	{0xFF12, UNICODE_NORM_QC_NO},
	{0xFF13, UNICODE_NORM_QC_NO},
	{0xFF14, UNICODE_NORM_QC_NO},
	{0xFF15, UNICODE_NORM_QC_NO},
	{0xFF16, UNICODE_NORM_QC_NO},
	{0xFF17, UNICODE_NORM_QC_NO},
	{0xFF18, UNICODE_NORM_QC_NO},
	{0xFF19, UNICODE_NORM_QC_NO},
	{0xFF1A, UNICODE_NORM_QC_NO},
	{0xFF1B, UNICODE_NORM_QC_NO},
	{0xFF1C, UNICODE_NORM_QC_NO},
	{0xFF1D, UNICODE_NORM_QC_NO},
	{0xFF1E, UNICODE_NORM_QC_NO},
	{0xFF1F, UNICODE_NORM_QC_NO},
	{0xFF20, UNICODE_NORM_QC_NO},
	{0xFF21, UNICODE_NORM_QC_NO},
	{0xFF22, UNICODE_NORM_QC_NO},
	{0xFF23, UNICODE_NORM_QC_NO},
	{0xFF24, UNICODE_NORM_QC_NO},
	{0xFF25, UNICODE_NORM_QC_NO},
	{0xFF26, UNICODE_NORM_QC_NO},
	{0xFF27, UNICODE_NORM_QC_NO},
	{0xFF28, UNICODE_NORM_QC_NO},
	{0xFF29, UNICODE_NORM_QC_NO},
	{0xFF2A, UNICODE_NORM_QC_NO},
	{0xFF2B, UNICODE_NORM_QC_NO},
	{0xFF2C, UNICODE_NORM_QC_NO},
	{0xFF2D, UNICODE_NORM_QC_NO},
	{0xFF2E, UNICODE_NORM_QC_NO},
	{0xFF2F, UNICODE_NORM_QC_NO},
	{0xFF30, UNICODE_NORM_QC_NO},
	{0xFF31, UNICODE_NORM_QC_NO},
	{0xFF32, UNICODE_NORM_QC_NO},
	{0xFF33, UNICODE_NORM_QC_NO},
	{0xFF34, UNICODE_NORM_QC_NO},
	{0xFF35, UNICODE_NORM_QC_NO},
	{0xFF36, UNICODE_NORM_QC_NO},
	{0xFF37, UNICODE_NORM_QC_NO},
	{0xFF38, UNICODE_NORM_QC_NO},
	{0xFF39, UNICODE_NORM_QC_NO},
	{0xFF3A, UNICODE_NORM_QC_NO},
	{0xFF3B, UNICODE_NORM_QC_NO},
	{0xFF3C, UNICODE_NORM_QC_NO},
	{0xFF3D, UNICODE_NORM_QC_NO},
	{0xFF3E, UNICODE_NORM_QC_NO},
	{0xFF3F, UNICODE_NORM_QC_NO},
	{0xFF40, UNICODE_NORM_QC_NO},
	{0xFF41, UNICODE_NORM_QC_NO},
	{0xFF42, UNICODE_NORM_QC_NO},
	{0xFF43, UNICODE_NORM_QC_NO},
	{0xFF44, UNICODE_NORM_QC_NO},
	{0xFF45, UNICODE_NORM_QC_NO},
	{0xFF46, UNICODE_NORM_QC_NO},
	{0xFF47, UNICODE_NORM_QC_NO},
	{0xFF48, UNICODE_NORM_QC_NO},
	{0xFF49, UNICODE_NORM_QC_NO},
	{0xFF4A, UNICODE_NORM_QC_NO},
	{0xFF4B, UNICODE_NORM_QC_NO},
	{0xFF4C, UNICODE_NORM_QC_NO},
	{0xFF4D, UNICODE_NORM_QC_NO},
	{0xFF4E, UNICODE_NORM_QC_NO},
	{0xFF4F, UNICODE_NORM_QC_NO},
	{0xFF50, UNICODE_NORM_QC_NO},
	{0xFF51, UNICODE_NORM_QC_NO},
	{0xFF52, UNICODE_NORM_QC_NO},
	{0xFF53, UNICODE_NORM_QC_NO},
	{0xFF54, UNICODE_NORM_QC_NO},
	{0xFF55, UNICODE_NORM_QC_NO},
	{0xFF56, UNICODE_NORM_QC_NO},
	{0xFF57, UNICODE_NORM_QC_NO},
	{0xFF58, UNICODE_NORM_QC_NO},
	{0xFF59, UNICODE_NORM_QC_NO},
	{0xFF5A, UNICODE_NORM_QC_NO},
	{0xFF5B, UNICODE_NORM_QC_NO},
	{0xFF5C, UNICODE_NORM_QC_NO},
	{0xFF5D, UNICODE_NORM_QC_NO},
	{0xFF5E, UNICODE_NORM_QC_NO},
	{0xFF5F, UNICODE_NORM_QC_NO},
	{0xFF60, UNICODE_NORM_QC_NO},
	{0xFF61, UNICODE_NORM_QC_NO},
	{0xFF62, UNICODE_NORM_QC_NO},
	{0xFF63, UNICODE_NORM_QC_NO},
	{0xFF64, UNICODE_NORM_QC_NO},
	{0xFF65, UNICODE_NORM_QC_NO},
	{0xFF66, UNICODE_NORM_QC_NO},
	{0xFF67, UNICODE_NORM_QC_NO},
	{0xFF68, UNICODE_NORM_QC_NO},
	{0xFF69, UNICODE_NORM_QC_NO},
	{0xFF6A, UNICODE_NORM_QC_NO},
	{0xFF6B, UNICODE_NORM_QC_NO},
	{0xFF6C, UNICODE_NORM_QC_NO},
	{0xFF6D, UNICODE_NORM_QC_NO},
	{0xFF6E, UNICODE_NORM_QC_NO},
	{0xFF6F, UNICODE_NORM_QC_NO},
	{0xFF70, UNICODE_NORM_QC_NO},
	{0xFF71, UNICODE_NORM_QC_NO},
	{0xFF72, UNICODE_NORM_QC_NO},
	{0xFF73, UNICODE_NORM_QC_NO},
	{0xFF74, UNICODE_NORM_QC_NO},
	{0xFF75, UNICODE_NORM_QC_NO},
	{0xFF76, UNICODE_NORM_QC_NO},
	{0xFF77, UNICODE_NORM_QC_NO},
	{0xFF78, UNICODE_NORM_QC_NO},
	{0xFF79, UNICODE_NORM_QC_NO},
	{0xFF7A, UNICODE_NORM_QC_NO},
	{0xFF7B, UNICODE_NORM_QC_NO},
	{0xFF7C, UNICODE_NORM_QC_NO},
	{0xFF7D, UNICODE_NORM_QC_NO},
	{0xFF7E, UNICODE_NORM_QC_NO},
	{0xFF7F, UNICODE_NORM_QC_NO},
	{0xFF80, UNICODE_NORM_QC_NO},
	{0xFF81, UNICODE_NORM_QC_NO},
	{0xFF82, UNICODE_NORM_QC_NO},
	{0xFF83, UNICODE_NORM_QC_NO},
	{0xFF84, UNICODE_NORM_QC_NO},
	{0xFF85, UNICODE_NORM_QC_NO},
	{0xFF86, UNICODE_NORM_QC_NO},
	{0xFF87, UNICODE_NORM_QC_NO},
	{0xFF88, UNICODE_NORM_QC_NO},
	{0xFF89, UNICODE_NORM_QC_NO},
	{0xFF8A, UNICODE_NORM_QC_NO},
	{0xFF8B, UNICODE_NORM_QC_NO},
	{0xFF8C, UNICODE_NORM_QC_NO},
	{0xFF8D, UNICODE_NORM_QC_NO},
	{0xFF8E, UNICODE_NORM_QC_NO},
	{0xFF8F, UNICODE_NORM_QC_NO},
	{0xFF90, UNICODE_NORM_QC_NO},
	{0xFF91, UNICODE_NORM_QC_NO},
	{0xFF92, UNICODE_NORM_QC_NO},
	{0xFF93, UNICODE_NORM_QC_NO},
	{0xFF94, UNICODE_NORM_QC_NO},
	{0xFF95, UNICODE_NORM_QC_NO},
	{0xFF96, UNICODE_NORM_QC_NO},
	{0xFF97, UNICODE_NORM_QC_NO},
	{0xFF98, UNICODE_NORM_QC_NO},
	{0xFF99, UNICODE_NORM_QC_NO},
	{0xFF9A, UNICODE_NORM_QC_NO},
	{0xFF9B, UNICODE_NORM_QC_NO},
	{0xFF9C, UNICODE_NORM_QC_NO},
	{0xFF9D, UNICODE_NORM_QC_NO},
	{0xFF9E, UNICODE_NORM_QC_NO},
	{0xFF9F, UNICODE_NORM_QC_NO},
	{0xFFA0, UNICODE_NORM_QC_NO},
	{0xFFA1, UNICODE_NORM_QC_NO},
	{0xFFA2, UNICODE_NORM_QC_NO},
	{0xFFA3, UNICODE_NORM_QC_NO},
	{0xFFA4, UNICODE_NORM_QC_NO},
	{0xFFA5, UNICODE_NORM_QC_NO},
	{0xFFA6, UNICODE_NORM_QC_NO},
	{0xFFA7, UNICODE_NORM_QC_NO},
	{0xFFA8, UNICODE_NORM_QC_NO},
	{0xFFA9, UNICODE_NORM_QC_NO},
	{0xFFAA, UNICODE_NORM_QC_NO},
	{0xFFAB, UNICODE_NORM_QC_NO},
	{0xFFAC, UNICODE_NORM_QC_NO},
	{0xFFAD, UNICODE_NORM_QC_NO},
	{0xFFAE, UNICODE_NORM_QC_NO},
	{0xFFAF, UNICODE_NORM_QC_NO},
	{0xFFB0, UNICODE_NORM_QC_NO},
	{0xFFB1, UNICODE_NORM_QC_NO},
	{0xFFB2, UNICODE_NORM_QC_NO},
	{0xFFB3, UNICODE_NORM_QC_NO},
	{0xFFB4, UNICODE_NORM_QC_NO},
	{0xFFB5, UNICODE_NORM_QC_NO},
	{0xFFB6, UNICODE_NORM_QC_NO},
	{0xFFB7, UNICODE_NORM_QC_NO},
	{0xFFB8, UNICODE_NORM_QC_NO},
	{0xFFB9, UNICODE_NORM_QC_NO},
	{0xFFBA, UNICODE_NORM_QC_NO},
	{0xFFBB, UNICODE_NORM_QC_NO},
	{0xFFBC, UNICODE_NORM_QC_NO},
	{0xFFBD, UNICODE_NORM_QC_NO},
	{0xFFBE, UNICODE_NORM_QC_NO},
	{0xFFC2, UNICODE_NORM_QC_NO},
	{0xFFC3, UNICODE_NORM_QC_NO},
	{0xFFC4, UNICODE_NORM_QC_NO},
	{0xFFC5, UNICODE_NORM_QC_NO},
	{0xFFC6, UNICODE_NORM_QC_NO},
	{0xFFC7, UNICODE_NORM_QC_NO},
	{0xFFCA, UNICODE_NORM_QC_NO},
	{0xFFCB, UNICODE_NORM_QC_NO},
	{0xFFCC, UNICODE_NORM_QC_NO},
	{0xFFCD, UNICODE_NORM_QC_NO},
	{0xFFCE, UNICODE_NORM_QC_NO},
	{0xFFCF, UNICODE_NORM_QC_NO},
	{0xFFD2, UNICODE_NORM_QC_NO},
	{0xFFD3, UNICODE_NORM_QC_NO},
	{0xFFD4, UNICODE_NORM_QC_NO},
	{0xFFD5, UNICODE_NORM_QC_NO},
	{0xFFD6, UNICODE_NORM_QC_NO},
	{0xFFD7, UNICODE_NORM_QC_NO},
	{0xFFDA, UNICODE_NORM_QC_NO},
	{0xFFDB, UNICODE_NORM_QC_NO},
	{0xFFDC, UNICODE_NORM_QC_NO},
	{0xFFE0, UNICODE_NORM_QC_NO},
	{0xFFE1, UNICODE_NORM_QC_NO},
	{0xFFE2, UNICODE_NORM_QC_NO},
	{0xFFE3, UNICODE_NORM_QC_NO},
	{0xFFE4, UNICODE_NORM_QC_NO},
	{0xFFE5, UNICODE_NORM_QC_NO},
	{0xFFE6, UNICODE_NORM_QC_NO},
	{0xFFE8, UNICODE_NORM_QC_NO},
	{0xFFE9, UNICODE_NORM_QC_NO},
	{0xFFEA, UNICODE_NORM_QC_NO},
	{0xFFEB, UNICODE_NORM_QC_NO},
	{0xFFEC, UNICODE_NORM_QC_NO},
	{0xFFED, UNICODE_NORM_QC_NO},
	{0xFFEE, UNICODE_NORM_QC_NO},
	{0x10781, UNICODE_NORM_QC_NO},
	{0x10782, UNICODE_NORM_QC_NO},
	{0x10783, UNICODE_NORM_QC_NO},
	{0x10784, UNICODE_NORM_QC_NO},
	{0x10785, UNICODE_NORM_QC_NO},
	{0x10787, UNICODE_NORM_QC_NO},
	{0x10788, UNICODE_NORM_QC_NO},
	{0x10789, UNICODE_NORM_QC_NO},
	{0x1078A, UNICODE_NORM_QC_NO},
	{0x1078B, UNICODE_NORM_QC_NO},
	{0x1078C, UNICODE_NORM_QC_NO},
	{0x1078D, UNICODE_NORM_QC_NO},
	{0x1078E, UNICODE_NORM_QC_NO},
	{0x1078F, UNICODE_NORM_QC_NO},
	{0x10790, UNICODE_NORM_QC_NO},
	{0x10791, UNICODE_NORM_QC_NO},
	{0x10792, UNICODE_NORM_QC_NO},
	{0x10793, UNICODE_NORM_QC_NO},
	{0x10794, UNICODE_NORM_QC_NO},
	{0x10795, UNICODE_NORM_QC_NO},
	{0x10796, UNICODE_NORM_QC_NO},
	{0x10797, UNICODE_NORM_QC_NO},
	{0x10798, UNICODE_NORM_QC_NO},
	{0x10799, UNICODE_NORM_QC_NO},
	{0x1079A, UNICODE_NORM_QC_NO},
	{0x1079B, UNICODE_NORM_QC_NO},
	{0x1079C, UNICODE_NORM_QC_NO},
	{0x1079D, UNICODE_NORM_QC_NO},
	{0x1079E, UNICODE_NORM_QC_NO},
	{0x1079F, UNICODE_NORM_QC_NO},
	{0x107A0, UNICODE_NORM_QC_NO},
	{0x107A1, UNICODE_NORM_QC_NO},
	{0x107A2, UNICODE_NORM_QC_NO},
	{0x107A3, UNICODE_NORM_QC_NO},
	{0x107A4, UNICODE_NORM_QC_NO},
	{0x107A5, UNICODE_NORM_QC_NO},
	{0x107A6, UNICODE_NORM_QC_NO},
	{0x107A7, UNICODE_NORM_QC_NO},
	{0x107A8, UNICODE_NORM_QC_NO},
	{0x107A9, UNICODE_NORM_QC_NO},
	{0x107AA, UNICODE_NORM_QC_NO},
	{0x107AB, UNICODE_NORM_QC_NO},
	{0x107AC, UNICODE_NORM_QC_NO},
	{0x107AD, UNICODE_NORM_QC_NO},
	{0x107AE, UNICODE_NORM_QC_NO},
	{0x107AF, UNICODE_NORM_QC_NO},
	{0x107B0, UNICODE_NORM_QC_NO},
	{0x107B2, UNICODE_NORM_QC_NO},
	{0x107B3, UNICODE_NORM_QC_NO},
	{0x107B4, UNICODE_NORM_QC_NO},
	{0x107B5, UNICODE_NORM_QC_NO},
	{0x107B6, UNICODE_NORM_QC_NO},
	{0x107B7, UNICODE_NORM_QC_NO},
	{0x107B8, UNICODE_NORM_QC_NO},
	{0x107B9, UNICODE_NORM_QC_NO},
	{0x107BA, UNICODE_NORM_QC_NO},
	{0x110BA, UNICODE_NORM_QC_MAYBE},
	{0x11127, UNICODE_NORM_QC_MAYBE},
	{0x1133E, UNICODE_NORM_QC_MAYBE},
	{0x11357, UNICODE_NORM_QC_MAYBE},
	{0x114B0, UNICODE_NORM_QC_MAYBE},
	{0x114BA, UNICODE_NORM_QC_MAYBE},
	{0x114BD, UNICODE_NORM_QC_MAYBE},
	{0x115AF, UNICODE_NORM_QC_MAYBE},
	{0x11930, UNICODE_NORM_QC_MAYBE},
	{0x1D15E, UNICODE_NORM_QC_NO},
	{0x1D15F, UNICODE_NORM_QC_NO},
	{0x1D160, UNICODE_NORM_QC_NO},
	{0x1D161, UNICODE_NORM_QC_NO},
	{0x1D162, UNICODE_NORM_QC_NO},
	{0x1D163, UNICODE_NORM_QC_NO},
	{0x1D164, UNICODE_NORM_QC_NO},
	{0x1D1BB, UNICODE_NORM_QC_NO},
	{0x1D1BC, UNICODE_NORM_QC_NO},
	{0x1D1BD, UNICODE_NORM_QC_NO},
	{0x1D1BE, UNICODE_NORM_QC_NO},
	{0x1D1BF, UNICODE_NORM_QC_NO},
	{0x1D1C0, UNICODE_NORM_QC_NO},
	{0x1D400, UNICODE_NORM_QC_NO},
	{0x1D401, UNICODE_NORM_QC_NO},
	{0x1D402, UNICODE_NORM_QC_NO},
	{0x1D403, UNICODE_NORM_QC_NO},
	{0x1D404, UNICODE_NORM_QC_NO},
	{0x1D405, UNICODE_NORM_QC_NO},
	{0x1D406, UNICODE_NORM_QC_NO},
	{0x1D407, UNICODE_NORM_QC_NO},
	{0x1D408, UNICODE_NORM_QC_NO},
	{0x1D409, UNICODE_NORM_QC_NO},
	{0x1D40A, UNICODE_NORM_QC_NO},
	{0x1D40B, UNICODE_NORM_QC_NO},
	{0x1D40C, UNICODE_NORM_QC_NO},
	{0x1D40D, UNICODE_NORM_QC_NO},
	{0x1D40E, UNICODE_NORM_QC_NO},
	{0x1D40F, UNICODE_NORM_QC_NO},
	{0x1D410, UNICODE_NORM_QC_NO},
	{0x1D411, UNICODE_NORM_QC_NO},
	{0x1D412, UNICODE_NORM_QC_NO},
	{0x1D413, UNICODE_NORM_QC_NO},
	{0x1D414, UNICODE_NORM_QC_NO},
	{0x1D415, UNICODE_NORM_QC_NO},
	{0x1D416, UNICODE_NORM_QC_NO},
	{0x1D417, UNICODE_NORM_QC_NO},
	{0x1D418, UNICODE_NORM_QC_NO},
	{0x1D419, UNICODE_NORM_QC_NO},
	{0x1D41A, UNICODE_NORM_QC_NO},
	{0x1D41B, UNICODE_NORM_QC_NO},
	{0x1D41C, UNICODE_NORM_QC_NO},
	{0x1D41D, UNICODE_NORM_QC_NO},
	{0x1D41E, UNICODE_NORM_QC_NO},
	{0x1D41F, UNICODE_NORM_QC_NO},
	{0x1D420, UNICODE_NORM_QC_NO},
	{0x1D421, UNICODE_NORM_QC_NO},
	{0x1D422, UNICODE_NORM_QC_NO},
	{0x1D423, UNICODE_NORM_QC_NO},
	{0x1D424, UNICODE_NORM_QC_NO},
	{0x1D425, UNICODE_NORM_QC_NO},
	{0x1D426, UNICODE_NORM_QC_NO},
	{0x1D427, UNICODE_NORM_QC_NO},
	{0x1D428, UNICODE_NORM_QC_NO},
	{0x1D429, UNICODE_NORM_QC_NO},
	{0x1D42A, UNICODE_NORM_QC_NO},
	{0x1D42B, UNICODE_NORM_QC_NO},
	{0x1D42C, UNICODE_NORM_QC_NO},
	{0x1D42D, UNICODE_NORM_QC_NO},
	{0x1D42E, UNICODE_NORM_QC_NO},
	{0x1D42F, UNICODE_NORM_QC_NO},
	{0x1D430, UNICODE_NORM_QC_NO},
	{0x1D431, UNICODE_NORM_QC_NO},
	{0x1D432, UNICODE_NORM_QC_NO},
	{0x1D433, UNICODE_NORM_QC_NO},
	{0x1D434, UNICODE_NORM_QC_NO},
	{0x1D435, UNICODE_NORM_QC_NO},
	{0x1D436, UNICODE_NORM_QC_NO},
	{0x1D437, UNICODE_NORM_QC_NO},
	{0x1D438, UNICODE_NORM_QC_NO},
	{0x1D439, UNICODE_NORM_QC_NO},
	{0x1D43A, UNICODE_NORM_QC_NO},
	{0x1D43B, UNICODE_NORM_QC_NO},
	{0x1D43C, UNICODE_NORM_QC_NO},
	{0x1D43D, UNICODE_NORM_QC_NO},
	{0x1D43E, UNICODE_NORM_QC_NO},
	{0x1D43F, UNICODE_NORM_QC_NO},
	{0x1D440, UNICODE_NORM_QC_NO},
	{0x1D441, UNICODE_NORM_QC_NO},
	{0x1D442, UNICODE_NORM_QC_NO},
	{0x1D443, UNICODE_NORM_QC_NO},
	{0x1D444, UNICODE_NORM_QC_NO},
	{0x1D445, UNICODE_NORM_QC_NO},
	{0x1D446, UNICODE_NORM_QC_NO},
	{0x1D447, UNICODE_NORM_QC_NO},
	{0x1D448, UNICODE_NORM_QC_NO},
	{0x1D449, UNICODE_NORM_QC_NO},
	{0x1D44A, UNICODE_NORM_QC_NO},
	{0x1D44B, UNICODE_NORM_QC_NO},
	{0x1D44C, UNICODE_NORM_QC_NO},
	{0x1D44D, UNICODE_NORM_QC_NO},
	{0x1D44E, UNICODE_NORM_QC_NO},
	{0x1D44F, UNICODE_NORM_QC_NO},
	{0x1D450, UNICODE_NORM_QC_NO},
	{0x1D451, UNICODE_NORM_QC_NO},
	{0x1D452, UNICODE_NORM_QC_NO},
	{0x1D453, UNICODE_NORM_QC_NO},
	{0x1D454, UNICODE_NORM_QC_NO},
	{0x1D456, UNICODE_NORM_QC_NO},
	{0x1D457, UNICODE_NORM_QC_NO},
	{0x1D458, UNICODE_NORM_QC_NO},
	{0x1D459, UNICODE_NORM_QC_NO},
	{0x1D45A, UNICODE_NORM_QC_NO},
	{0x1D45B, UNICODE_NORM_QC_NO},
	{0x1D45C, UNICODE_NORM_QC_NO},
	{0x1D45D, UNICODE_NORM_QC_NO},
	{0x1D45E, UNICODE_NORM_QC_NO},
	{0x1D45F, UNICODE_NORM_QC_NO},
	{0x1D460, UNICODE_NORM_QC_NO},
	{0x1D461, UNICODE_NORM_QC_NO},
	{0x1D462, UNICODE_NORM_QC_NO},
	{0x1D463, UNICODE_NORM_QC_NO},
	{0x1D464, UNICODE_NORM_QC_NO},
	{0x1D465, UNICODE_NORM_QC_NO},
	{0x1D466, UNICODE_NORM_QC_NO},
	{0x1D467, UNICODE_NORM_QC_NO},
	{0x1D468, UNICODE_NORM_QC_NO},
	{0x1D469, UNICODE_NORM_QC_NO},
	{0x1D46A, UNICODE_NORM_QC_NO},
	{0x1D46B, UNICODE_NORM_QC_NO},
	{0x1D46C, UNICODE_NORM_QC_NO},
	{0x1D46D, UNICODE_NORM_QC_NO},
	{0x1D46E, UNICODE_NORM_QC_NO},
	{0x1D46F, UNICODE_NORM_QC_NO},
	{0x1D470, UNICODE_NORM_QC_NO},
	{0x1D471, UNICODE_NORM_QC_NO},
	{0x1D472, UNICODE_NORM_QC_NO},
	{0x1D473, UNICODE_NORM_QC_NO},
	{0x1D474, UNICODE_NORM_QC_NO},
	{0x1D475, UNICODE_NORM_QC_NO},
	{0x1D476, UNICODE_NORM_QC_NO},
	{0x1D477, UNICODE_NORM_QC_NO},
	{0x1D478, UNICODE_NORM_QC_NO},
	{0x1D479, UNICODE_NORM_QC_NO},
	{0x1D47A, UNICODE_NORM_QC_NO},
	{0x1D47B, UNICODE_NORM_QC_NO},
	{0x1D47C, UNICODE_NORM_QC_NO},
	{0x1D47D, UNICODE_NORM_QC_NO},
	{0x1D47E, UNICODE_NORM_QC_NO},
	{0x1D47F, UNICODE_NORM_QC_NO},
	{0x1D480, UNICODE_NORM_QC_NO},
	{0x1D481, UNICODE_NORM_QC_NO},
	{0x1D482, UNICODE_NORM_QC_NO},
	{0x1D483, UNICODE_NORM_QC_NO},
	{0x1D484, UNICODE_NORM_QC_NO},
	{0x1D485, UNICODE_NORM_QC_NO},
	{0x1D486, UNICODE_NORM_QC_NO},
	{0x1D487, UNICODE_NORM_QC_NO},
	{0x1D488, UNICODE_NORM_QC_NO},
	{0x1D489, UNICODE_NORM_QC_NO},
	{0x1D48A, UNICODE_NORM_QC_NO},
	{0x1D48B, UNICODE_NORM_QC_NO},
	{0x1D48C, UNICODE_NORM_QC_NO},
	{0x1D48D, UNICODE_NORM_QC_NO},
	{0x1D48E, UNICODE_NORM_QC_NO},
	{0x1D48F, UNICODE_NORM_QC_NO},
	{0x1D490, UNICODE_NORM_QC_NO},
	{0x1D491, UNICODE_NORM_QC_NO},
	{0x1D492, UNICODE_NORM_QC_NO},
	{0x1D493, UNICODE_NORM_QC_NO},
	{0x1D494, UNICODE_NORM_QC_NO},
	{0x1D495, UNICODE_NORM_QC_NO},
	{0x1D496, UNICODE_NORM_QC_NO},
	{0x1D497, UNICODE_NORM_QC_NO},
	{0x1D498, UNICODE_NORM_QC_NO},
	{0x1D499, UNICODE_NORM_QC_NO},
	{0x1D49A, UNICODE_NORM_QC_NO},
	{0x1D49B, UNICODE_NORM_QC_NO},
	{0x1D49C, UNICODE_NORM_QC_NO},
	{0x1D49E, UNICODE_NORM_QC_NO},
	{0x1D49F, UNICODE_NORM_QC_NO},
	{0x1D4A2, UNICODE_NORM_QC_NO},
	{0x1D4A5, UNICODE_NORM_QC_NO},
	{0x1D4A6, UNICODE_NORM_QC_NO},
	{0x1D4A9, UNICODE_NORM_QC_NO},
	{0x1D4AA, UNICODE_NORM_QC_NO},
	{0x1D4AB, UNICODE_NORM_QC_NO},
	{0x1D4AC, UNICODE_NORM_QC_NO},
	{0x1D4AE, UNICODE_NORM_QC_NO},
	{0x1D4AF, UNICODE_NORM_QC_NO},
	{0x1D4B0, UNICODE_NORM_QC_NO},
	{0x1D4B1, UNICODE_NORM_QC_NO},
	{0x1D4B2, UNICODE_NORM_QC_NO},
	{0x1D4B3, UNICODE_NORM_QC_NO},
	{0x1D4B4, UNICODE_NORM_QC_NO},
	{0x1D4B5, UNICODE_NORM_QC_NO},
	{0x1D4B6, UNICODE_NORM_QC_NO},
	{0x1D4B7, UNICODE_NORM_QC_NO},
	{0x1D4B8, UNICODE_NORM_QC_NO},
	{0x1D4B9, UNICODE_NORM_QC_NO},
	{0x1D4BB, UNICODE_NORM_QC_NO},
	{0x1D4BD, UNICODE_NORM_QC_NO},
	{0x1D4BE, UNICODE_NORM_QC_NO},
	{0x1D4BF, UNICODE_NORM_QC_NO},
	{0x1D4C0, UNICODE_NORM_QC_NO},
	{0x1D4C1, UNICODE_NORM_QC_NO},
	{0x1D4C2, UNICODE_NORM_QC_NO},
	{0x1D4C3, UNICODE_NORM_QC_NO},
	{0x1D4C5, UNICODE_NORM_QC_NO},
	{0x1D4C6, UNICODE_NORM_QC_NO},
	{0x1D4C7, UNICODE_NORM_QC_NO},
	{0x1D4C8, UNICODE_NORM_QC_NO},
	{0x1D4C9, UNICODE_NORM_QC_NO},
	{0x1D4CA, UNICODE_NORM_QC_NO},
	{0x1D4CB, UNICODE_NORM_QC_NO},
	{0x1D4CC, UNICODE_NORM_QC_NO},
	{0x1D4CD, UNICODE_NORM_QC_NO},
	{0x1D4CE, UNICODE_NORM_QC_NO},
	{0x1D4CF, UNICODE_NORM_QC_NO},
	{0x1D4D0, UNICODE_NORM_QC_NO},
	{0x1D4D1, UNICODE_NORM_QC_NO},
	{0x1D4D2, UNICODE_NORM_QC_NO},
	{0x1D4D3, UNICODE_NORM_QC_NO},
	{0x1D4D4, UNICODE_NORM_QC_NO},
	{0x1D4D5, UNICODE_NORM_QC_NO},
	{0x1D4D6, UNICODE_NORM_QC_NO},
	{0x1D4D7, UNICODE_NORM_QC_NO},
	{0x1D4D8, UNICODE_NORM_QC_NO},
	{0x1D4D9, UNICODE_NORM_QC_NO},
	{0x1D4DA, UNICODE_NORM_QC_NO},
	{0x1D4DB, UNICODE_NORM_QC_NO},
	{0x1D4DC, UNICODE_NORM_QC_NO},
	{0x1D4DD, UNICODE_NORM_QC_NO},
	{0x1D4DE, UNICODE_NORM_QC_NO},
	{0x1D4DF, UNICODE_NORM_QC_NO},
	{0x1D4E0, UNICODE_NORM_QC_NO},
	{0x1D4E1, UNICODE_NORM_QC_NO},
	{0x1D4E2, UNICODE_NORM_QC_NO},
	{0x1D4E3, UNICODE_NORM_QC_NO},
	{0x1D4E4, UNICODE_NORM_QC_NO},
	{0x1D4E5, UNICODE_NORM_QC_NO},
	{0x1D4E6, UNICODE_NORM_QC_NO},
	{0x1D4E7, UNICODE_NORM_QC_NO},
	{0x1D4E8, UNICODE_NORM_QC_NO},
	{0x1D4E9, UNICODE_NORM_QC_NO},
	{0x1D4EA, UNICODE_NORM_QC_NO},
	{0x1D4EB, UNICODE_NORM_QC_NO},
	{0x1D4EC, UNICODE_NORM_QC_NO},
	{0x1D4ED, UNICODE_NORM_QC_NO},
	{0x1D4EE, UNICODE_NORM_QC_NO},
	{0x1D4EF, UNICODE_NORM_QC_NO},
	{0x1D4F0, UNICODE_NORM_QC_NO},
	{0x1D4F1, UNICODE_NORM_QC_NO},
	{0x1D4F2, UNICODE_NORM_QC_NO},
	{0x1D4F3, UNICODE_NORM_QC_NO},
	{0x1D4F4, UNICODE_NORM_QC_NO},
	{0x1D4F5, UNICODE_NORM_QC_NO},
	{0x1D4F6, UNICODE_NORM_QC_NO},
	{0x1D4F7, UNICODE_NORM_QC_NO},
	{0x1D4F8, UNICODE_NORM_QC_NO},
	{0x1D4F9, UNICODE_NORM_QC_NO},
	{0x1D4FA, UNICODE_NORM_QC_NO},
	{0x1D4FB, UNICODE_NORM_QC_NO},
	{0x1D4FC, UNICODE_NORM_QC_NO},
	{0x1D4FD, UNICODE_NORM_QC_NO},
	{0x1D4FE, UNICODE_NORM_QC_NO},
	{0x1D4FF, UNICODE_NORM_QC_NO},
	{0x1D500, UNICODE_NORM_QC_NO},
	{0x1D501, UNICODE_NORM_QC_NO},
	{0x1D502, UNICODE_NORM_QC_NO},
	{0x1D503, UNICODE_NORM_QC_NO},
	{0x1D504, UNICODE_NORM_QC_NO},
	{0x1D505, UNICODE_NORM_QC_NO},
	{0x1D507, UNICODE_NORM_QC_NO},
	{0x1D508, UNICODE_NORM_QC_NO},
	{0x1D509, UNICODE_NORM_QC_NO},
	{0x1D50A, UNICODE_NORM_QC_NO},
	{0x1D50D, UNICODE_NORM_QC_NO},
	{0x1D50E, UNICODE_NORM_QC_NO},
	{0x1D50F, UNICODE_NORM_QC_NO},
	{0x1D510, UNICODE_NORM_QC_NO},
	{0x1D511, UNICODE_NORM_QC_NO},
	{0x1D512, UNICODE_NORM_QC_NO},
	{0x1D513, UNICODE_NORM_QC_NO},
	{0x1D514, UNICODE_NORM_QC_NO},
	{0x1D516, UNICODE_NORM_QC_NO},
	{0x1D517, UNICODE_NORM_QC_NO},
	{0x1D518, UNICODE_NORM_QC_NO},
	{0x1D519, UNICODE_NORM_QC_NO},
	{0x1D51A, UNICODE_NORM_QC_NO},
	{0x1D51B, UNICODE_NORM_QC_NO},
	{0x1D51C, UNICODE_NORM_QC_NO},
	{0x1D51E, UNICODE_NORM_QC_NO},
	{0x1D51F, UNICODE_NORM_QC_NO},
	{0x1D520, UNICODE_NORM_QC_NO},
	{0x1D521, UNICODE_NORM_QC_NO},
	{0x1D522, UNICODE_NORM_QC_NO},
	{0x1D523, UNICODE_NORM_QC_NO},
	{0x1D524, UNICODE_NORM_QC_NO},
	{0x1D525, UNICODE_NORM_QC_NO},
	{0x1D526, UNICODE_NORM_QC_NO},
	{0x1D527, UNICODE_NORM_QC_NO},
	{0x1D528, UNICODE_NORM_QC_NO},
	{0x1D529, UNICODE_NORM_QC_NO},
	{0x1D52A, UNICODE_NORM_QC_NO},
	{0x1D52B, UNICODE_NORM_QC_NO},
	{0x1D52C, UNICODE_NORM_QC_NO},
	{0x1D52D, UNICODE_NORM_QC_NO},
	{0x1D52E, UNICODE_NORM_QC_NO},
	{0x1D52F, UNICODE_NORM_QC_NO},
	{0x1D530, UNICODE_NORM_QC_NO},
	{0x1D531, UNICODE_NORM_QC_NO},
	{0x1D532, UNICODE_NORM_QC_NO},
	{0x1D533, UNICODE_NORM_QC_NO},
	{0x1D534, UNICODE_NORM_QC_NO},
	{0x1D535, UNICODE_NORM_QC_NO},
	{0x1D536, UNICODE_NORM_QC_NO},
	{0x1D537, UNICODE_NORM_QC_NO},
	{0x1D538, UNICODE_NORM_QC_NO},
	{0x1D539, UNICODE_NORM_QC_NO},
	{0x1D53B, UNICODE_NORM_QC_NO},
	{0x1D53C, UNICODE_NORM_QC_NO},
	{0x1D53D, UNICODE_NORM_QC_NO},
	{0x1D53E, UNICODE_NORM_QC_NO},
	{0x1D540, UNICODE_NORM_QC_NO},
	{0x1D541, UNICODE_NORM_QC_NO},
	{0x1D542, UNICODE_NORM_QC_NO},
	{0x1D543, UNICODE_NORM_QC_NO},
	{0x1D544, UNICODE_NORM_QC_NO},
	{0x1D546, UNICODE_NORM_QC_NO},
	{0x1D54A, UNICODE_NORM_QC_NO},
	{0x1D54B, UNICODE_NORM_QC_NO},
	{0x1D54C, UNICODE_NORM_QC_NO},
	{0x1D54D, UNICODE_NORM_QC_NO},
	{0x1D54E, UNICODE_NORM_QC_NO},
	{0x1D54F, UNICODE_NORM_QC_NO},
	{0x1D550, UNICODE_NORM_QC_NO},
	{0x1D552, UNICODE_NORM_QC_NO},
	{0x1D553, UNICODE_NORM_QC_NO},
	{0x1D554, UNICODE_NORM_QC_NO},
	{0x1D555, UNICODE_NORM_QC_NO},
	{0x1D556, UNICODE_NORM_QC_NO},
	{0x1D557, UNICODE_NORM_QC_NO},
	{0x1D558, UNICODE_NORM_QC_NO},
	{0x1D559, UNICODE_NORM_QC_NO},
	{0x1D55A, UNICODE_NORM_QC_NO},
	{0x1D55B, UNICODE_NORM_QC_NO},
	{0x1D55C, UNICODE_NORM_QC_NO},
	{0x1D55D, UNICODE_NORM_QC_NO},
	{0x1D55E, UNICODE_NORM_QC_NO},
	{0x1D55F, UNICODE_NORM_QC_NO},
	{0x1D560, UNICODE_NORM_QC_NO},
	{0x1D561, UNICODE_NORM_QC_NO},
	{0x1D562, UNICODE_NORM_QC_NO},
	{0x1D563, UNICODE_NORM_QC_NO},
	{0x1D564, UNICODE_NORM_QC_NO},
	{0x1D565, UNICODE_NORM_QC_NO},
	{0x1D566, UNICODE_NORM_QC_NO},
	{0x1D567, UNICODE_NORM_QC_NO},
	{0x1D568, UNICODE_NORM_QC_NO},
	{0x1D569, UNICODE_NORM_QC_NO},
	{0x1D56A, UNICODE_NORM_QC_NO},
	{0x1D56B, UNICODE_NORM_QC_NO},
	{0x1D56C, UNICODE_NORM_QC_NO},
	{0x1D56D, UNICODE_NORM_QC_NO},
	{0x1D56E, UNICODE_NORM_QC_NO},
	{0x1D56F, UNICODE_NORM_QC_NO},
	{0x1D570, UNICODE_NORM_QC_NO},
	{0x1D571, UNICODE_NORM_QC_NO},
	{0x1D572, UNICODE_NORM_QC_NO},
	{0x1D573, UNICODE_NORM_QC_NO},
	{0x1D574, UNICODE_NORM_QC_NO},
	{0x1D575, UNICODE_NORM_QC_NO},
	{0x1D576, UNICODE_NORM_QC_NO},
	{0x1D577, UNICODE_NORM_QC_NO},
	{0x1D578, UNICODE_NORM_QC_NO},
	{0x1D579, UNICODE_NORM_QC_NO},
	{0x1D57A, UNICODE_NORM_QC_NO},
	{0x1D57B, UNICODE_NORM_QC_NO},
	{0x1D57C, UNICODE_NORM_QC_NO},
	{0x1D57D, UNICODE_NORM_QC_NO},
	{0x1D57E, UNICODE_NORM_QC_NO},
	{0x1D57F, UNICODE_NORM_QC_NO},
	{0x1D580, UNICODE_NORM_QC_NO},
	{0x1D581, UNICODE_NORM_QC_NO},
	{0x1D582, UNICODE_NORM_QC_NO},
	{0x1D583, UNICODE_NORM_QC_NO},
	{0x1D584, UNICODE_NORM_QC_NO},
	{0x1D585, UNICODE_NORM_QC_NO},
	{0x1D586, UNICODE_NORM_QC_NO},
	{0x1D587, UNICODE_NORM_QC_NO},
	{0x1D588, UNICODE_NORM_QC_NO},
	{0x1D589, UNICODE_NORM_QC_NO},
	{0x1D58A, UNICODE_NORM_QC_NO},
	{0x1D58B, UNICODE_NORM_QC_NO},
	{0x1D58C, UNICODE_NORM_QC_NO},
	{0x1D58D, UNICODE_NORM_QC_NO},
	{0x1D58E, UNICODE_NORM_QC_NO},
	{0x1D58F, UNICODE_NORM_QC_NO},
	{0x1D590, UNICODE_NORM_QC_NO},
	{0x1D591, UNICODE_NORM_QC_NO},
	{0x1D592, UNICODE_NORM_QC_NO},
	{0x1D593, UNICODE_NORM_QC_NO},
	{0x1D594, UNICODE_NORM_QC_NO},
	{0x1D595, UNICODE_NORM_QC_NO},
	{0x1D596, UNICODE_NORM_QC_NO},
	{0x1D597, UNICODE_NORM_QC_NO},
	{0x1D598, UNICODE_NORM_QC_NO},
	{0x1D599, UNICODE_NORM_QC_NO},
	{0x1D59A, UNICODE_NORM_QC_NO},
	{0x1D59B, UNICODE_NORM_QC_NO},
	{0x1D59C, UNICODE_NORM_QC_NO},
	{0x1D59D, UNICODE_NORM_QC_NO},
	{0x1D59E, UNICODE_NORM_QC_NO},
	{0x1D59F, UNICODE_NORM_QC_NO},
	{0x1D5A0, UNICODE_NORM_QC_NO},
	{0x1D5A1, UNICODE_NORM_QC_NO},
	{0x1D5A2, UNICODE_NORM_QC_NO},
	{0x1D5A3, UNICODE_NORM_QC_NO},
	{0x1D5A4, UNICODE_NORM_QC_NO},
	{0x1D5A5, UNICODE_NORM_QC_NO},
	{0x1D5A6, UNICODE_NORM_QC_NO},
	{0x1D5A7, UNICODE_NORM_QC_NO},
	{0x1D5A8, UNICODE_NORM_QC_NO},
	{0x1D5A9, UNICODE_NORM_QC_NO},
	{0x1D5AA, UNICODE_NORM_QC_NO},
	{0x1D5AB, UNICODE_NORM_QC_NO},
	{0x1D5AC, UNICODE_NORM_QC_NO},
	{0x1D5AD, UNICODE_NORM_QC_NO},
	{0x1D5AE, UNICODE_NORM_QC_NO},
	{0x1D5AF, UNICODE_NORM_QC_NO},
	{0x1D5B0, UNICODE_NORM_QC_NO},
	{0x1D5B1, UNICODE_NORM_QC_NO},
	{0x1D5B2, UNICODE_NORM_QC_NO},
	{0x1D5B3, UNICODE_NORM_QC_NO},
	{0x1D5B4, UNICODE_NORM_QC_NO},
	{0x1D5B5, UNICODE_NORM_QC_NO},
	{0x1D5B6, UNICODE_NORM_QC_NO},
	{0x1D5B7, UNICODE_NORM_QC_NO},
	{0x1D5B8, UNICODE_NORM_QC_NO},
	{0x1D5B9, UNICODE_NORM_QC_NO},
	{0x1D5BA, UNICODE_NORM_QC_NO},
	{0x1D5BB, UNICODE_NORM_QC_NO},
	{0x1D5BC, UNICODE_NORM_QC_NO},
	{0x1D5BD, UNICODE_NORM_QC_NO},
	{0x1D5BE, UNICODE_NORM_QC_NO},
	{0x1D5BF, UNICODE_NORM_QC_NO},
	{0x1D5C0, UNICODE_NORM_QC_NO},
	{0x1D5C1, UNICODE_NORM_QC_NO},
	{0x1D5C2, UNICODE_NORM_QC_NO},
	{0x1D5C3, UNICODE_NORM_QC_NO},
	{0x1D5C4, UNICODE_NORM_QC_NO},
	{0x1D5C5, UNICODE_NORM_QC_NO},
	{0x1D5C6, UNICODE_NORM_QC_NO},
	{0x1D5C7, UNICODE_NORM_QC_NO},
	{0x1D5C8, UNICODE_NORM_QC_NO},
	{0x1D5C9, UNICODE_NORM_QC_NO},
	{0x1D5CA, UNICODE_NORM_QC_NO},
	{0x1D5CB, UNICODE_NORM_QC_NO},
	{0x1D5CC, UNICODE_NORM_QC_NO},
	{0x1D5CD, UNICODE_NORM_QC_NO},
	{0x1D5CE, UNICODE_NORM_QC_NO},
	{0x1D5CF, UNICODE_NORM_QC_NO},
	{0x1D5D0, UNICODE_NORM_QC_NO},
	{0x1D5D1, UNICODE_NORM_QC_NO},
	{0x1D5D2, UNICODE_NORM_QC_NO},
	{0x1D5D3, UNICODE_NORM_QC_NO},
	{0x1D5D4, UNICODE_NORM_QC_NO},
	{0x1D5D5, UNICODE_NORM_QC_NO},
	{0x1D5D6, UNICODE_NORM_QC_NO},
	{0x1D5D7, UNICODE_NORM_QC_NO},
	{0x1D5D8, UNICODE_NORM_QC_NO},
	{0x1D5D9, UNICODE_NORM_QC_NO},
	{0x1D5DA, UNICODE_NORM_QC_NO},
	{0x1D5DB, UNICODE_NORM_QC_NO},
	{0x1D5DC, UNICODE_NORM_QC_NO},
	{0x1D5DD, UNICODE_NORM_QC_NO},
	{0x1D5DE, UNICODE_NORM_QC_NO},
	{0x1D5DF, UNICODE_NORM_QC_NO},
	{0x1D5E0, UNICODE_NORM_QC_NO},
	{0x1D5E1, UNICODE_NORM_QC_NO},
	{0x1D5E2, UNICODE_NORM_QC_NO},
	{0x1D5E3, UNICODE_NORM_QC_NO},
	{0x1D5E4, UNICODE_NORM_QC_NO},
	{0x1D5E5, UNICODE_NORM_QC_NO},
	{0x1D5E6, UNICODE_NORM_QC_NO},
	{0x1D5E7, UNICODE_NORM_QC_NO},
	{0x1D5E8, UNICODE_NORM_QC_NO},
	{0x1D5E9, UNICODE_NORM_QC_NO},
	{0x1D5EA, UNICODE_NORM_QC_NO},
	{0x1D5EB, UNICODE_NORM_QC_NO},
	{0x1D5EC, UNICODE_NORM_QC_NO},
	{0x1D5ED, UNICODE_NORM_QC_NO},
	{0x1D5EE, UNICODE_NORM_QC_NO},
	{0x1D5EF, UNICODE_NORM_QC_NO},
	{0x1D5F0, UNICODE_NORM_QC_NO},
	{0x1D5F1, UNICODE_NORM_QC_NO},
	{0x1D5F2, UNICODE_NORM_QC_NO},
	{0x1D5F3, UNICODE_NORM_QC_NO},
	{0x1D5F4, UNICODE_NORM_QC_NO},
	{0x1D5F5, UNICODE_NORM_QC_NO},
	{0x1D5F6, UNICODE_NORM_QC_NO},
	{0x1D5F7, UNICODE_NORM_QC_NO},
	{0x1D5F8, UNICODE_NORM_QC_NO},
	{0x1D5F9, UNICODE_NORM_QC_NO},
	{0x1D5FA, UNICODE_NORM_QC_NO},
	{0x1D5FB, UNICODE_NORM_QC_NO},
	{0x1D5FC, UNICODE_NORM_QC_NO},
	{0x1D5FD, UNICODE_NORM_QC_NO},
	{0x1D5FE, UNICODE_NORM_QC_NO},
	{0x1D5FF, UNICODE_NORM_QC_NO},
	{0x1D600, UNICODE_NORM_QC_NO},
	{0x1D601, UNICODE_NORM_QC_NO},
	{0x1D602, UNICODE_NORM_QC_NO},
	{0x1D603, UNICODE_NORM_QC_NO},
	{0x1D604, UNICODE_NORM_QC_NO},
	{0x1D605, UNICODE_NORM_QC_NO},
	{0x1D606, UNICODE_NORM_QC_NO},
	{0x1D607, UNICODE_NORM_QC_NO},
	{0x1D608, UNICODE_NORM_QC_NO},
	{0x1D609, UNICODE_NORM_QC_NO},
	{0x1D60A, UNICODE_NORM_QC_NO},
	{0x1D60B, UNICODE_NORM_QC_NO},
	{0x1D60C, UNICODE_NORM_QC_NO},
	{0x1D60D, UNICODE_NORM_QC_NO},
	{0x1D60E, UNICODE_NORM_QC_NO},
	{0x1D60F, UNICODE_NORM_QC_NO},
	{0x1D610, UNICODE_NORM_QC_NO},
	{0x1D611, UNICODE_NORM_QC_NO},
	{0x1D612, UNICODE_NORM_QC_NO},
	{0x1D613, UNICODE_NORM_QC_NO},
	{0x1D614, UNICODE_NORM_QC_NO},
	{0x1D615, UNICODE_NORM_QC_NO},
	{0x1D616, UNICODE_NORM_QC_NO},
	{0x1D617, UNICODE_NORM_QC_NO},
	{0x1D618, UNICODE_NORM_QC_NO},
	{0x1D619, UNICODE_NORM_QC_NO},
	{0x1D61A, UNICODE_NORM_QC_NO},
	{0x1D61B, UNICODE_NORM_QC_NO},
	{0x1D61C, UNICODE_NORM_QC_NO},
	{0x1D61D, UNICODE_NORM_QC_NO},
	{0x1D61E, UNICODE_NORM_QC_NO},
	{0x1D61F, UNICODE_NORM_QC_NO},
	{0x1D620, UNICODE_NORM_QC_NO},
	{0x1D621, UNICODE_NORM_QC_NO},
	{0x1D622, UNICODE_NORM_QC_NO},
	{0x1D623, UNICODE_NORM_QC_NO},
	{0x1D624, UNICODE_NORM_QC_NO},
	{0x1D625, UNICODE_NORM_QC_NO},
	{0x1D626, UNICODE_NORM_QC_NO},
	{0x1D627, UNICODE_NORM_QC_NO},
	{0x1D628, UNICODE_NORM_QC_NO},
	{0x1D629, UNICODE_NORM_QC_NO},
	{0x1D62A, UNICODE_NORM_QC_NO},
	{0x1D62B, UNICODE_NORM_QC_NO},
	{0x1D62C, UNICODE_NORM_QC_NO},
	{0x1D62D, UNICODE_NORM_QC_NO},
	{0x1D62E, UNICODE_NORM_QC_NO},
	{0x1D62F, UNICODE_NORM_QC_NO},
	{0x1D630, UNICODE_NORM_QC_NO},
	{0x1D631, UNICODE_NORM_QC_NO},
	{0x1D632, UNICODE_NORM_QC_NO},
	{0x1D633, UNICODE_NORM_QC_NO},
	{0x1D634, UNICODE_NORM_QC_NO},
	{0x1D635, UNICODE_NORM_QC_NO},
	{0x1D636, UNICODE_NORM_QC_NO},
	{0x1D637, UNICODE_NORM_QC_NO},
	{0x1D638, UNICODE_NORM_QC_NO},
	{0x1D639, UNICODE_NORM_QC_NO},
	{0x1D63A, UNICODE_NORM_QC_NO},
	{0x1D63B, UNICODE_NORM_QC_NO},
	{0x1D63C, UNICODE_NORM_QC_NO},
	{0x1D63D, UNICODE_NORM_QC_NO},
	{0x1D63E, UNICODE_NORM_QC_NO},
	{0x1D63F, UNICODE_NORM_QC_NO},
	{0x1D640, UNICODE_NORM_QC_NO},
	{0x1D641, UNICODE_NORM_QC_NO},
	{0x1D642, UNICODE_NORM_QC_NO},
	{0x1D643, UNICODE_NORM_QC_NO},
	{0x1D644, UNICODE_NORM_QC_NO},
	{0x1D645, UNICODE_NORM_QC_NO},
	{0x1D646, UNICODE_NORM_QC_NO},
	{0x1D647, UNICODE_NORM_QC_NO},
	{0x1D648, UNICODE_NORM_QC_NO},
	{0x1D649, UNICODE_NORM_QC_NO},
	{0x1D64A, UNICODE_NORM_QC_NO},
	{0x1D64B, UNICODE_NORM_QC_NO},
	{0x1D64C, UNICODE_NORM_QC_NO},
	{0x1D64D, UNICODE_NORM_QC_NO},
	{0x1D64E, UNICODE_NORM_QC_NO},
	{0x1D64F, UNICODE_NORM_QC_NO},
	{0x1D650, UNICODE_NORM_QC_NO},
	{0x1D651, UNICODE_NORM_QC_NO},
	{0x1D652, UNICODE_NORM_QC_NO},
	{0x1D653, UNICODE_NORM_QC_NO},
	{0x1D654, UNICODE_NORM_QC_NO},
	{0x1D655, UNICODE_NORM_QC_NO},
	{0x1D656, UNICODE_NORM_QC_NO},
	{0x1D657, UNICODE_NORM_QC_NO},
	{0x1D658, UNICODE_NORM_QC_NO},
	{0x1D659, UNICODE_NORM_QC_NO},
	{0x1D65A, UNICODE_NORM_QC_NO},
	{0x1D65B, UNICODE_NORM_QC_NO},
	{0x1D65C, UNICODE_NORM_QC_NO},
	{0x1D65D, UNICODE_NORM_QC_NO},
	{0x1D65E, UNICODE_NORM_QC_NO},
	{0x1D65F, UNICODE_NORM_QC_NO},
	{0x1D660, UNICODE_NORM_QC_NO},
	{0x1D661, UNICODE_NORM_QC_NO},
	{0x1D662, UNICODE_NORM_QC_NO},
	{0x1D663, UNICODE_NORM_QC_NO},
	{0x1D664, UNICODE_NORM_QC_NO},
	{0x1D665, UNICODE_NORM_QC_NO},
	{0x1D666, UNICODE_NORM_QC_NO},
	{0x1D667, UNICODE_NORM_QC_NO},
	{0x1D668, UNICODE_NORM_QC_NO},
	{0x1D669, UNICODE_NORM_QC_NO},
	{0x1D66A, UNICODE_NORM_QC_NO},
	{0x1D66B, UNICODE_NORM_QC_NO},
	{0x1D66C, UNICODE_NORM_QC_NO},
	{0x1D66D, UNICODE_NORM_QC_NO},
	{0x1D66E, UNICODE_NORM_QC_NO},
	{0x1D66F, UNICODE_NORM_QC_NO},
	{0x1D670, UNICODE_NORM_QC_NO},
	{0x1D671, UNICODE_NORM_QC_NO},
	{0x1D672, UNICODE_NORM_QC_NO},
	{0x1D673, UNICODE_NORM_QC_NO},
	{0x1D674, UNICODE_NORM_QC_NO},
	{0x1D675, UNICODE_NORM_QC_NO},
	{0x1D676, UNICODE_NORM_QC_NO},
	{0x1D677, UNICODE_NORM_QC_NO},
	{0x1D678, UNICODE_NORM_QC_NO},
	{0x1D679, UNICODE_NORM_QC_NO},
	{0x1D67A, UNICODE_NORM_QC_NO},
	{0x1D67B, UNICODE_NORM_QC_NO},
	{0x1D67C, UNICODE_NORM_QC_NO},
	{0x1D67D, UNICODE_NORM_QC_NO},
	{0x1D67E, UNICODE_NORM_QC_NO},
	{0x1D67F, UNICODE_NORM_QC_NO},
	{0x1D680, UNICODE_NORM_QC_NO},
	{0x1D681, UNICODE_NORM_QC_NO},
	{0x1D682, UNICODE_NORM_QC_NO},
	{0x1D683, UNICODE_NORM_QC_NO},
	{0x1D684, UNICODE_NORM_QC_NO},
	{0x1D685, UNICODE_NORM_QC_NO},
	{0x1D686, UNICODE_NORM_QC_NO},
	{0x1D687, UNICODE_NORM_QC_NO},
	{0x1D688, UNICODE_NORM_QC_NO},
	{0x1D689, UNICODE_NORM_QC_NO},
	{0x1D68A, UNICODE_NORM_QC_NO},
	{0x1D68B, UNICODE_NORM_QC_NO},
	{0x1D68C, UNICODE_NORM_QC_NO},
	{0x1D68D, UNICODE_NORM_QC_NO},
	{0x1D68E, UNICODE_NORM_QC_NO},
	{0x1D68F, UNICODE_NORM_QC_NO},
	{0x1D690, UNICODE_NORM_QC_NO},
	{0x1D691, UNICODE_NORM_QC_NO},
	{0x1D692, UNICODE_NORM_QC_NO},
	{0x1D693, UNICODE_NORM_QC_NO},
	{0x1D694, UNICODE_NORM_QC_NO},
	{0x1D695, UNICODE_NORM_QC_NO},
	{0x1D696, UNICODE_NORM_QC_NO},
	{0x1D697, UNICODE_NORM_QC_NO},
	{0x1D698, UNICODE_NORM_QC_NO},
	{0x1D699, UNICODE_NORM_QC_NO},
	{0x1D69A, UNICODE_NORM_QC_NO},
	{0x1D69B, UNICODE_NORM_QC_NO},
	{0x1D69C, UNICODE_NORM_QC_NO},
	{0x1D69D, UNICODE_NORM_QC_NO},
	{0x1D69E, UNICODE_NORM_QC_NO},
	{0x1D69F, UNICODE_NORM_QC_NO},
	{0x1D6A0, UNICODE_NORM_QC_NO},
	{0x1D6A1, UNICODE_NORM_QC_NO},
	{0x1D6A2, UNICODE_NORM_QC_NO},
	{0x1D6A3, UNICODE_NORM_QC_NO},
	{0x1D6A4, UNICODE_NORM_QC_NO},
	{0x1D6A5, UNICODE_NORM_QC_NO},
	{0x1D6A8, UNICODE_NORM_QC_NO},
	{0x1D6A9, UNICODE_NORM_QC_NO},
	{0x1D6AA, UNICODE_NORM_QC_NO},
	{0x1D6AB, UNICODE_NORM_QC_NO},
	{0x1D6AC, UNICODE_NORM_QC_NO},
	{0x1D6AD, UNICODE_NORM_QC_NO},
	{0x1D6AE, UNICODE_NORM_QC_NO},
	{0x1D6AF, UNICODE_NORM_QC_NO},
	{0x1D6B0, UNICODE_NORM_QC_NO},
	{0x1D6B1, UNICODE_NORM_QC_NO},
	{0x1D6B2, UNICODE_NORM_QC_NO},
	{0x1D6B3, UNICODE_NORM_QC_NO},
	{0x1D6B4, UNICODE_NORM_QC_NO},
	{0x1D6B5, UNICODE_NORM_QC_NO},
	{0x1D6B6, UNICODE_NORM_QC_NO},
	{0x1D6B7, UNICODE_NORM_QC_NO},
	{0x1D6B8, UNICODE_NORM_QC_NO},
	{0x1D6B9, UNICODE_NORM_QC_NO},
	{0x1D6BA, UNICODE_NORM_QC_NO},
	{0x1D6BB, UNICODE_NORM_QC_NO},
	{0x1D6BC, UNICODE_NORM_QC_NO},
	{0x1D6BD, UNICODE_NORM_QC_NO},
	{0x1D6BE, UNICODE_NORM_QC_NO},
	{0x1D6BF, UNICODE_NORM_QC_NO},
	{0x1D6C0, UNICODE_NORM_QC_NO},
	{0x1D6C1, UNICODE_NORM_QC_NO},
	{0x1D6C2, UNICODE_NORM_QC_NO},
	{0x1D6C3, UNICODE_NORM_QC_NO},
	{0x1D6C4, UNICODE_NORM_QC_NO},
	{0x1D6C5, UNICODE_NORM_QC_NO},
	{0x1D6C6, UNICODE_NORM_QC_NO},
	{0x1D6C7, UNICODE_NORM_QC_NO},
	{0x1D6C8, UNICODE_NORM_QC_NO},
	{0x1D6C9, UNICODE_NORM_QC_NO},
	{0x1D6CA, UNICODE_NORM_QC_NO},
	{0x1D6CB, UNICODE_NORM_QC_NO},
	{0x1D6CC, UNICODE_NORM_QC_NO},
	{0x1D6CD, UNICODE_NORM_QC_NO},
	{0x1D6CE, UNICODE_NORM_QC_NO},
	{0x1D6CF, UNICODE_NORM_QC_NO},
	{0x1D6D0, UNICODE_NORM_QC_NO},
	{0x1D6D1, UNICODE_NORM_QC_NO},
	{0x1D6D2, UNICODE_NORM_QC_NO},
	{0x1D6D3, UNICODE_NORM_QC_NO},
	{0x1D6D4, UNICODE_NORM_QC_NO},
	{0x1D6D5, UNICODE_NORM_QC_NO},
	{0x1D6D6, UNICODE_NORM_QC_NO},
	{0x1D6D7, UNICODE_NORM_QC_NO},
	{0x1D6D8, UNICODE_NORM_QC_NO},
	{0x1D6D9, UNICODE_NORM_QC_NO},
	{0x1D6DA, UNICODE_NORM_QC_NO},
	{0x1D6DB, UNICODE_NORM_QC_NO},
	{0x1D6DC, UNICODE_NORM_QC_NO},
	{0x1D6DD, UNICODE_NORM_QC_NO},
	{0x1D6DE, UNICODE_NORM_QC_NO},
	{0x1D6DF, UNICODE_NORM_QC_NO},
	{0x1D6E0, UNICODE_NORM_QC_NO},
	{0x1D6E1, UNICODE_NORM_QC_NO},
	{0x1D6E2, UNICODE_NORM_QC_NO},
	{0x1D6E3, UNICODE_NORM_QC_NO},
	{0x1D6E4, UNICODE_NORM_QC_NO},
	{0x1D6E5, UNICODE_NORM_QC_NO},
	{0x1D6E6, UNICODE_NORM_QC_NO},
	{0x1D6E7, UNICODE_NORM_QC_NO},
	{0x1D6E8, UNICODE_NORM_QC_NO},
	{0x1D6E9, UNICODE_NORM_QC_NO},
	{0x1D6EA, UNICODE_NORM_QC_NO},
	{0x1D6EB, UNICODE_NORM_QC_NO},
	{0x1D6EC, UNICODE_NORM_QC_NO},
	{0x1D6ED, UNICODE_NORM_QC_NO},
	{0x1D6EE, UNICODE_NORM_QC_NO},
	{0x1D6EF, UNICODE_NORM_QC_NO},
	{0x1D6F0, UNICODE_NORM_QC_NO},
	{0x1D6F1, UNICODE_NORM_QC_NO},
	{0x1D6F2, UNICODE_NORM_QC_NO},
	{0x1D6F3, UNICODE_NORM_QC_NO},
	{0x1D6F4, UNICODE_NORM_QC_NO},
	{0x1D6F5, UNICODE_NORM_QC_NO},
	{0x1D6F6, UNICODE_NORM_QC_NO},
	{0x1D6F7, UNICODE_NORM_QC_NO},
	{0x1D6F8, UNICODE_NORM_QC_NO},
	{0x1D6F9, UNICODE_NORM_QC_NO},
	{0x1D6FA, UNICODE_NORM_QC_NO},
	{0x1D6FB, UNICODE_NORM_QC_NO},
	{0x1D6FC, UNICODE_NORM_QC_NO},
	{0x1D6FD, UNICODE_NORM_QC_NO},
	{0x1D6FE, UNICODE_NORM_QC_NO},
	{0x1D6FF, UNICODE_NORM_QC_NO},
	{0x1D700, UNICODE_NORM_QC_NO},
	{0x1D701, UNICODE_NORM_QC_NO},
	{0x1D702, UNICODE_NORM_QC_NO},
	{0x1D703, UNICODE_NORM_QC_NO},
	{0x1D704, UNICODE_NORM_QC_NO},
	{0x1D705, UNICODE_NORM_QC_NO},
	{0x1D706, UNICODE_NORM_QC_NO},
	{0x1D707, UNICODE_NORM_QC_NO},
	{0x1D708, UNICODE_NORM_QC_NO},
	{0x1D709, UNICODE_NORM_QC_NO},
	{0x1D70A, UNICODE_NORM_QC_NO},
	{0x1D70B, UNICODE_NORM_QC_NO},
	{0x1D70C, UNICODE_NORM_QC_NO},
	{0x1D70D, UNICODE_NORM_QC_NO},
	{0x1D70E, UNICODE_NORM_QC_NO},
	{0x1D70F, UNICODE_NORM_QC_NO},
	{0x1D710, UNICODE_NORM_QC_NO},
	{0x1D711, UNICODE_NORM_QC_NO},
	{0x1D712, UNICODE_NORM_QC_NO},
	{0x1D713, UNICODE_NORM_QC_NO},
	{0x1D714, UNICODE_NORM_QC_NO},
	{0x1D715, UNICODE_NORM_QC_NO},
	{0x1D716, UNICODE_NORM_QC_NO},
	{0x1D717, UNICODE_NORM_QC_NO},
	{0x1D718, UNICODE_NORM_QC_NO},
	{0x1D719, UNICODE_NORM_QC_NO},
	{0x1D71A, UNICODE_NORM_QC_NO},
	{0x1D71B, UNICODE_NORM_QC_NO},
	{0x1D71C, UNICODE_NORM_QC_NO},
	{0x1D71D, UNICODE_NORM_QC_NO},
	{0x1D71E, UNICODE_NORM_QC_NO},
	{0x1D71F, UNICODE_NORM_QC_NO},
	{0x1D720, UNICODE_NORM_QC_NO},
	{0x1D721, UNICODE_NORM_QC_NO},
	{0x1D722, UNICODE_NORM_QC_NO},
	{0x1D723, UNICODE_NORM_QC_NO},
	{0x1D724, UNICODE_NORM_QC_NO},
	{0x1D725, UNICODE_NORM_QC_NO},
	{0x1D726, UNICODE_NORM_QC_NO},
	{0x1D727, UNICODE_NORM_QC_NO},
	{0x1D728, UNICODE_NORM_QC_NO},
	{0x1D729, UNICODE_NORM_QC_NO},
	{0x1D72A, UNICODE_NORM_QC_NO},
	{0x1D72B, UNICODE_NORM_QC_NO},
	{0x1D72C, UNICODE_NORM_QC_NO},
	{0x1D72D, UNICODE_NORM_QC_NO},
	{0x1D72E, UNICODE_NORM_QC_NO},
	{0x1D72F, UNICODE_NORM_QC_NO},
	{0x1D730, UNICODE_NORM_QC_NO},
	{0x1D731, UNICODE_NORM_QC_NO},
	{0x1D732, UNICODE_NORM_QC_NO},
	{0x1D733, UNICODE_NORM_QC_NO},
	{0x1D734, UNICODE_NORM_QC_NO},
	{0x1D735, UNICODE_NORM_QC_NO},
	{0x1D736, UNICODE_NORM_QC_NO},
	{0x1D737, UNICODE_NORM_QC_NO},
	{0x1D738, UNICODE_NORM_QC_NO},
	{0x1D739, UNICODE_NORM_QC_NO},
	{0x1D73A, UNICODE_NORM_QC_NO},
	{0x1D73B, UNICODE_NORM_QC_NO},
	{0x1D73C, UNICODE_NORM_QC_NO},
	{0x1D73D, UNICODE_NORM_QC_NO},
	{0x1D73E, UNICODE_NORM_QC_NO},
	{0x1D73F, UNICODE_NORM_QC_NO},
	{0x1D740, UNICODE_NORM_QC_NO},
	{0x1D741, UNICODE_NORM_QC_NO},
	{0x1D742, UNICODE_NORM_QC_NO},
	{0x1D743, UNICODE_NORM_QC_NO},
	{0x1D744, UNICODE_NORM_QC_NO},
	{0x1D745, UNICODE_NORM_QC_NO},
	{0x1D746, UNICODE_NORM_QC_NO},
	{0x1D747, UNICODE_NORM_QC_NO},
	{0x1D748, UNICODE_NORM_QC_NO},
	{0x1D749, UNICODE_NORM_QC_NO},
	{0x1D74A, UNICODE_NORM_QC_NO},
	{0x1D74B, UNICODE_NORM_QC_NO},
	{0x1D74C, UNICODE_NORM_QC_NO},
	{0x1D74D, UNICODE_NORM_QC_NO},
	{0x1D74E, UNICODE_NORM_QC_NO},
	{0x1D74F, UNICODE_NORM_QC_NO},
	{0x1D750, UNICODE_NORM_QC_NO},
	{0x1D751, UNICODE_NORM_QC_NO},
	{0x1D752, UNICODE_NORM_QC_NO},
	{0x1D753, UNICODE_NORM_QC_NO},
	{0x1D754, UNICODE_NORM_QC_NO},
	{0x1D755, UNICODE_NORM_QC_NO},
	{0x1D756, UNICODE_NORM_QC_NO},
	{0x1D757, UNICODE_NORM_QC_NO},
	{0x1D758, UNICODE_NORM_QC_NO},
	{0x1D759, UNICODE_NORM_QC_NO},
	{0x1D75A, UNICODE_NORM_QC_NO},
	{0x1D75B, UNICODE_NORM_QC_NO},
	{0x1D75C, UNICODE_NORM_QC_NO},
	{0x1D75D, UNICODE_NORM_QC_NO},
	{0x1D75E, UNICODE_NORM_QC_NO},
	{0x1D75F, UNICODE_NORM_QC_NO},
	{0x1D760, UNICODE_NORM_QC_NO},
	{0x1D761, UNICODE_NORM_QC_NO},
	{0x1D762, UNICODE_NORM_QC_NO},
	{0x1D763, UNICODE_NORM_QC_NO},
	{0x1D764, UNICODE_NORM_QC_NO},
	{0x1D765, UNICODE_NORM_QC_NO},
	{0x1D766, UNICODE_NORM_QC_NO},
	{0x1D767, UNICODE_NORM_QC_NO},
	{0x1D768, UNICODE_NORM_QC_NO},
	{0x1D769, UNICODE_NORM_QC_NO},
	{0x1D76A, UNICODE_NORM_QC_NO},
	{0x1D76B, UNICODE_NORM_QC_NO},
	{0x1D76C, UNICODE_NORM_QC_NO},
	{0x1D76D, UNICODE_NORM_QC_NO},
	{0x1D76E, UNICODE_NORM_QC_NO},
	{0x1D76F, UNICODE_NORM_QC_NO},
	{0x1D770, UNICODE_NORM_QC_NO},
	{0x1D771, UNICODE_NORM_QC_NO},
	{0x1D772, UNICODE_NORM_QC_NO},
	{0x1D773, UNICODE_NORM_QC_NO},
	{0x1D774, UNICODE_NORM_QC_NO},
	{0x1D775, UNICODE_NORM_QC_NO},
	{0x1D776, UNICODE_NORM_QC_NO},
	{0x1D777, UNICODE_NORM_QC_NO},
	{0x1D778, UNICODE_NORM_QC_NO},
	{0x1D779, UNICODE_NORM_QC_NO},
	{0x1D77A, UNICODE_NORM_QC_NO},
	{0x1D77B, UNICODE_NORM_QC_NO},
	{0x1D77C, UNICODE_NORM_QC_NO},
	{0x1D77D, UNICODE_NORM_QC_NO},
	{0x1D77E, UNICODE_NORM_QC_NO},
	{0x1D77F, UNICODE_NORM_QC_NO},
	{0x1D780, UNICODE_NORM_QC_NO},
	{0x1D781, UNICODE_NORM_QC_NO},
	{0x1D782, UNICODE_NORM_QC_NO},
	{0x1D783, UNICODE_NORM_QC_NO},
	{0x1D784, UNICODE_NORM_QC_NO},
	{0x1D785, UNICODE_NORM_QC_NO},
	{0x1D786, UNICODE_NORM_QC_NO},
	{0x1D787, UNICODE_NORM_QC_NO},
	{0x1D788, UNICODE_NORM_QC_NO},
	{0x1D789, UNICODE_NORM_QC_NO},
	{0x1D78A, UNICODE_NORM_QC_NO},
	{0x1D78B, UNICODE_NORM_QC_NO},
	{0x1D78C, UNICODE_NORM_QC_NO},
	{0x1D78D, UNICODE_NORM_QC_NO},
	{0x1D78E, UNICODE_NORM_QC_NO},
	{0x1D78F, UNICODE_NORM_QC_NO},
	{0x1D790, UNICODE_NORM_QC_NO},
	{0x1D791, UNICODE_NORM_QC_NO},
	{0x1D792, UNICODE_NORM_QC_NO},
	{0x1D793, UNICODE_NORM_QC_NO},
	{0x1D794, UNICODE_NORM_QC_NO},
	{0x1D795, UNICODE_NORM_QC_NO},
	{0x1D796, UNICODE_NORM_QC_NO},
	{0x1D797, UNICODE_NORM_QC_NO},
	{0x1D798, UNICODE_NORM_QC_NO},
	{0x1D799, UNICODE_NORM_QC_NO},
	{0x1D79A, UNICODE_NORM_QC_NO},
	{0x1D79B, UNICODE_NORM_QC_NO},
	{0x1D79C, UNICODE_NORM_QC_NO},
	{0x1D79D, UNICODE_NORM_QC_NO},
	{0x1D79E, UNICODE_NORM_QC_NO},
	{0x1D79F, UNICODE_NORM_QC_NO},
	{0x1D7A0, UNICODE_NORM_QC_NO},
	{0x1D7A1, UNICODE_NORM_QC_NO},
	{0x1D7A2, UNICODE_NORM_QC_NO},
	{0x1D7A3, UNICODE_NORM_QC_NO},
	{0x1D7A4, UNICODE_NORM_QC_NO},
	{0x1D7A5, UNICODE_NORM_QC_NO},
	{0x1D7A6, UNICODE_NORM_QC_NO},
	{0x1D7A7, UNICODE_NORM_QC_NO},
	{0x1D7A8, UNICODE_NORM_QC_NO},
	{0x1D7A9, UNICODE_NORM_QC_NO},
	{0x1D7AA, UNICODE_NORM_QC_NO},
	{0x1D7AB, UNICODE_NORM_QC_NO},
	{0x1D7AC, UNICODE_NORM_QC_NO},
	{0x1D7AD, UNICODE_NORM_QC_NO},
	{0x1D7AE, UNICODE_NORM_QC_NO},
	{0x1D7AF, UNICODE_NORM_QC_NO},
	{0x1D7B0, UNICODE_NORM_QC_NO},
	{0x1D7B1, UNICODE_NORM_QC_NO},
	{0x1D7B2, UNICODE_NORM_QC_NO},
	{0x1D7B3, UNICODE_NORM_QC_NO},
	{0x1D7B4, UNICODE_NORM_QC_NO},
	{0x1D7B5, UNICODE_NORM_QC_NO},
	{0x1D7B6, UNICODE_NORM_QC_NO},
	{0x1D7B7, UNICODE_NORM_QC_NO},
	{0x1D7B8, UNICODE_NORM_QC_NO},
	{0x1D7B9, UNICODE_NORM_QC_NO},
	{0x1D7BA, UNICODE_NORM_QC_NO},
	{0x1D7BB, UNICODE_NORM_QC_NO},
	{0x1D7BC, UNICODE_NORM_QC_NO},
	{0x1D7BD, UNICODE_NORM_QC_NO},
	{0x1D7BE, UNICODE_NORM_QC_NO},
	{0x1D7BF, UNICODE_NORM_QC_NO},
	{0x1D7C0, UNICODE_NORM_QC_NO},
	{0x1D7C1, UNICODE_NORM_QC_NO},
	{0x1D7C2, UNICODE_NORM_QC_NO},
	{0x1D7C3, UNICODE_NORM_QC_NO},
	{0x1D7C4, UNICODE_NORM_QC_NO},
	{0x1D7C5, UNICODE_NORM_QC_NO},
	{0x1D7C6, UNICODE_NORM_QC_NO},
	{0x1D7C7, UNICODE_NORM_QC_NO},
	{0x1D7C8, UNICODE_NORM_QC_NO},
	{0x1D7C9, UNICODE_NORM_QC_NO},
	{0x1D7CA, UNICODE_NORM_QC_NO},
	{0x1D7CB, UNICODE_NORM_QC_NO},
	{0x1D7CE, UNICODE_NORM_QC_NO},
	{0x1D7CF, UNICODE_NORM_QC_NO},
	{0x1D7D0, UNICODE_NORM_QC_NO},
	{0x1D7D1, UNICODE_NORM_QC_NO},
	{0x1D7D2, UNICODE_NORM_QC_NO},
	{0x1D7D3, UNICODE_NORM_QC_NO},
	{0x1D7D4, UNICODE_NORM_QC_NO},
	{0x1D7D5, UNICODE_NORM_QC_NO},
	{0x1D7D6, UNICODE_NORM_QC_NO},
	{0x1D7D7, UNICODE_NORM_QC_NO},
	{0x1D7D8, UNICODE_NORM_QC_NO},
	{0x1D7D9, UNICODE_NORM_QC_NO},
	{0x1D7DA, UNICODE_NORM_QC_NO},
	{0x1D7DB, UNICODE_NORM_QC_NO},
	{0x1D7DC, UNICODE_NORM_QC_NO},
	{0x1D7DD, UNICODE_NORM_QC_NO},
	{0x1D7DE, UNICODE_NORM_QC_NO},
	{0x1D7DF, UNICODE_NORM_QC_NO},
	{0x1D7E0, UNICODE_NORM_QC_NO},
	{0x1D7E1, UNICODE_NORM_QC_NO},
	{0x1D7E2, UNICODE_NORM_QC_NO},
	{0x1D7E3, UNICODE_NORM_QC_NO},
	{0x1D7E4, UNICODE_NORM_QC_NO},
	{0x1D7E5, UNICODE_NORM_QC_NO},
	{0x1D7E6, UNICODE_NORM_QC_NO},
	{0x1D7E7, UNICODE_NORM_QC_NO},
	{0x1D7E8, UNICODE_NORM_QC_NO},
	{0x1D7E9, UNICODE_NORM_QC_NO},
	{0x1D7EA, UNICODE_NORM_QC_NO},
	{0x1D7EB, UNICODE_NORM_QC_NO},
	{0x1D7EC, UNICODE_NORM_QC_NO},
	{0x1D7ED, UNICODE_NORM_QC_NO},
	{0x1D7EE, UNICODE_NORM_QC_NO},
	{0x1D7EF, UNICODE_NORM_QC_NO},
	{0x1D7F0, UNICODE_NORM_QC_NO},
	{0x1D7F1, UNICODE_NORM_QC_NO},
	{0x1D7F2, UNICODE_NORM_QC_NO},
	{0x1D7F3, UNICODE_NORM_QC_NO},
	{0x1D7F4, UNICODE_NORM_QC_NO},
	{0x1D7F5, UNICODE_NORM_QC_NO},
	{0x1D7F6, UNICODE_NORM_QC_NO},
	{0x1D7F7, UNICODE_NORM_QC_NO},
	{0x1D7F8, UNICODE_NORM_QC_NO},
	{0x1D7F9, UNICODE_NORM_QC_NO},
	{0x1D7FA, UNICODE_NORM_QC_NO},
	{0x1D7FB, UNICODE_NORM_QC_NO},
	{0x1D7FC, UNICODE_NORM_QC_NO},
	{0x1D7FD, UNICODE_NORM_QC_NO},
	{0x1D7FE, UNICODE_NORM_QC_NO},
	{0x1D7FF, UNICODE_NORM_QC_NO},
	{0x1E030, UNICODE_NORM_QC_NO},
	{0x1E031, UNICODE_NORM_QC_NO},
	{0x1E032, UNICODE_NORM_QC_NO},
	{0x1E033, UNICODE_NORM_QC_NO},
	{0x1E034, UNICODE_NORM_QC_NO},
	{0x1E035, UNICODE_NORM_QC_NO},
	{0x1E036, UNICODE_NORM_QC_NO},
	{0x1E037, UNICODE_NORM_QC_NO},
	{0x1E038, UNICODE_NORM_QC_NO},
	{0x1E039, UNICODE_NORM_QC_NO},
	{0x1E03A, UNICODE_NORM_QC_NO},
	{0x1E03B, UNICODE_NORM_QC_NO},
	{0x1E03C, UNICODE_NORM_QC_NO},
	{0x1E03D, UNICODE_NORM_QC_NO},
	{0x1E03E, UNICODE_NORM_QC_NO},
	{0x1E03F, UNICODE_NORM_QC_NO},
	{0x1E040, UNICODE_NORM_QC_NO},
	{0x1E041, UNICODE_NORM_QC_NO},
	{0x1E042, UNICODE_NORM_QC_NO},
	{0x1E043, UNICODE_NORM_QC_NO},
	{0x1E044, UNICODE_NORM_QC_NO},
	{0x1E045, UNICODE_NORM_QC_NO},
	{0x1E046, UNICODE_NORM_QC_NO},
	{0x1E047, UNICODE_NORM_QC_NO},
	{0x1E048, UNICODE_NORM_QC_NO},
	{0x1E049, UNICODE_NORM_QC_NO},
	{0x1E04A, UNICODE_NORM_QC_NO},
	{0x1E04B, UNICODE_NORM_QC_NO},
	{0x1E04C, UNICODE_NORM_QC_NO},
	{0x1E04D, UNICODE_NORM_QC_NO},
	{0x1E04E, UNICODE_NORM_QC_NO},
	{0x1E04F, UNICODE_NORM_QC_NO},
	{0x1E050, UNICODE_NORM_QC_NO},
	{0x1E051, UNICODE_NORM_QC_NO},
	{0x1E052, UNICODE_NORM_QC_NO},
	{0x1E053, UNICODE_NORM_QC_NO},
	{0x1E054, UNICODE_NORM_QC_NO},
	{0x1E055, UNICODE_NORM_QC_NO},
	{0x1E056, UNICODE_NORM_QC_NO},
	{0x1E057, UNICODE_NORM_QC_NO},
	{0x1E058, UNICODE_NORM_QC_NO},
	{0x1E059, UNICODE_NORM_QC_NO},
	{0x1E05A, UNICODE_NORM_QC_NO},
	{0x1E05B, UNICODE_NORM_QC_NO},
	{0x1E05C, UNICODE_NORM_QC_NO},
	{0x1E05D, UNICODE_NORM_QC_NO},
	{0x1E05E, UNICODE_NORM_QC_NO},
	{0x1E05F, UNICODE_NORM_QC_NO},
	{0x1E060, UNICODE_NORM_QC_NO},
	{0x1E061, UNICODE_NORM_QC_NO},
	{0x1E062, UNICODE_NORM_QC_NO},
	{0x1E063, UNICODE_NORM_QC_NO},
	{0x1E064, UNICODE_NORM_QC_NO},
	{0x1E065, UNICODE_NORM_QC_NO},
	{0x1E066, UNICODE_NORM_QC_NO},
	{0x1E067, UNICODE_NORM_QC_NO},
	{0x1E068, UNICODE_NORM_QC_NO},
	{0x1E069, UNICODE_NORM_QC_NO},
	{0x1E06A, UNICODE_NORM_QC_NO},
	{0x1E06B, UNICODE_NORM_QC_NO},
	{0x1E06C, UNICODE_NORM_QC_NO},
	{0x1E06D, UNICODE_NORM_QC_NO},
	{0x1EE00, UNICODE_NORM_QC_NO},
	{0x1EE01, UNICODE_NORM_QC_NO},
	{0x1EE02, UNICODE_NORM_QC_NO},
	{0x1EE03, UNICODE_NORM_QC_NO},
	{0x1EE05, UNICODE_NORM_QC_NO},
	{0x1EE06, UNICODE_NORM_QC_NO},
	{0x1EE07, UNICODE_NORM_QC_NO},
	{0x1EE08, UNICODE_NORM_QC_NO},
	{0x1EE09, UNICODE_NORM_QC_NO},
	{0x1EE0A, UNICODE_NORM_QC_NO},
	{0x1EE0B, UNICODE_NORM_QC_NO},
	{0x1EE0C, UNICODE_NORM_QC_NO},
	{0x1EE0D, UNICODE_NORM_QC_NO},
	{0x1EE0E, UNICODE_NORM_QC_NO},
	{0x1EE0F, UNICODE_NORM_QC_NO},
	{0x1EE10, UNICODE_NORM_QC_NO},
	{0x1EE11, UNICODE_NORM_QC_NO},
	{0x1EE12, UNICODE_NORM_QC_NO},
	{0x1EE13, UNICODE_NORM_QC_NO},
	{0x1EE14, UNICODE_NORM_QC_NO},
	{0x1EE15, UNICODE_NORM_QC_NO},
	{0x1EE16, UNICODE_NORM_QC_NO},
	{0x1EE17, UNICODE_NORM_QC_NO},
	{0x1EE18, UNICODE_NORM_QC_NO},
	{0x1EE19, UNICODE_NORM_QC_NO},
	{0x1EE1A, UNICODE_NORM_QC_NO},
	{0x1EE1B, UNICODE_NORM_QC_NO},
	{0x1EE1C, UNICODE_NORM_QC_NO},
	{0x1EE1D, UNICODE_NORM_QC_NO},
	{0x1EE1E, UNICODE_NORM_QC_NO},
	{0x1EE1F, UNICODE_NORM_QC_NO},
	{0x1EE21, UNICODE_NORM_QC_NO},
	{0x1EE22, UNICODE_NORM_QC_NO},
	{0x1EE24, UNICODE_NORM_QC_NO},
	{0x1EE27, UNICODE_NORM_QC_NO},
	{0x1EE29, UNICODE_NORM_QC_NO},
	{0x1EE2A, UNICODE_NORM_QC_NO},
	{0x1EE2B, UNICODE_NORM_QC_NO},
	{0x1EE2C, UNICODE_NORM_QC_NO},
	{0x1EE2D, UNICODE_NORM_QC_NO},
	{0x1EE2E, UNICODE_NORM_QC_NO},
	{0x1EE2F, UNICODE_NORM_QC_NO},
	{0x1EE30, UNICODE_NORM_QC_NO},
	{0x1EE31, UNICODE_NORM_QC_NO},
	{0x1EE32, UNICODE_NORM_QC_NO},
	{0x1EE34, UNICODE_NORM_QC_NO},
	{0x1EE35, UNICODE_NORM_QC_NO},
	{0x1EE36, UNICODE_NORM_QC_NO},
	{0x1EE37, UNICODE_NORM_QC_NO},
	{0x1EE39, UNICODE_NORM_QC_NO},
	{0x1EE3B, UNICODE_NORM_QC_NO},
	{0x1EE42, UNICODE_NORM_QC_NO},
	{0x1EE47, UNICODE_NORM_QC_NO},
	{0x1EE49, UNICODE_NORM_QC_NO},
	{0x1EE4B, UNICODE_NORM_QC_NO},
	{0x1EE4D, UNICODE_NORM_QC_NO},
	{0x1EE4E, UNICODE_NORM_QC_NO},
	{0x1EE4F, UNICODE_NORM_QC_NO},
	{0x1EE51, UNICODE_NORM_QC_NO},
	{0x1EE52, UNICODE_NORM_QC_NO},
	{0x1EE54, UNICODE_NORM_QC_NO},
	{0x1EE57, UNICODE_NORM_QC_NO},
	{0x1EE59, UNICODE_NORM_QC_NO},
	{0x1EE5B, UNICODE_NORM_QC_NO},
	{0x1EE5D, UNICODE_NORM_QC_NO},
	{0x1EE5F, UNICODE_NORM_QC_NO},
	{0x1EE61, UNICODE_NORM_QC_NO},
	{0x1EE62, UNICODE_NORM_QC_NO},
	{0x1EE64, UNICODE_NORM_QC_NO},
	{0x1EE67, UNICODE_NORM_QC_NO},
	{0x1EE68, UNICODE_NORM_QC_NO},
	{0x1EE69, UNICODE_NORM_QC_NO},
	{0x1EE6A, UNICODE_NORM_QC_NO},
	{0x1EE6C, UNICODE_NORM_QC_NO},
	{0x1EE6D, UNICODE_NORM_QC_NO},
	{0x1EE6E, UNICODE_NORM_QC_NO},
	{0x1EE6F, UNICODE_NORM_QC_NO},
	{0x1EE70, UNICODE_NORM_QC_NO},
	{0x1EE71, UNICODE_NORM_QC_NO},
	{0x1EE72, UNICODE_NORM_QC_NO},
	{0x1EE74, UNICODE_NORM_QC_NO},
	{0x1EE75, UNICODE_NORM_QC_NO},
	{0x1EE76, UNICODE_NORM_QC_NO},
	{0x1EE77, UNICODE_NORM_QC_NO},
	{0x1EE79, UNICODE_NORM_QC_NO},
	{0x1EE7A, UNICODE_NORM_QC_NO},
	{0x1EE7B, UNICODE_NORM_QC_NO},
	{0x1EE7C, UNICODE_NORM_QC_NO},
	{0x1EE7E, UNICODE_NORM_QC_NO},
	{0x1EE80, UNICODE_NORM_QC_NO},
	{0x1EE81, UNICODE_NORM_QC_NO},
	{0x1EE82, UNICODE_NORM_QC_NO},
	{0x1EE83, UNICODE_NORM_QC_NO},
	{0x1EE84, UNICODE_NORM_QC_NO},
	{0x1EE85, UNICODE_NORM_QC_NO},
	{0x1EE86, UNICODE_NORM_QC_NO},
	{0x1EE87, UNICODE_NORM_QC_NO},
	{0x1EE88, UNICODE_NORM_QC_NO},
	{0x1EE89, UNICODE_NORM_QC_NO},
	{0x1EE8B, UNICODE_NORM_QC_NO},
	{0x1EE8C, UNICODE_NORM_QC_NO},
	{0x1EE8D, UNICODE_NORM_QC_NO},
	{0x1EE8E, UNICODE_NORM_QC_NO},
	{0x1EE8F, UNICODE_NORM_QC_NO},
	{0x1EE90, UNICODE_NORM_QC_NO},
	{0x1EE91, UNICODE_NORM_QC_NO},
	{0x1EE92, UNICODE_NORM_QC_NO},
	{0x1EE93, UNICODE_NORM_QC_NO},
	{0x1EE94, UNICODE_NORM_QC_NO},
	{0x1EE95, UNICODE_NORM_QC_NO},
	{0x1EE96, UNICODE_NORM_QC_NO},
	{0x1EE97, UNICODE_NORM_QC_NO},
	{0x1EE98, UNICODE_NORM_QC_NO},
	{0x1EE99, UNICODE_NORM_QC_NO},
	{0x1EE9A, UNICODE_NORM_QC_NO},
	{0x1EE9B, UNICODE_NORM_QC_NO},
	{0x1EEA1, UNICODE_NORM_QC_NO},
	{0x1EEA2, UNICODE_NORM_QC_NO},
	{0x1EEA3, UNICODE_NORM_QC_NO},
	{0x1EEA5, UNICODE_NORM_QC_NO},
	{0x1EEA6, UNICODE_NORM_QC_NO},
	{0x1EEA7, UNICODE_NORM_QC_NO},
	{0x1EEA8, UNICODE_NORM_QC_NO},
	{0x1EEA9, UNICODE_NORM_QC_NO},
	{0x1EEAB, UNICODE_NORM_QC_NO},
	{0x1EEAC, UNICODE_NORM_QC_NO},
	{0x1EEAD, UNICODE_NORM_QC_NO},
	{0x1EEAE, UNICODE_NORM_QC_NO},
	{0x1EEAF, UNICODE_NORM_QC_NO},
	{0x1EEB0, UNICODE_NORM_QC_NO},
	{0x1EEB1, UNICODE_NORM_QC_NO},
	{0x1EEB2, UNICODE_NORM_QC_NO},
	{0x1EEB3, UNICODE_NORM_QC_NO},
	{0x1EEB4, UNICODE_NORM_QC_NO},
	{0x1EEB5, UNICODE_NORM_QC_NO},
	{0x1EEB6, UNICODE_NORM_QC_NO},
	{0x1EEB7, UNICODE_NORM_QC_NO},
	{0x1EEB8, UNICODE_NORM_QC_NO},
	{0x1EEB9, UNICODE_NORM_QC_NO},
	{0x1EEBA, UNICODE_NORM_QC_NO},
	{0x1EEBB, UNICODE_NORM_QC_NO},
	{0x1F100, UNICODE_NORM_QC_NO},
	{0x1F101, UNICODE_NORM_QC_NO},
	{0x1F102, UNICODE_NORM_QC_NO},
	{0x1F103, UNICODE_NORM_QC_NO},
	{0x1F104, UNICODE_NORM_QC_NO},
	{0x1F105, UNICODE_NORM_QC_NO},
	{0x1F106, UNICODE_NORM_QC_NO},
	{0x1F107, UNICODE_NORM_QC_NO},
	{0x1F108, UNICODE_NORM_QC_NO},
	{0x1F109, UNICODE_NORM_QC_NO},
	{0x1F10A, UNICODE_NORM_QC_NO},
	{0x1F110, UNICODE_NORM_QC_NO},
	{0x1F111, UNICODE_NORM_QC_NO},
	{0x1F112, UNICODE_NORM_QC_NO},
	{0x1F113, UNICODE_NORM_QC_NO},
	{0x1F114, UNICODE_NORM_QC_NO},
	{0x1F115, UNICODE_NORM_QC_NO},
	{0x1F116, UNICODE_NORM_QC_NO},
	{0x1F117, UNICODE_NORM_QC_NO},
	{0x1F118, UNICODE_NORM_QC_NO},
	{0x1F119, UNICODE_NORM_QC_NO},
	{0x1F11A, UNICODE_NORM_QC_NO},
	{0x1F11B, UNICODE_NORM_QC_NO},
	{0x1F11C, UNICODE_NORM_QC_NO},
	{0x1F11D, UNICODE_NORM_QC_NO},
	{0x1F11E, UNICODE_NORM_QC_NO},
	{0x1F11F, UNICODE_NORM_QC_NO},
	{0x1F120, UNICODE_NORM_QC_NO},
	{0x1F121, UNICODE_NORM_QC_NO},
	{0x1F122, UNICODE_NORM_QC_NO},
	{0x1F123, UNICODE_NORM_QC_NO},
	{0x1F124, UNICODE_NORM_QC_NO},
	{0x1F125, UNICODE_NORM_QC_NO},
	{0x1F126, UNICODE_NORM_QC_NO},
	{0x1F127, UNICODE_NORM_QC_NO},
	{0x1F128, UNICODE_NORM_QC_NO},
	{0x1F129, UNICODE_NORM_QC_NO},
	{0x1F12A, UNICODE_NORM_QC_NO},
	{0x1F12B, UNICODE_NORM_QC_NO},
	{0x1F12C, UNICODE_NORM_QC_NO},
	{0x1F12D, UNICODE_NORM_QC_NO},
	{0x1F12E, UNICODE_NORM_QC_NO},
	{0x1F130, UNICODE_NORM_QC_NO},
	{0x1F131, UNICODE_NORM_QC_NO},
	{0x1F132, UNICODE_NORM_QC_NO},
	{0x1F133, UNICODE_NORM_QC_NO},
	{0x1F134, UNICODE_NORM_QC_NO},
	{0x1F135, UNICODE_NORM_QC_NO},
	{0x1F136, UNICODE_NORM_QC_NO},
	{0x1F137, UNICODE_NORM_QC_NO},
	{0x1F138, UNICODE_NORM_QC_NO},
	{0x1F139, UNICODE_NORM_QC_NO},
	{0x1F13A, UNICODE_NORM_QC_NO},
	{0x1F13B, UNICODE_NORM_QC_NO},
	{0x1F13C, UNICODE_NORM_QC_NO},
	{0x1F13D, UNICODE_NORM_QC_NO},
	{0x1F13E, UNICODE_NORM_QC_NO},
	{0x1F13F, UNICODE_NORM_QC_NO},
	{0x1F140, UNICODE_NORM_QC_NO},
	{0x1F141, UNICODE_NORM_QC_NO},
	{0x1F142, UNICODE_NORM_QC_NO},
	{0x1F143, UNICODE_NORM_QC_NO},
	{0x1F144, UNICODE_NORM_QC_NO},
	{0x1F145, UNICODE_NORM_QC_NO},
	{0x1F146, UNICODE_NORM_QC_NO},
	{0x1F147, UNICODE_NORM_QC_NO},
	{0x1F148, UNICODE_NORM_QC_NO},
	{0x1F149, UNICODE_NORM_QC_NO},
	{0x1F14A, UNICODE_NORM_QC_NO},
	{0x1F14B, UNICODE_NORM_QC_NO},
	{0x1F14C, UNICODE_NORM_QC_NO},
	{0x1F14D, UNICODE_NORM_QC_NO},
	{0x1F14E, UNICODE_NORM_QC_NO},
	{0x1F14F, UNICODE_NORM_QC_NO},
	{0x1F16A, UNICODE_NORM_QC_NO},
	{0x1F16B, UNICODE_NORM_QC_NO},
	{0x1F16C, UNICODE_NORM_QC_NO},
	{0x1F190, UNICODE_NORM_QC_NO},
	{0x1F200, UNICODE_NORM_QC_NO},
	{0x1F201, UNICODE_NORM_QC_NO},
	{0x1F202, UNICODE_NORM_QC_NO},
	{0x1F210, UNICODE_NORM_QC_NO},
	{0x1F211, UNICODE_NORM_QC_NO},
	{0x1F212, UNICODE_NORM_QC_NO},
	{0x1F213, UNICODE_NORM_QC_NO},
	{0x1F214, UNICODE_NORM_QC_NO},
	{0x1F215, UNICODE_NORM_QC_NO},
	{0x1F216, UNICODE_NORM_QC_NO},
	{0x1F217, UNICODE_NORM_QC_NO},
	{0x1F218, UNICODE_NORM_QC_NO},
	{0x1F219, UNICODE_NORM_QC_NO},
	{0x1F21A, UNICODE_NORM_QC_NO},
	{0x1F21B, UNICODE_NORM_QC_NO},
	{0x1F21C, UNICODE_NORM_QC_NO},
	{0x1F21D, UNICODE_NORM_QC_NO},
	{0x1F21E, UNICODE_NORM_QC_NO},
	{0x1F21F, UNICODE_NORM_QC_NO},
	{0x1F220, UNICODE_NORM_QC_NO},
	{0x1F221, UNICODE_NORM_QC_NO},
	{0x1F222, UNICODE_NORM_QC_NO},
	{0x1F223, UNICODE_NORM_QC_NO},
	{0x1F224, UNICODE_NORM_QC_NO},
	{0x1F225, UNICODE_NORM_QC_NO},
	{0x1F226, UNICODE_NORM_QC_NO},
	{0x1F227, UNICODE_NORM_QC_NO},
	{0x1F228, UNICODE_NORM_QC_NO},
	{0x1F229, UNICODE_NORM_QC_NO},
	{0x1F22A, UNICODE_NORM_QC_NO},
	{0x1F22B, UNICODE_NORM_QC_NO},
	{0x1F22C, UNICODE_NORM_QC_NO},
	{0x1F22D, UNICODE_NORM_QC_NO},
	{0x1F22E, UNICODE_NORM_QC_NO},
	{0x1F22F, UNICODE_NORM_QC_NO},
	{0x1F230, UNICODE_NORM_QC_NO},
	{0x1F231, UNICODE_NORM_QC_NO},
	{0x1F232, UNICODE_NORM_QC_NO},
	{0x1F233, UNICODE_NORM_QC_NO},
	{0x1F234, UNICODE_NORM_QC_NO},
	{0x1F235, UNICODE_NORM_QC_NO},
	{0x1F236, UNICODE_NORM_QC_NO},
	{0x1F237, UNICODE_NORM_QC_NO},
	{0x1F238, UNICODE_NORM_QC_NO},
	{0x1F239, UNICODE_NORM_QC_NO},
	{0x1F23A, UNICODE_NORM_QC_NO},
	{0x1F23B, UNICODE_NORM_QC_NO},
	{0x1F240, UNICODE_NORM_QC_NO},
	{0x1F241, UNICODE_NORM_QC_NO},
	{0x1F242, UNICODE_NORM_QC_NO},
	{0x1F243, UNICODE_NORM_QC_NO},
	{0x1F244, UNICODE_NORM_QC_NO},
	{0x1F245, UNICODE_NORM_QC_NO},
	{0x1F246, UNICODE_NORM_QC_NO},
	{0x1F247, UNICODE_NORM_QC_NO},
	{0x1F248, UNICODE_NORM_QC_NO},
	{0x1F250, UNICODE_NORM_QC_NO},
	{0x1F251, UNICODE_NORM_QC_NO},
	{0x1FBF0, UNICODE_NORM_QC_NO},
	{0x1FBF1, UNICODE_NORM_QC_NO},
	{0x1FBF2, UNICODE_NORM_QC_NO},
	{0x1FBF3, UNICODE_NORM_QC_NO},
	{0x1FBF4, UNICODE_NORM_QC_NO},
	{0x1FBF5, UNICODE_NORM_QC_NO},
	{0x1FBF6, UNICODE_NORM_QC_NO},
	{0x1FBF7, UNICODE_NORM_QC_NO},
	{0x1FBF8, UNICODE_NORM_QC_NO},
	{0x1FBF9, UNICODE_NORM_QC_NO},
	{0x2F800, UNICODE_NORM_QC_NO},
	{0x2F801, UNICODE_NORM_QC_NO},
	{0x2F802, UNICODE_NORM_QC_NO},
	{0x2F803, UNICODE_NORM_QC_NO},
	{0x2F804, UNICODE_NORM_QC_NO},
	{0x2F805, UNICODE_NORM_QC_NO},
	{0x2F806, UNICODE_NORM_QC_NO},
	{0x2F807, UNICODE_NORM_QC_NO},
	{0x2F808, UNICODE_NORM_QC_NO},
	{0x2F809, UNICODE_NORM_QC_NO},
	{0x2F80A, UNICODE_NORM_QC_NO},
	{0x2F80B, UNICODE_NORM_QC_NO},
	{0x2F80C, UNICODE_NORM_QC_NO},
	{0x2F80D, UNICODE_NORM_QC_NO},
	{0x2F80E, UNICODE_NORM_QC_NO},
	{0x2F80F, UNICODE_NORM_QC_NO},
	{0x2F810, UNICODE_NORM_QC_NO},
	{0x2F811, UNICODE_NORM_QC_NO},
	{0x2F812, UNICODE_NORM_QC_NO},
	{0x2F813, UNICODE_NORM_QC_NO},
	{0x2F814, UNICODE_NORM_QC_NO},
	{0x2F815, UNICODE_NORM_QC_NO},
	{0x2F816, UNICODE_NORM_QC_NO},
	{0x2F817, UNICODE_NORM_QC_NO},
	{0x2F818, UNICODE_NORM_QC_NO},
	{0x2F819, UNICODE_NORM_QC_NO},
	{0x2F81A, UNICODE_NORM_QC_NO},
	{0x2F81B, UNICODE_NORM_QC_NO},
	{0x2F81C, UNICODE_NORM_QC_NO},
	{0x2F81D, UNICODE_NORM_QC_NO},
	{0x2F81E, UNICODE_NORM_QC_NO},
	{0x2F81F, UNICODE_NORM_QC_NO},
	{0x2F820, UNICODE_NORM_QC_NO},
	{0x2F821, UNICODE_NORM_QC_NO},
	{0x2F822, UNICODE_NORM_QC_NO},
	{0x2F823, UNICODE_NORM_QC_NO},
	{0x2F824, UNICODE_NORM_QC_NO},
	{0x2F825, UNICODE_NORM_QC_NO},
	{0x2F826, UNICODE_NORM_QC_NO},
	{0x2F827, UNICODE_NORM_QC_NO},
	{0x2F828, UNICODE_NORM_QC_NO},
	{0x2F829, UNICODE_NORM_QC_NO},
	{0x2F82A, UNICODE_NORM_QC_NO},
	{0x2F82B, UNICODE_NORM_QC_NO},
	{0x2F82C, UNICODE_NORM_QC_NO},
	{0x2F82D, UNICODE_NORM_QC_NO},
	{0x2F82E, UNICODE_NORM_QC_NO},
	{0x2F82F, UNICODE_NORM_QC_NO},
	{0x2F830, UNICODE_NORM_QC_NO},
	{0x2F831, UNICODE_NORM_QC_NO},
	{0x2F832, UNICODE_NORM_QC_NO},
	{0x2F833, UNICODE_NORM_QC_NO},
	{0x2F834, UNICODE_NORM_QC_NO},
	{0x2F835, UNICODE_NORM_QC_NO},
	{0x2F836, UNICODE_NORM_QC_NO},
	{0x2F837, UNICODE_NORM_QC_NO},
	{0x2F838, UNICODE_NORM_QC_NO},
	{0x2F839, UNICODE_NORM_QC_NO},
	{0x2F83A, UNICODE_NORM_QC_NO},
	{0x2F83B, UNICODE_NORM_QC_NO},
	{0x2F83C, UNICODE_NORM_QC_NO},
	{0x2F83D, UNICODE_NORM_QC_NO},
	{0x2F83E, UNICODE_NORM_QC_NO},
	{0x2F83F, UNICODE_NORM_QC_NO},
	{0x2F840, UNICODE_NORM_QC_NO},
	{0x2F841, UNICODE_NORM_QC_NO},
	{0x2F842, UNICODE_NORM_QC_NO},
	{0x2F843, UNICODE_NORM_QC_NO},
	{0x2F844, UNICODE_NORM_QC_NO},
	{0x2F845, UNICODE_NORM_QC_NO},
	{0x2F846, UNICODE_NORM_QC_NO},
	{0x2F847, UNICODE_NORM_QC_NO},
	{0x2F848, UNICODE_NORM_QC_NO},
	{0x2F849, UNICODE_NORM_QC_NO},
	{0x2F84A, UNICODE_NORM_QC_NO},
	{0x2F84B, UNICODE_NORM_QC_NO},
	{0x2F84C, UNICODE_NORM_QC_NO},
	{0x2F84D, UNICODE_NORM_QC_NO},
	{0x2F84E, UNICODE_NORM_QC_NO},
	{0x2F84F, UNICODE_NORM_QC_NO},
	{0x2F850, UNICODE_NORM_QC_NO},
	{0x2F851, UNICODE_NORM_QC_NO},
	{0x2F852, UNICODE_NORM_QC_NO},
	{0x2F853, UNICODE_NORM_QC_NO},
	{0x2F854, UNICODE_NORM_QC_NO},
	{0x2F855, UNICODE_NORM_QC_NO},
	{0x2F856, UNICODE_NORM_QC_NO},
	{0x2F857, UNICODE_NORM_QC_NO},
	{0x2F858, UNICODE_NORM_QC_NO},
	{0x2F859, UNICODE_NORM_QC_NO},
	{0x2F85A, UNICODE_NORM_QC_NO},
	{0x2F85B, UNICODE_NORM_QC_NO},
	{0x2F85C, UNICODE_NORM_QC_NO},
	{0x2F85D, UNICODE_NORM_QC_NO},
	{0x2F85E, UNICODE_NORM_QC_NO},
	{0x2F85F, UNICODE_NORM_QC_NO},
	{0x2F860, UNICODE_NORM_QC_NO},
	{0x2F861, UNICODE_NORM_QC_NO},
	{0x2F862, UNICODE_NORM_QC_NO},
	{0x2F863, UNICODE_NORM_QC_NO},
	{0x2F864, UNICODE_NORM_QC_NO},
	{0x2F865, UNICODE_NORM_QC_NO},
	{0x2F866, UNICODE_NORM_QC_NO},
	{0x2F867, UNICODE_NORM_QC_NO},
	{0x2F868, UNICODE_NORM_QC_NO},
	{0x2F869, UNICODE_NORM_QC_NO},
	{0x2F86A, UNICODE_NORM_QC_NO},
	{0x2F86B, UNICODE_NORM_QC_NO},
	{0x2F86C, UNICODE_NORM_QC_NO},
	{0x2F86D, UNICODE_NORM_QC_NO},
	{0x2F86E, UNICODE_NORM_QC_NO},
	{0x2F86F, UNICODE_NORM_QC_NO},
	{0x2F870, UNICODE_NORM_QC_NO},
	{0x2F871, UNICODE_NORM_QC_NO},
	{0x2F872, UNICODE_NORM_QC_NO},
	{0x2F873, UNICODE_NORM_QC_NO},
	{0x2F874, UNICODE_NORM_QC_NO},
	{0x2F875, UNICODE_NORM_QC_NO},
	{0x2F876, UNICODE_NORM_QC_NO},
	{0x2F877, UNICODE_NORM_QC_NO},
	{0x2F878, UNICODE_NORM_QC_NO},
	{0x2F879, UNICODE_NORM_QC_NO},
	{0x2F87A, UNICODE_NORM_QC_NO},
	{0x2F87B, UNICODE_NORM_QC_NO},
	{0x2F87C, UNICODE_NORM_QC_NO},
	{0x2F87D, UNICODE_NORM_QC_NO},
	{0x2F87E, UNICODE_NORM_QC_NO},
	{0x2F87F, UNICODE_NORM_QC_NO},
	{0x2F880, UNICODE_NORM_QC_NO},
	{0x2F881, UNICODE_NORM_QC_NO},
	{0x2F882, UNICODE_NORM_QC_NO},
	{0x2F883, UNICODE_NORM_QC_NO},
	{0x2F884, UNICODE_NORM_QC_NO},
	{0x2F885, UNICODE_NORM_QC_NO},
	{0x2F886, UNICODE_NORM_QC_NO},
	{0x2F887, UNICODE_NORM_QC_NO},
	{0x2F888, UNICODE_NORM_QC_NO},
	{0x2F889, UNICODE_NORM_QC_NO},
	{0x2F88A, UNICODE_NORM_QC_NO},
	{0x2F88B, UNICODE_NORM_QC_NO},
	{0x2F88C, UNICODE_NORM_QC_NO},
	{0x2F88D, UNICODE_NORM_QC_NO},
	{0x2F88E, UNICODE_NORM_QC_NO},
	{0x2F88F, UNICODE_NORM_QC_NO},
	{0x2F890, UNICODE_NORM_QC_NO},
	{0x2F891, UNICODE_NORM_QC_NO},
	{0x2F892, UNICODE_NORM_QC_NO},
	{0x2F893, UNICODE_NORM_QC_NO},
	{0x2F894, UNICODE_NORM_QC_NO},
	{0x2F895, UNICODE_NORM_QC_NO},
	{0x2F896, UNICODE_NORM_QC_NO},
	{0x2F897, UNICODE_NORM_QC_NO},
	{0x2F898, UNICODE_NORM_QC_NO},
	{0x2F899, UNICODE_NORM_QC_NO},
	{0x2F89A, UNICODE_NORM_QC_NO},
	{0x2F89B, UNICODE_NORM_QC_NO},
	{0x2F89C, UNICODE_NORM_QC_NO},
	{0x2F89D, UNICODE_NORM_QC_NO},
	{0x2F89E, UNICODE_NORM_QC_NO},
	{0x2F89F, UNICODE_NORM_QC_NO},
	{0x2F8A0, UNICODE_NORM_QC_NO},
	{0x2F8A1, UNICODE_NORM_QC_NO},
	{0x2F8A2, UNICODE_NORM_QC_NO},
	{0x2F8A3, UNICODE_NORM_QC_NO},
	{0x2F8A4, UNICODE_NORM_QC_NO},
	{0x2F8A5, UNICODE_NORM_QC_NO},
	{0x2F8A6, UNICODE_NORM_QC_NO},
	{0x2F8A7, UNICODE_NORM_QC_NO},
	{0x2F8A8, UNICODE_NORM_QC_NO},
	{0x2F8A9, UNICODE_NORM_QC_NO},
	{0x2F8AA, UNICODE_NORM_QC_NO},
	{0x2F8AB, UNICODE_NORM_QC_NO},
	{0x2F8AC, UNICODE_NORM_QC_NO},
	{0x2F8AD, UNICODE_NORM_QC_NO},
	{0x2F8AE, UNICODE_NORM_QC_NO},
	{0x2F8AF, UNICODE_NORM_QC_NO},
	{0x2F8B0, UNICODE_NORM_QC_NO},
	{0x2F8B1, UNICODE_NORM_QC_NO},
	{0x2F8B2, UNICODE_NORM_QC_NO},
	{0x2F8B3, UNICODE_NORM_QC_NO},
	{0x2F8B4, UNICODE_NORM_QC_NO},
	{0x2F8B5, UNICODE_NORM_QC_NO},
	{0x2F8B6, UNICODE_NORM_QC_NO},
	{0x2F8B7, UNICODE_NORM_QC_NO},
	{0x2F8B8, UNICODE_NORM_QC_NO},
	{0x2F8B9, UNICODE_NORM_QC_NO},
	{0x2F8BA, UNICODE_NORM_QC_NO},
	{0x2F8BB, UNICODE_NORM_QC_NO},
	{0x2F8BC, UNICODE_NORM_QC_NO},
	{0x2F8BD, UNICODE_NORM_QC_NO},
	{0x2F8BE, UNICODE_NORM_QC_NO},
	{0x2F8BF, UNICODE_NORM_QC_NO},
	{0x2F8C0, UNICODE_NORM_QC_NO},
	{0x2F8C1, UNICODE_NORM_QC_NO},
	{0x2F8C2, UNICODE_NORM_QC_NO},
	{0x2F8C3, UNICODE_NORM_QC_NO},
	{0x2F8C4, UNICODE_NORM_QC_NO},
	{0x2F8C5, UNICODE_NORM_QC_NO},
	{0x2F8C6, UNICODE_NORM_QC_NO},
	{0x2F8C7, UNICODE_NORM_QC_NO},
	{0x2F8C8, UNICODE_NORM_QC_NO},
	{0x2F8C9, UNICODE_NORM_QC_NO},
	{0x2F8CA, UNICODE_NORM_QC_NO},
	{0x2F8CB, UNICODE_NORM_QC_NO},
	{0x2F8CC, UNICODE_NORM_QC_NO},
	{0x2F8CD, UNICODE_NORM_QC_NO},
	{0x2F8CE, UNICODE_NORM_QC_NO},
	{0x2F8CF, UNICODE_NORM_QC_NO},
	{0x2F8D0, UNICODE_NORM_QC_NO},
	{0x2F8D1, UNICODE_NORM_QC_NO},
	{0x2F8D2, UNICODE_NORM_QC_NO},
	{0x2F8D3, UNICODE_NORM_QC_NO},
	{0x2F8D4, UNICODE_NORM_QC_NO},
	{0x2F8D5, UNICODE_NORM_QC_NO},
	{0x2F8D6, UNICODE_NORM_QC_NO},
	{0x2F8D7, UNICODE_NORM_QC_NO},
	{0x2F8D8, UNICODE_NORM_QC_NO},
	{0x2F8D9, UNICODE_NORM_QC_NO},
	{0x2F8DA, UNICODE_NORM_QC_NO},
	{0x2F8DB, UNICODE_NORM_QC_NO},
	{0x2F8DC, UNICODE_NORM_QC_NO},
	{0x2F8DD, UNICODE_NORM_QC_NO},
	{0x2F8DE, UNICODE_NORM_QC_NO},
	{0x2F8DF, UNICODE_NORM_QC_NO},
	{0x2F8E0, UNICODE_NORM_QC_NO},
	{0x2F8E1, UNICODE_NORM_QC_NO},
	{0x2F8E2, UNICODE_NORM_QC_NO},
	{0x2F8E3, UNICODE_NORM_QC_NO},
	{0x2F8E4, UNICODE_NORM_QC_NO},
	{0x2F8E5, UNICODE_NORM_QC_NO},
	{0x2F8E6, UNICODE_NORM_QC_NO},
	{0x2F8E7, UNICODE_NORM_QC_NO},
	{0x2F8E8, UNICODE_NORM_QC_NO},
	{0x2F8E9, UNICODE_NORM_QC_NO},
	{0x2F8EA, UNICODE_NORM_QC_NO},
	{0x2F8EB, UNICODE_NORM_QC_NO},
	{0x2F8EC, UNICODE_NORM_QC_NO},
	{0x2F8ED, UNICODE_NORM_QC_NO},
	{0x2F8EE, UNICODE_NORM_QC_NO},
	{0x2F8EF, UNICODE_NORM_QC_NO},
	{0x2F8F0, UNICODE_NORM_QC_NO},
	{0x2F8F1, UNICODE_NORM_QC_NO},
	{0x2F8F2, UNICODE_NORM_QC_NO},
	{0x2F8F3, UNICODE_NORM_QC_NO},
	{0x2F8F4, UNICODE_NORM_QC_NO},
	{0x2F8F5, UNICODE_NORM_QC_NO},
	{0x2F8F6, UNICODE_NORM_QC_NO},
	{0x2F8F7, UNICODE_NORM_QC_NO},
	{0x2F8F8, UNICODE_NORM_QC_NO},
	{0x2F8F9, UNICODE_NORM_QC_NO},
	{0x2F8FA, UNICODE_NORM_QC_NO},
	{0x2F8FB, UNICODE_NORM_QC_NO},
	{0x2F8FC, UNICODE_NORM_QC_NO},
	{0x2F8FD, UNICODE_NORM_QC_NO},
	{0x2F8FE, UNICODE_NORM_QC_NO},
	{0x2F8FF, UNICODE_NORM_QC_NO},
	{0x2F900, UNICODE_NORM_QC_NO},
	{0x2F901, UNICODE_NORM_QC_NO},
	{0x2F902, UNICODE_NORM_QC_NO},
	{0x2F903, UNICODE_NORM_QC_NO},
	{0x2F904, UNICODE_NORM_QC_NO},
	{0x2F905, UNICODE_NORM_QC_NO},
	{0x2F906, UNICODE_NORM_QC_NO},
	{0x2F907, UNICODE_NORM_QC_NO},
	{0x2F908, UNICODE_NORM_QC_NO},
	{0x2F909, UNICODE_NORM_QC_NO},
	{0x2F90A, UNICODE_NORM_QC_NO},
	{0x2F90B, UNICODE_NORM_QC_NO},
	{0x2F90C, UNICODE_NORM_QC_NO},
	{0x2F90D, UNICODE_NORM_QC_NO},
	{0x2F90E, UNICODE_NORM_QC_NO},
	{0x2F90F, UNICODE_NORM_QC_NO},
	{0x2F910, UNICODE_NORM_QC_NO},
	{0x2F911, UNICODE_NORM_QC_NO},
	{0x2F912, UNICODE_NORM_QC_NO},
	{0x2F913, UNICODE_NORM_QC_NO},
	{0x2F914, UNICODE_NORM_QC_NO},
	{0x2F915, UNICODE_NORM_QC_NO},
	{0x2F916, UNICODE_NORM_QC_NO},
	{0x2F917, UNICODE_NORM_QC_NO},
	{0x2F918, UNICODE_NORM_QC_NO},
	{0x2F919, UNICODE_NORM_QC_NO},
	{0x2F91A, UNICODE_NORM_QC_NO},
	{0x2F91B, UNICODE_NORM_QC_NO},
	{0x2F91C, UNICODE_NORM_QC_NO},
	{0x2F91D, UNICODE_NORM_QC_NO},
	{0x2F91E, UNICODE_NORM_QC_NO},
	{0x2F91F, UNICODE_NORM_QC_NO},
	{0x2F920, UNICODE_NORM_QC_NO},
	{0x2F921, UNICODE_NORM_QC_NO},
	{0x2F922, UNICODE_NORM_QC_NO},
	{0x2F923, UNICODE_NORM_QC_NO},
	{0x2F924, UNICODE_NORM_QC_NO},
	{0x2F925, UNICODE_NORM_QC_NO},
	{0x2F926, UNICODE_NORM_QC_NO},
	{0x2F927, UNICODE_NORM_QC_NO},
	{0x2F928, UNICODE_NORM_QC_NO},
	{0x2F929, UNICODE_NORM_QC_NO},
	{0x2F92A, UNICODE_NORM_QC_NO},
	{0x2F92B, UNICODE_NORM_QC_NO},
	{0x2F92C, UNICODE_NORM_QC_NO},
	{0x2F92D, UNICODE_NORM_QC_NO},
	{0x2F92E, UNICODE_NORM_QC_NO},
	{0x2F92F, UNICODE_NORM_QC_NO},
	{0x2F930, UNICODE_NORM_QC_NO},
	{0x2F931, UNICODE_NORM_QC_NO},
	{0x2F932, UNICODE_NORM_QC_NO},
	{0x2F933, UNICODE_NORM_QC_NO},
	{0x2F934, UNICODE_NORM_QC_NO},
	{0x2F935, UNICODE_NORM_QC_NO},
	{0x2F936, UNICODE_NORM_QC_NO},
	{0x2F937, UNICODE_NORM_QC_NO},
	{0x2F938, UNICODE_NORM_QC_NO},
	{0x2F939, UNICODE_NORM_QC_NO},
	{0x2F93A, UNICODE_NORM_QC_NO},
	{0x2F93B, UNICODE_NORM_QC_NO},
	{0x2F93C, UNICODE_NORM_QC_NO},
	{0x2F93D, UNICODE_NORM_QC_NO},
	{0x2F93E, UNICODE_NORM_QC_NO},
	{0x2F93F, UNICODE_NORM_QC_NO},
	{0x2F940, UNICODE_NORM_QC_NO},
	{0x2F941, UNICODE_NORM_QC_NO},
	{0x2F942, UNICODE_NORM_QC_NO},
	{0x2F943, UNICODE_NORM_QC_NO},
	{0x2F944, UNICODE_NORM_QC_NO},
	{0x2F945, UNICODE_NORM_QC_NO},
	{0x2F946, UNICODE_NORM_QC_NO},
	{0x2F947, UNICODE_NORM_QC_NO},
	{0x2F948, UNICODE_NORM_QC_NO},
	{0x2F949, UNICODE_NORM_QC_NO},
	{0x2F94A, UNICODE_NORM_QC_NO},
	{0x2F94B, UNICODE_NORM_QC_NO},
	{0x2F94C, UNICODE_NORM_QC_NO},
	{0x2F94D, UNICODE_NORM_QC_NO},
	{0x2F94E, UNICODE_NORM_QC_NO},
	{0x2F94F, UNICODE_NORM_QC_NO},
	{0x2F950, UNICODE_NORM_QC_NO},
	{0x2F951, UNICODE_NORM_QC_NO},
	{0x2F952, UNICODE_NORM_QC_NO},
	{0x2F953, UNICODE_NORM_QC_NO},
	{0x2F954, UNICODE_NORM_QC_NO},
	{0x2F955, UNICODE_NORM_QC_NO},
	{0x2F956, UNICODE_NORM_QC_NO},
	{0x2F957, UNICODE_NORM_QC_NO},
	{0x2F958, UNICODE_NORM_QC_NO},
	{0x2F959, UNICODE_NORM_QC_NO},
	{0x2F95A, UNICODE_NORM_QC_NO},
	{0x2F95B, UNICODE_NORM_QC_NO},
	{0x2F95C, UNICODE_NORM_QC_NO},
	{0x2F95D, UNICODE_NORM_QC_NO},
	{0x2F95E, UNICODE_NORM_QC_NO},
	{0x2F95F, UNICODE_NORM_QC_NO},
	{0x2F960, UNICODE_NORM_QC_NO},
	{0x2F961, UNICODE_NORM_QC_NO},
	{0x2F962, UNICODE_NORM_QC_NO},
	{0x2F963, UNICODE_NORM_QC_NO},
	{0x2F964, UNICODE_NORM_QC_NO},
	{0x2F965, UNICODE_NORM_QC_NO},
	{0x2F966, UNICODE_NORM_QC_NO},
	{0x2F967, UNICODE_NORM_QC_NO},
	{0x2F968, UNICODE_NORM_QC_NO},
	{0x2F969, UNICODE_NORM_QC_NO},
	{0x2F96A, UNICODE_NORM_QC_NO},
	{0x2F96B, UNICODE_NORM_QC_NO},
	{0x2F96C, UNICODE_NORM_QC_NO},
	{0x2F96D, UNICODE_NORM_QC_NO},
	{0x2F96E, UNICODE_NORM_QC_NO},
	{0x2F96F, UNICODE_NORM_QC_NO},
	{0x2F970, UNICODE_NORM_QC_NO},
	{0x2F971, UNICODE_NORM_QC_NO},
	{0x2F972, UNICODE_NORM_QC_NO},
	{0x2F973, UNICODE_NORM_QC_NO},
	{0x2F974, UNICODE_NORM_QC_NO},
	{0x2F975, UNICODE_NORM_QC_NO},
	{0x2F976, UNICODE_NORM_QC_NO},
	{0x2F977, UNICODE_NORM_QC_NO},
	{0x2F978, UNICODE_NORM_QC_NO},
	{0x2F979, UNICODE_NORM_QC_NO},
	{0x2F97A, UNICODE_NORM_QC_NO},
	{0x2F97B, UNICODE_NORM_QC_NO},
	{0x2F97C, UNICODE_NORM_QC_NO},
	{0x2F97D, UNICODE_NORM_QC_NO},
	{0x2F97E, UNICODE_NORM_QC_NO},
	{0x2F97F, UNICODE_NORM_QC_NO},
	{0x2F980, UNICODE_NORM_QC_NO},
	{0x2F981, UNICODE_NORM_QC_NO},
	{0x2F982, UNICODE_NORM_QC_NO},
	{0x2F983, UNICODE_NORM_QC_NO},
	{0x2F984, UNICODE_NORM_QC_NO},
	{0x2F985, UNICODE_NORM_QC_NO},
	{0x2F986, UNICODE_NORM_QC_NO},
	{0x2F987, UNICODE_NORM_QC_NO},
	{0x2F988, UNICODE_NORM_QC_NO},
	{0x2F989, UNICODE_NORM_QC_NO},
	{0x2F98A, UNICODE_NORM_QC_NO},
	{0x2F98B, UNICODE_NORM_QC_NO},
	{0x2F98C, UNICODE_NORM_QC_NO},
	{0x2F98D, UNICODE_NORM_QC_NO},
	{0x2F98E, UNICODE_NORM_QC_NO},
	{0x2F98F, UNICODE_NORM_QC_NO},
	{0x2F990, UNICODE_NORM_QC_NO},
	{0x2F991, UNICODE_NORM_QC_NO},
	{0x2F992, UNICODE_NORM_QC_NO},
	{0x2F993, UNICODE_NORM_QC_NO},
	{0x2F994, UNICODE_NORM_QC_NO},
	{0x2F995, UNICODE_NORM_QC_NO},
	{0x2F996, UNICODE_NORM_QC_NO},
	{0x2F997, UNICODE_NORM_QC_NO},
	{0x2F998, UNICODE_NORM_QC_NO},
	{0x2F999, UNICODE_NORM_QC_NO},
	{0x2F99A, UNICODE_NORM_QC_NO},
	{0x2F99B, UNICODE_NORM_QC_NO},
	{0x2F99C, UNICODE_NORM_QC_NO},
	{0x2F99D, UNICODE_NORM_QC_NO},
	{0x2F99E, UNICODE_NORM_QC_NO},
	{0x2F99F, UNICODE_NORM_QC_NO},
	{0x2F9A0, UNICODE_NORM_QC_NO},
	{0x2F9A1, UNICODE_NORM_QC_NO},
	{0x2F9A2, UNICODE_NORM_QC_NO},
	{0x2F9A3, UNICODE_NORM_QC_NO},
	{0x2F9A4, UNICODE_NORM_QC_NO},
	{0x2F9A5, UNICODE_NORM_QC_NO},
	{0x2F9A6, UNICODE_NORM_QC_NO},
	{0x2F9A7, UNICODE_NORM_QC_NO},
	{0x2F9A8, UNICODE_NORM_QC_NO},
	{0x2F9A9, UNICODE_NORM_QC_NO},
	{0x2F9AA, UNICODE_NORM_QC_NO},
	{0x2F9AB, UNICODE_NORM_QC_NO},
	{0x2F9AC, UNICODE_NORM_QC_NO},
	{0x2F9AD, UNICODE_NORM_QC_NO},
	{0x2F9AE, UNICODE_NORM_QC_NO},
	{0x2F9AF, UNICODE_NORM_QC_NO},
	{0x2F9B0, UNICODE_NORM_QC_NO},
	{0x2F9B1, UNICODE_NORM_QC_NO},
	{0x2F9B2, UNICODE_NORM_QC_NO},
	{0x2F9B3, UNICODE_NORM_QC_NO},
	{0x2F9B4, UNICODE_NORM_QC_NO},
	{0x2F9B5, UNICODE_NORM_QC_NO},
	{0x2F9B6, UNICODE_NORM_QC_NO},
	{0x2F9B7, UNICODE_NORM_QC_NO},
	{0x2F9B8, UNICODE_NORM_QC_NO},
	{0x2F9B9, UNICODE_NORM_QC_NO},
	{0x2F9BA, UNICODE_NORM_QC_NO},
	{0x2F9BB, UNICODE_NORM_QC_NO},
	{0x2F9BC, UNICODE_NORM_QC_NO},
	{0x2F9BD, UNICODE_NORM_QC_NO},
	{0x2F9BE, UNICODE_NORM_QC_NO},
	{0x2F9BF, UNICODE_NORM_QC_NO},
	{0x2F9C0, UNICODE_NORM_QC_NO},
	{0x2F9C1, UNICODE_NORM_QC_NO},
	{0x2F9C2, UNICODE_NORM_QC_NO},
	{0x2F9C3, UNICODE_NORM_QC_NO},
	{0x2F9C4, UNICODE_NORM_QC_NO},
	{0x2F9C5, UNICODE_NORM_QC_NO},
	{0x2F9C6, UNICODE_NORM_QC_NO},
	{0x2F9C7, UNICODE_NORM_QC_NO},
	{0x2F9C8, UNICODE_NORM_QC_NO},
	{0x2F9C9, UNICODE_NORM_QC_NO},
	{0x2F9CA, UNICODE_NORM_QC_NO},
	{0x2F9CB, UNICODE_NORM_QC_NO},
	{0x2F9CC, UNICODE_NORM_QC_NO},
	{0x2F9CD, UNICODE_NORM_QC_NO},
	{0x2F9CE, UNICODE_NORM_QC_NO},
	{0x2F9CF, UNICODE_NORM_QC_NO},
	{0x2F9D0, UNICODE_NORM_QC_NO},
	{0x2F9D1, UNICODE_NORM_QC_NO},
	{0x2F9D2, UNICODE_NORM_QC_NO},
	{0x2F9D3, UNICODE_NORM_QC_NO},
	{0x2F9D4, UNICODE_NORM_QC_NO},
	{0x2F9D5, UNICODE_NORM_QC_NO},
	{0x2F9D6, UNICODE_NORM_QC_NO},
	{0x2F9D7, UNICODE_NORM_QC_NO},
	{0x2F9D8, UNICODE_NORM_QC_NO},
	{0x2F9D9, UNICODE_NORM_QC_NO},
	{0x2F9DA, UNICODE_NORM_QC_NO},
	{0x2F9DB, UNICODE_NORM_QC_NO},
	{0x2F9DC, UNICODE_NORM_QC_NO},
	{0x2F9DD, UNICODE_NORM_QC_NO},
	{0x2F9DE, UNICODE_NORM_QC_NO},
	{0x2F9DF, UNICODE_NORM_QC_NO},
	{0x2F9E0, UNICODE_NORM_QC_NO},
	{0x2F9E1, UNICODE_NORM_QC_NO},
	{0x2F9E2, UNICODE_NORM_QC_NO},
	{0x2F9E3, UNICODE_NORM_QC_NO},
	{0x2F9E4, UNICODE_NORM_QC_NO},
	{0x2F9E5, UNICODE_NORM_QC_NO},
	{0x2F9E6, UNICODE_NORM_QC_NO},
	{0x2F9E7, UNICODE_NORM_QC_NO},
	{0x2F9E8, UNICODE_NORM_QC_NO},
	{0x2F9E9, UNICODE_NORM_QC_NO},
	{0x2F9EA, UNICODE_NORM_QC_NO},
	{0x2F9EB, UNICODE_NORM_QC_NO},
	{0x2F9EC, UNICODE_NORM_QC_NO},
	{0x2F9ED, UNICODE_NORM_QC_NO},
	{0x2F9EE, UNICODE_NORM_QC_NO},
	{0x2F9EF, UNICODE_NORM_QC_NO},
	{0x2F9F0, UNICODE_NORM_QC_NO},
	{0x2F9F1, UNICODE_NORM_QC_NO},
	{0x2F9F2, UNICODE_NORM_QC_NO},
	{0x2F9F3, UNICODE_NORM_QC_NO},
	{0x2F9F4, UNICODE_NORM_QC_NO},
	{0x2F9F5, UNICODE_NORM_QC_NO},
	{0x2F9F6, UNICODE_NORM_QC_NO},
	{0x2F9F7, UNICODE_NORM_QC_NO},
	{0x2F9F8, UNICODE_NORM_QC_NO},
	{0x2F9F9, UNICODE_NORM_QC_NO},
	{0x2F9FA, UNICODE_NORM_QC_NO},
	{0x2F9FB, UNICODE_NORM_QC_NO},
	{0x2F9FC, UNICODE_NORM_QC_NO},
	{0x2F9FD, UNICODE_NORM_QC_NO},
	{0x2F9FE, UNICODE_NORM_QC_NO},
	{0x2F9FF, UNICODE_NORM_QC_NO},
	{0x2FA00, UNICODE_NORM_QC_NO},
	{0x2FA01, UNICODE_NORM_QC_NO},
	{0x2FA02, UNICODE_NORM_QC_NO},
	{0x2FA03, UNICODE_NORM_QC_NO},
	{0x2FA04, UNICODE_NORM_QC_NO},
	{0x2FA05, UNICODE_NORM_QC_NO},
	{0x2FA06, UNICODE_NORM_QC_NO},
	{0x2FA07, UNICODE_NORM_QC_NO},
	{0x2FA08, UNICODE_NORM_QC_NO},
	{0x2FA09, UNICODE_NORM_QC_NO},
	{0x2FA0A, UNICODE_NORM_QC_NO},
	{0x2FA0B, UNICODE_NORM_QC_NO},
	{0x2FA0C, UNICODE_NORM_QC_NO},
	{0x2FA0D, UNICODE_NORM_QC_NO},
	{0x2FA0E, UNICODE_NORM_QC_NO},
	{0x2FA0F, UNICODE_NORM_QC_NO},
	{0x2FA10, UNICODE_NORM_QC_NO},
	{0x2FA11, UNICODE_NORM_QC_NO},
	{0x2FA12, UNICODE_NORM_QC_NO},
	{0x2FA13, UNICODE_NORM_QC_NO},
	{0x2FA14, UNICODE_NORM_QC_NO},
	{0x2FA15, UNICODE_NORM_QC_NO},
	{0x2FA16, UNICODE_NORM_QC_NO},
	{0x2FA17, UNICODE_NORM_QC_NO},
	{0x2FA18, UNICODE_NORM_QC_NO},
	{0x2FA19, UNICODE_NORM_QC_NO},
	{0x2FA1A, UNICODE_NORM_QC_NO},
	{0x2FA1B, UNICODE_NORM_QC_NO},
	{0x2FA1C, UNICODE_NORM_QC_NO},
	{0x2FA1D, UNICODE_NORM_QC_NO},
};

/* Perfect hash function for NFKC_QC */
static int
NFKC_QC_hash_func(const void *key)
{
	static const int16 h[10079] = {
		3542,  3543,  3544,  3545,  3546,  3547,  3548,  3549,
		3550,  3551,  3552,  3553,  3554,  3555,  3556,  3557,
		3558,  3559,  3560,  3561,  3562,  3563,  3564,  3565,
		3566,  3567,  3568,  3569,  3570,  3571,  3572,  3573,
		3574,  3575,  3576,  3577,  3578,  3579,  3580,  3581,
		3582,  3583,  3584,  3585,  3586,  3587,  3588,  3589,
		3590,  3591,  3592,  3593,  3594,  3595,  3596,  3597,
		3598,  3599,  3600,  3601,  3602,  3603,  3604,  3605,
		3606,  3607,  3608,  3970,  3488,  3611,  3612,  3613,
		3614,  3615,  3616,  3617,  3618,  7967,  3620,  3621,
		3622,  3623,  3624,  3625,  3626,  3627,  3628,  3629,
		3630,  3631,  3632,  3633,  3634,  3512,  3636,  3637,
		3638,  3639,  32767, 11118, 11119, 11120, 11121, 11122,
		1470,  11124, 11125, 11126, 11127, 11128, 11129, 11130,
		11131, 11132, -287,  8773,  8774,  8775,  8776,  8777,
		8778,  -287,  8781,  8782,  -287,  8783,  8784,  8785,
		8786,  8787,  8788,  8789,  8790,  8791,  -287,  -287,
		-287,  -287,  -287,  -287,  -287,  -287,  -287,  -287,
		-287,  -287,  -287,  -287,  -287,  -287,  -287,  -287,
		-287,  2372,  -287,  -287,  -287,  -287,  -287,  -287,
		-287,  -287,  -287,  2458,  2458,  2458,  2458,  2458,
		2458,  2458,  2458,  2458,  -287,  -287,  -287,  -5017,
		5765,  -287,  -5019, -5019, -5019, -5019, -287,  -287,
		4632,  -5021, -5021, -287,  -5022, -5022, -5022, 3732,
		-287,  -287,  3735,  3736,  -287,  -287,  -287,  -287,
		-287,  -287,  -287,  -287,  -287,  -287,  -287,  -287,
		4994,  2491,  2491,  -287,  -287,  -287,  6887,  -287,
		-287,  -287,  -287,  -287,  -287,  -287,  -287,  -287,
		-287,  -287,  -287,  -287,  -287,  -287,  -287,  -287,
		-287,  -287,  -287,  -287,  -287,  -287,  -287,  -287,
		-287,  -287,  -287,  -287,  -287,  -287,  -287,  -287,
		-287,  -287,  -287,  -287,  -287,  -287,  -287,  -287,
		-287,  -287,  -287,  -287,  -287,  -287,  -287,  -287,
		-287,  32767, 32767, -289,  1778,  629,   3809,  3810,
		-3050, -287,  -287,  -287,  -287,  -287,  -287,  -287,
		-287,  -287,  -287,  -287,  -287,  -287,  -287,  -287,
		-287,  -287,  -287,  -287,  -287,  -287,  -287,  -287,
		-287,  -287,  -287,  -287,  -287,  -287,  -287,  -287,
		-287,  -287,  -287,  -287,  -287,  -287,  -287,  -287,
		-287,  -287,  -287,  -287,  -287,  -287,  -287,  -287,
		-287,  -287,  3861,  3862,  3863,  3864,  3865,  3866,
		3867,  3868,  -1002, 3870,  3871,  3872,  3873,  3874,
		3875,  3876,  3877,  3878,  3879,  3880,  3881,  3882,
		3883,  3884,  3885,  3886,  3887,  3888,  3889,  3890,
		3891,  3892,  3893,  32767, 3392,  3392,  3392,  -5411,
		3392,  3392,  3392,  3392,  3392,  3392,  3392,  3392,
		3392,  3392,  224,   225,   226,   3392,  227,   228,
		-5998, -5998, 3392,  -5998, -5,    0,     -5997, -5997,
		-7477, 3923,  -7478, -7478, -7478, -7478, -7478, -7478,
		-7478, -7478, 2175,  -7478, -7478, -7478, -7478, -7478,
		-7478, -7478, -7478, -7478, 3942,  -5117, -5117, -5117,
		-5117, -5117, -5117, 3949,  -5118, -5118, 3952,  -5117,
		-5117, -5117, -5117, -5117, -5117, -5117, -5117, -5117,
		3962,  3963,  3964,  3965,  3966,  3967,  3968,  3969,
		3970,  3971,  3972,  3973,  3974,  3975,  3976,  3977,
		3978,  3979,  3980,  1322,  3982,  3983,  3984,  3985,
		3986,  3987,  3988,  3989,  3990,  1246,  1247,  1248,
		1249,  1250,  1251,  1252,  1253,  1254,  4000,  4001,
		4002,  8733,  -2048, 4005,  8738,  8739,  8740,  8741,
		4010,  4011,  -907,  8747,  8748,  4015,  8751,  8752,
		8753,  0,     4020,  4021,  0,     0,     4024,  4025,
		4026,  4027,  4028,  4029,  4030,  4031,  4032,  4033,
		4034,  4035,  -1245, 1259,  1260,  4039,  4040,  4041,
		-3132, 4043,  4044,  4045,  4046,  4047,  4048,  4049,
		4050,  4051,  4052,  4053,  4054,  4055,  4056,  4057,
		4058,  4059,  4060,  4061,  4062,  4063,  4064,  4065,
		4066,  4067,  4068,  4069,  4070,  4071,  4072,  4073,
		4074,  4075,  4076,  4077,  4078,  4079,  4080,  4081,
		4082,  4083,  4084,  4085,  4086,  4087,  4088,  4089,
		4090,  4091,  4092,  4093,  4094,  4095,  2029,  3179,
		0,     0,     6861,  4099,  4100,  4101,  4102,  4103,
		4104,  4105,  4106,  4107,  4108,  4109,  4110,  4111,
		4112,  4113,  4114,  4115,  4116,  4117,  4118,  4119,
		4120,  4121,  4122,  4123,  4124,  4125,  4126,  4127,
		4128,  4129,  4130,  4131,  4132,  4133,  4134,  4135,
		4136,  4137,  4138,  4139,  4140,  4141,  4142,  4143,
		4144,  4145,  4146,  4147,  0,     0,     0,     0,
		0,     0,     0,     0,     4871,  0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, -1786, -1785, -1784, -1783, -1782, 5982,
		-630,  -48,   -48,   32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 677,
		32767, 32767, -636,  -635,  1019,  -3373, 1019,  1019,
		1019,  1019,  1019,  1019,  1019,  1019,  1019,  1019,
		1019,  1019,  1019,  1019,  1019,  -685,  -684,  -683,
		-682,  -681,  -6170, -679,  -678,  2476,  2477,  -6912,
		2479,  -3513, 3959,  2482,  2483,  3964,  3965,  3966,
		3967,  3968,  3969,  3970,  3971,  3972,  55,    56,
		2495,  57,    58,    59,    60,    61,    62,    63,
		2503,  2504,  64,    2506,  65,    2508,  66,    67,
		2511,  2512,  32767, 32767, 32767, 32767, 68,    32767,
		32767, 32767, 32767, 32767, 32767, 32767, 69,    70,
		71,    72,    73,    74,    1140,  2514,  2515,  422,
		75,    2518,  -1594, 556,   557,   2522,  2523,  2524,
		2525,  422,   422,   422,   422,   422,   422,   422,
		422,   422,   422,   422,   422,   422,   422,   422,
		422,   422,   422,   422,   422,   422,   422,   422,
		422,   422,   422,   422,   422,   422,   422,   422,
		422,   422,   422,   422,   422,   422,   422,   422,
		422,   422,   422,   422,   422,   422,   422,   -2277,
		-2277, -2277, -2277, -2277, 32767, 32767, -2279, -2279,
		-2279, -2279, -2279, -2279, -2279, -2279, -2279, 86,
		-2279, -2279, -2279, -2279, -2279, 87,    -2279, -2279,
		-2279, 88,    -2279, -2279, -2279, -2279, -2279, 89,
		453,   453,   453,   453,   453,   453,   2609,  453,
		453,   453,   453,   453,   453,   453,   453,   453,
		453,   453,   453,   1817,  453,   453,   453,   453,
		453,   453,   453,   453,   32767, 32767, 32767, 681,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, -2319, 2632,  -2319, -2319,
		-2319, 2636,  2637,  92,    2639,  2640,  95,    96,
		97,    98,    32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 3279,  3280,  3281,  3282,  3283,  3284,
		3285,  3286,  3287,  3288,  3289,  3290,  3291,  3292,
		-384,  -1535, -385,  -385,  0,     3298,  3299,  3300,
		3301,  3302,  3303,  3304,  -8646, -1149, -1148, -1147,
		-1146, 32767, 3310,  3311,  3312,  3313,  3314,  -1140,
		-1139, -1138, -1137, -1136, -1135, -1134, -1133, -1132,
		3324,  3325,  3326,  3327,  3328,  3329,  3330,  3331,
		3332,  3333,  3334,  3335,  3336,  3337,  3338,  3339,
		3340,  3341,  3342,  3343,  3344,  -1327, -1327, -1327,
		-1327, -1327, 32767, 32767, 2346,  32767, 32767, 32767,
		32767, 32767, 3350,  335,   3245,  8185,  1818,  666,
		1818,  795,   795,   1818,  1818,  1818,  1818,  1818,
		-5945, 668,   87,    88,    89,    90,    91,    92,
		93,    94,    95,    0,     0,     671,   671,   671,
		671,   671,   0,     0,     673,   673,   673,   0,
		674,   0,     675,   0,     676,   0,     677,   677,
		677,   32767, 0,     677,   677,   -976,  3417,  -974,
		-973,  678,   678,   -972,  -971,  -970,  -969,  -968,
		1152,  1153,  1154,  1155,  1156,  0,     1022,  1022,
		5615,  -6335, 1162,  1163,  1164,  1165,  5621,  5622,
		5623,  5624,  5625,  5626,  1172,  1173,  1174,  1175,
		1176,  1177,  1178,  1179,  1180,  1181,  1182,  1183,
		1184,  1185,  1186,  1187,  1188,  1189,  1190,  1191,
		1192,  1193,  9997,  1195,  1196,  1197,  1198,  1199,
		1200,  1201,  1202,  1203,  1204,  4373,  4373,  4373,
		1208,  4374,  4374,  10601, 10602, 1213,  10604, 4612,
		4608,  10606, 10607, 12088, 1220,  1221,  1222,  1223,
		1224,  1225,  1226,  2453,  2454,  2455,  2456,  2457,
		2458,  2459,  2460,  2461,  2462,  2463,  2464,  2465,
		2466,  2467,  6860,  2469,  2470,  2471,  2472,  2473,
		2474,  2475,  2476,  2477,  2478,  2479,  2480,  2481,
		2482,  2483,  2484,  258,   2486,  2487,  258,   258,
		0,     0,     32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 5673,  5674,
		0,     5677,  0,     5680,  5681,  5682,  5683,  5684,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 682,   683,   32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 105,   32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		2315,  4754,  2316,  2317,  2318,  2319,  2320,  2321,
		2322,  4762,  4763,  2323,  4765,  2324,  4767,  2325,
		2326,  4770,  4771,  4772,  2377,  4774,  4775,  4776,
		4777,  4778,  4779,  4780,  4781,  4782,  4783,  4784,
		4785,  4786,  4787,  4788,  4789,  3417,  4791,  4792,
		2699,  2352,  4795,  683,   2833,  2834,  4799,  4800,
		4801,  4802,  2699,  2699,  2699,  2699,  2699,  2699,
		2699,  2699,  2699,  2699,  2699,  2699,  2699,  2699,
		2699,  2699,  2699,  2699,  2699,  2699,  2699,  2699,
		2699,  2699,  2699,  2699,  2699,  2699,  2699,  2699,
		2699,  2699,  2699,  2699,  2699,  2699,  2699,  2699,
		2699,  2699,  2699,  2699,  2699,  2699,  2699,  2699,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		2365,  0,     0,     0,     0,     0,     2366,  0,
		0,     0,     2367,  0,     0,     0,     0,     0,
		2368,  2732,  2732,  2732,  2732,  2732,  2732,  4888,
		2732,  2732,  2732,  2732,  2732,  2732,  2732,  2732,
		2732,  2732,  2732,  2732,  4096,  2732,  2732,  2732,
		2732,  2732,  2732,  2732,  2732,  2732,  2732,  2732,
		2732,  2732,  2732,  2732,  2732,  2732,  2732,  2732,
		2732,  2732,  2732,  2732,  2732,  2732,  4927,  0,
		0,     0,     0,     0,     4933,  0,     0,     0,
		0,     -2910, 0,     4940,  4941,  4942,  0,     0,
		0,     0,     0,     0,     0,     0,     4951,  0,
		0,     0,     4955,  4956,  2411,  4958,  4959,  2414,
		2415,  2416,  0,     10954, 0,     3436,  0,     0,
		0,     0,     0,     -3170, -3169, -3168, -3167, 3446,
		2865,  0,     0,     0,     0,     0,     2339,  2339,
		2339,  2339,  2339,  2339,  2339,  2339,  2892,  2339,
		0,     0,     0,     0,     0,     0,     2907,  0,
		0,     0,     0,     0,     0,     4719,  0,     0,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 1856,  868,   868,   868,   868,
		868,   868,   868,   868,   868,   868,   868,   1856,
		1856,  1856,  1856,  1856,  873,   873,   873,   873,
		873,   873,   873,   873,   873,   873,   873,   873,
		873,   873,   873,   873,   873,   6441,  6442,  6443,
		6444,  6445,  6446,  6447,  6448,  6449,  6450,  6451,
		6452,  -1169, 6454,  6455,  6456,  6457,  6458,  6459,
		6460,  6461,  6462,  6463,  6464,  6465,  6466,  6467,
		6468,  32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, -3334, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, -766,  32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, -1530, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 686,   32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		687,   688,   689,   690,   691,   692,   693,   694,
		695,   2465,  697,   698,   699,   700,   701,   702,
		703,   704,   705,   2484,  707,   708,   709,   710,
		2493,  712,   713,   714,   715,   2502,  717,   718,
		719,   720,   2511,  722,   723,   724,   725,   726,
		727,   728,   729,   730,   731,   732,   733,   2536,
		735,   736,   737,   738,   739,   740,   741,   742,
		743,   2555,  745,   2558,  2559,  2560,  2561,  2562,
		751,   752,   753,   754,   755,   756,   757,   2577,
		759,   760,   761,   762,   763,   764,   1224,  1224,
		767,   768,   769,   770,   3787,  772,   773,   774,
		775,   776,   777,   778,   779,   780,   781,   782,
		783,   784,   785,   786,   787,   788,   789,   790,
		791,   792,   793,   794,   795,   796,   797,   798,
		799,   800,   801,   802,   803,   804,   805,   806,
		807,   808,   809,   810,   811,   812,   813,   814,
		815,   816,   817,   818,   819,   820,   821,   822,
		823,   824,   825,   826,   827,   828,   829,   830,
		831,   832,   833,   834,   835,   836,   837,   838,
		839,   840,   841,   842,   843,   844,   845,   846,
		847,   848,   849,   850,   851,   852,   853,   854,
		855,   856,   857,   858,   859,   860,   861,   862,
		863,   864,   865,   866,   867,   868,   869,   870,
		871,   872,   873,   874,   875,   876,   877,   878,
		879,   880,   881,   882,   883,   884,   885,   886,
		887,   888,   889,   890,   891,   892,   893,   894,
		895,   896,   897,   898,   899,   900,   32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 901,   32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, -1268,
		32767, 32767, 32767, 32767, 32767, -984,  -984,  -2101,
		-2101, -2101, -2101, 32767, 3402,  3403,  -985,  -985,
		32767, 32767, 0,     -986,  -986,  -986,  -986,  -986,
		-986,  -986,  32767, -987,  -987,  -987,  -987,  -987,
		-987,  -987,  32767, -988,  -988,  -988,  -988,  -1746,
		-988,  -1747, -1747, -1194, -988,  -988,  -988,  -988,
		-988,  -988,  -988,  0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     32767,
		0,     0,     0,     321,   32767, 0,     0,     0,
		0,     0,     32767, 0,     32767, 32767, 32767, -968,
		0,     0,     0,     0,     0,     0,     32767, 0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     333,   113,   333,   2915,
		333,   333,   0,     0,     0,     0,     0,     0,
		0,     0,     0,     -934,  0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     -361,  122,
		0,     0,     0,     0,     0,     0,     0,     0,
		-4348, 0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		123,   0,     0,     0,     0,     -3819, -3818, 32767,
		126,   32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 5367,  5368,  914,   915,   916,
		917,   918,   919,   920,   921,   922,   923,   924,
		925,   926,   927,   928,   929,   930,   931,   932,
		933,   934,   935,   9739,  937,   938,   939,   940,
		941,   942,   943,   944,   945,   946,   4115,  4115,
		4115,  950,   4116,  4116,  10343, 10344, 955,   10346,
		4354,  4350,  10348, 10349, 11830, 962,   963,   964,
		965,   966,   967,   968,   2195,  2196,  2197,  2198,
		2199,  2200,  2201,  2202,  2203,  2204,  2205,  2206,
		2207,  2208,  2209,  6602,  2211,  2212,  2213,  2214,
		2215,  2216,  2217,  2218,  2219,  2220,  2221,  2222,
		2223,  2224,  2225,  2226,  0,     2228,  2229,  0,
		0,     2232,  0,     129,   2235,  2236,  -260,  -260,
		2239,  2240,  2241,  2242,  -260,  2244,  2245,  2246,
		2247,  2248,  2249,  2250,  2251,  2252,  2253,  -2259,
		-2259, -2259, -2259, -2259, -2259, -2259, -2259, -2259,
		-2259, 2264,  2265,  -2259, 2267,  2268,  2269,  2270,
		2271,  2272,  2273,  2274,  2275,  2276,  2277,  2278,
		-2729, 2280,  2281,  2282,  2283,  2284,  2285,  2286,
		2287,  2288,  2289,  2290,  2291,  2292,  3281,  3282,
		3283,  3284,  3285,  3286,  3287,  3288,  3289,  3290,
		3291,  2304,  2305,  2306,  2307,  2308,  3292,  3293,
		3294,  3295,  3296,  3297,  3298,  3299,  3300,  3301,
		3302,  3303,  3304,  3305,  3306,  3307,  3308,  -2259,
		-2259, -2259, -2259, -2259, -2259, -2259, -2259, -2259,
		-2259, -2259, -2259, 5363,  -2259, -2259, -2259, -2259,
		-2259, -2259, -2259, -2259, -2259, -2259, -2259, -2259,
		-2259, -2259, -2259, -2259, -2259, -2259, -2259, -2259,
		-2259, 1418,  2570,  1421,  1422,  1038,  -2259, -2259,
		-2259, -2259, -2259, -2259, -2259, 9692,  2196,  2196,
		2196,  2196,  -2259, -2259, -2259, -2259, -2259, -2259,
		2196,  2196,  2196,  2196,  2196,  2196,  2196,  2196,
		2196,  -2259, -2259, -2259, -2259, -2259, -2259, -2259,
		-2259, -2259, -2259, -2259, -2259, -2259, -2259, -2259,
		-2259, -2259, -2259, -2259, -2259, -2259, 2413,  2414,
		2415,  2416,  2417,  2418,  2419,  301,   302,   760,
		-2259, -2259, -2259, -2259, 757,   -2152, -7091, -723,
		430,   -721,  303,   304,   -718,  -717,  -716,  -715,
		-714,  7050,  438,   1020,  1020,  1020,  1020,  1020,
		1020,  1020,  1020,  1020,  1116,  1117,  447,   448,
		449,   450,   451,   1123,  1124,  452,   453,   454,
		1128,  455,   1130,  456,   1132,  457,   1134,  458,
		459,   460,   461,   1138,  462,   463,   2117,  -2275,
		2117,  2117,  467,   468,   2119,  2119,  2119,  2119,
		2119,  0,     0,     0,     0,     0,     1157,  136,
		137,   -4455, 7496,  0,     0,     0,     0,     -4455,
		-4455, -4455, -4455, -4455, -4455, 0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     -8803, 0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     -3168, -3167,
		-3166, 0,     -3165, -3164, -9390, -9390, 0,     -9390,
		-3397, -3392, -9389, -9389, -10869,0,     0,     0,
		0,     0,     0,     0,     -1226, -1226, -1226, -1226,
		-1226, -1226, -1226, -1226, -1226, -1226, -1226, -1226,
		-1226, -1226, -1226, -5618, -1226, -1226, -1226, -1226,
		-1226, -1226, -1226, -1226, -1226, -1226, -1226, -1226,
		-1226, -1226, -1226, -1226, 1001,  -1226, -1226, 1004,
		1005,  1264,  1265,  32767, -1229, -1229, 1268,  1269,
		-1229, -1229, -1229, -1229, 1274,  -1229, -1229, -1229,
		-1229, -1229, 139,   1281,  1282,  1283,  1284,  1285,
		1286,  1287,  1288,  1289,  1290,  1291,  1292,  1293,
		1294,  1295,  1296,  1297,  1298,  1299,  1300,  1301,
		1302,  1303,  1304,  1305,  1306,  1307,  1308,  1309,
		1310,  1311,  1675,  1313,  1676,  1315,  1316,  1317,
		1318,  1319,  1320,  1321,  1322,  1323,  1324,  1325,
		1326,  1327,  1328,  1329,  1330,  1331,  1332,  1333,
		1334,  1335,  1336,  1337,  1338,  1339,  1340,  1341,
		1342,  1343,  1344,  1345,  1346,  1347,  1348,  1349,
		1350,  1351,  1352,  1353,  1354,  1355,  1356,  1357,
		1358,  1359,  1360,  1361,  1362,  1363,  1364,  1365,
		1366,  1367,  1368,  1369,  1370,  1371,  1372,  1373,
		1374,  1375,  1376,  1377,  1378,  1379,  1380,  1381,
		1382,  1383,  1384,  1385,  1386,  1387,  3934,  3935,
		1390,  1391,  1392,  1393,  1394,  1395,  1396,  1397,
		1398,  1399,  1400,  1401,  1402,  1403,  1404,  1405,
		1406,  1407,  1408,  1409,  1410,  1411,  1412,  1413,
		1414,  1415,  1416,  1417,  1418,  935,   1320,  4618,
		1422,  4621,  4622,  1425,  1426,  1427,  -7322, 5372,
		1430,  1431,  5375,  891,   891,   891,   891,   891,
		891,   891,   5376,  2037,  891,   891,   891,   891,
		891,   891,   891,   891,   891,   891,   891,   891,
		891,   891,   891,   891,   891,   891,   891,   891,
		891,   891,   891,   891,   891,   891,   891,   891,
		891,   891,   891,   891,   891,   891,   891,   891,
		891,   891,   891,   891,   891,   891,   891,   891,
		891,   891,   891,   891,   891,   891,   891,   891,
		891,   891,   891,   891,   891,   891,   891,   891,
		891,   891,   891,   891,   891,   891,   891,   891,
		891,   891,   891,   891,   891,   891,   141,   891,
		891,   891,   891,   891,   32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 142,   143,   32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		1999,  4498,  4499,  4500,  4501,  1999,  4503,  4504,
		4505,  4506,  4507,  4508,  4509,  4510,  4511,  4512,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     4523,  4524,  0,     4526,  4527,  4528,
		4529,  4530,  4531,  4532,  4533,  4534,  4535,  4536,
		4537,  -470,  4539,  4540,  4541,  4542,  4543,  4544,
		4545,  4546,  4547,  4548,  4549,  4550,  4551,  5540,
		5541,  5542,  5543,  5544,  5545,  5546,  5547,  5548,
		5549,  5550,  4563,  4564,  4565,  4566,  4567,  5551,
		5552,  5553,  5554,  5555,  5556,  5557,  5558,  5559,
		5560,  5561,  5562,  5563,  5564,  5565,  5566,  5567,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     7622,  0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     3677,  4829,  3680,  3681,  3297,  0,
		0,     0,     0,     0,     0,     0,     11951, 4455,
		4455,  4455,  4455,  0,     0,     0,     0,     0,
		0,     4455,  4455,  4455,  4455,  4455,  4455,  4455,
		4455,  4455,  0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     4672,
		4673,  4674,  4675,  4676,  4677,  4678,  2560,  2561,
		3019,  0,     0,     0,     0,     3016,  107,   -4832,
		1536,  2689,  1538,  2562,  2563,  1541,  1542,  1543,
		1544,  1545,  9309,  2697,  3279,  3279,  3279,  3279,
		3279,  3279,  3279,  3279,  3279,  3279,  3279,  -1113,
		3279,  3279,  3279,  3279,  3279,  3279,  3279,  3279,
		3279,  3279,  3279,  3279,  3279,  3279,  3279,  1575,
		1576,  1577,  1578,  1579,  -3910, 1581,  1582,  4736,
		4737,  -4652, 4739,  -1253, 6219,  4742,  4743,  6224,
		6225,  6226,  6227,  6228,  6229,  6230,  6231,  6232,
		6233,  -3419, 6235,  6236,  6237,  6238,  6239,  6240,
		6241,  6242,  6243,  3882,  3883,  3884,  3885,  3886,
		3887,  3888,  -5177, 3891,  3892,  -5177, 3893,  3894,
		3895,  3896,  3897,  3898,  3899,  3900,  3901,  3902,
		-5176, -5176, -5176, -5176, -5176, -5176, -5176, -5176,
		1640,  1641,  1642,  3924,  1644,  1645,  1646,  1647,
		1648,  1649,  1650,  1651,  1652,  1653,  1654,  1655,
		1656,  1657,  1658,  1659,  1660,  1661,  1662,  1663,
		1664,  1665,  1666,  1667,  1668,  1669,  1670,  1671,
		1672,  1673,  1674,  1675,  1676,  1677,  1678,  1679,
		1680,  1681,  1682,  1683,  1684,  1685,  1686,  1687,
		1688,  1689,  1690,  1691,  1692,  1693,  1694,  1695,
		1696,  1697,  1698,  1699,  1700,  1701,  1702,  1703,
		1704,  1705,  1706,  1707,  1708,  1709,  1710,  1711,
		1712,  1713,  1714,  1715,  1716,  1717,  1718,  1719,
		1720,  1721,  1722,  1723,  1724,  1725,  1726,  1727,
		1728,  1729,  1730,  1731,  -847,  -5786, 582,   1735,
		1736,  1609,  1610,  588,   1740,  591,   592,   593,
		8357,  1745,  2327,  2327,  2327,  2327,  2327,  2327,
		1357,  2328,  2328,  1755,  1756,  1757,  1758,  1759,
		1760,  1761,  1762,  1763,  1764,  1765,  1766,  1767,
		1768,  1769,  1770,  1771,  1772,  1773,  1774,  1775,
		1776,  1777,  1778,  1779,  1780,  3434,  1782,  3435,
		3435,  1785,  1786,  3437,  3437,  3437,  3306,  32767,
		-2509, -2509, -2509, -2509, -2509, -2509, -2509, -2509,
		-2509, 1800,  -2508, -2508, -2508, -2508, 32767, 32767,
		-2510, 32767, -2511, 32767, 32767, -2513, -2513, -2513,
		-2513, -2513, -2513, 1813,  1814,  1815,  1816,  32767,
		-2509, 32767, -2510, 32767, 32767, -2511, -2511, 32767,
		32767, 32767, -3871, -3871, -2513, -2513, -2513, -2513,
		-2513, -2513, -2513, -2513, -2513, -2513, -2513, -2513,
		-2513, -2513, -2513, 1838,  1839,  1840,  1841,  1842,
		1843,  1844,  1845,  1846,  1847,  1848,  1849,  1850,
		1851,  1852,  1853,  1854,  1855,  1856,  1857,  1858,
		1859,  1860,  1861,  1862,  1863,  1864,  1865,  1866,
		1867,  1868,  1869,  1870,  1871,  1872,  1873,  1874,
		1875,  1876,  1877,  1878,  1879,  1880,  1881,  1882,
		1883,  1401,  1885,  1886,  1887,  1888,  32767, 32767,
		1889,  1890,  1891,  1892,  1893,  1894,  1895,  1896,
		1897,  1898,  1899,  1900,  1901,  1902,  1903,  1904,
		1905,  1906,  1907,  1908,  1909,  1910,  1911,  1912,
		1913,  1914,  1915,  1916,  1917,  1918,  1919,  1920,
		1921,  1922,  1923,  1924,  1925,  1926,  1927,  1928,
		1929,  1930,  1931,  1932,  1933,  1934,  1935,  1936,
		1937,  1938,  1939,  1940,  1941,  1942,  1943,  1944,
		1945,  1946,  1947,  1948,  1949,  1950,  1951,  1952,
		1953,  1954,  1955,  1956,  1957,  1958,  1959,  1960,
		1961,  1962,  1963,  1964,  1965,  1966,  1967,  1968,
		1969,  1970,  1971,  1972,  1973,  1974,  1975,  1976,
		1977,  1978,  1979,  1980,  1981,  1982,  1983,  1984,
		1985,  1986,  1987,  1988,  1989,  1990,  1991,  1992,
		3710,  1994,  32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 167,   612,   32767, 32767, 2714,  32767,
		32767, 32767, 32767, 32767, 168,   32767, 32767, 32767,
		32767, 169,   32767, 32767, 32767, 32767, 170,   32767,
		32767, 32767, 32767, 171,   32767, 32767, 32767, 32767,
		32767, 1995,  1996,  1997,  1998,  1999,  2000,  2001,
		172,   32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, -1151, -127,  -126,  -1148,
		-1147, 32767, 32767, 32767, 32767, 32767, 585,   32767,
		584,   584,   584,   584,   584,   584,   584,   584,
		584,   -3808, 584,   584,   584,   584,   584,   584,
		584,   584,   584,   584,   584,   584,   584,   584,
		32767, -1121, -1120, -1119, -1118, -1117, 32767, -1116,
		32767, 2038,  2039,  32767, 2040,  -3952, 32767, 2042,
		2043,  3524,  3525,  3526,  3527,  3528,  3529,  3530,
		3531,  3532,  -385,  -384,  2055,  -383,  -382,  -381,
		-380,  -379,  -378,  -377,  2063,  2064,  -376,  2066,
		-375,  2068,  -374,  -373,  2071,  2072,  2073,  -322,
		2075,  2076,  2077,  2078,  2079,  2080,  2081,  2082,
		2083,  2084,  2085,  2086,  2087,  2088,  2089,  2090,
		718,   2092,  2093,  0,     -347,  2096,  -2016, 134,
		135,   2100,  2101,  2102,  2103,  0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     2156,  0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     1364,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     -4392, 0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     2227,  0,     0,     2230,  2231,  0,
		2233,  2105,  0,     0,     2497,  2498,  0,     0,
		0,     0,     2503,  0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     4513,  4514,  4515,
		4516,  4517,  4518,  4519,  4520,  4521,  4522,  0,
		0,     4525,  0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     5008,  0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     -988,  -988,  -988,  -988,
		-988,  -988,  -988,  -988,  -988,  -988,  -988,  0,
		0,     0,     0,     0,     -983,  -983,  -983,  -983,
		-983,  -983,  -983,  -983,  -983,  -983,  -983,  -983,
		-983,  -983,  -983,  -983,  -983,  4585,  4586,  4587,
		4588,  4589,  4590,  4591,  4592,  4593,  4594,  4595,
		4596,  -3025, 4598,  4599,  4600,  4601,  4602,  4603,
		4604,  4605,  4606,  4607,  4608,  4609,  4610,  4611,
		4612,  4613,  4614,  4615,  4616,  4617,  4618,  942,
		-209,  941,   941,   1326,  4624,  4625,  4626,  4627,
		4628,  4629,  4630,  -7320, 177,   178,   179,   180,
		4636,  4637,  4638,  4639,  4640,  4641,  187,   188,
		189,   190,   191,   192,   193,   194,   195,   4651,
		4652,  4653,  4654,  4655,  4656,  4657,  4658,  4659,
		4660,  4661,  4662,  4663,  4664,  4665,  4666,  4667,
		4668,  4669,  4670,  4671,  0,     0,     0,     0,
		0,     0,     0,     2119,  2119,  1662,  4682,  4683,
		4684,  4685,  1670,  4580,  9520,  3153,  2001,  3153,
		2130,  2130,  3153,  3153,  3153,  3153,  3153,  -4610,
		2003,  1422,  1423,  1424,  1425,  1426,  1427,  1428,
		1429,  1430,  1431,  1432,  5825,  1434,  1435,  1436,
		1437,  1438,  1439,  1440,  1441,  1442,  1443,  1444,
		1445,  1446,  1447,  1448,  3153,  3153,  3153,  3153,
		3153,  8643,  3153,  3153,  0,     0,     9390,  0,
		5993,  -1478, 0,     0,     -1480, -1480, -1480, -1480,
		-1480, -1480, -1480, -1480, -1480, 2438,  2438,  0,
		2439,  2439,  2439,  2439,  2439,  2439,  2439,  0,
		0,     2441,  0,     2442,  0,     2443,  2443,  0,
		0,     0,     2396,  0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     1373,  0,     0,     2094,  2442,
		0,     4113,  1964,  1964,  0,     0,     0,     0,
		2104,  2105,  2106,  2107,  2108,  2109,  2110,  2111,
		2112,  2113,  2114,  2115,  2116,  2117,  2118,  2119,
		2120,  2121,  2122,  2123,  2124,  2125,  2126,  2127,
		2128,  2129,  2130,  2131,  2132,  2133,  2134,  2135,
		2136,  2137,  2138,  2139,  2140,  2141,  2142,  2143,
		2144,  2145,  2146,  2147,  2148,  2149,  4849,  4850,
		4851,  4852,  4853,  4854,  4855,  4856,  4857,  4858,
		4859,  4860,  4861,  4862,  4863,  4864,  2500,  4866,
		4867,  4868,  4869,  4870,  2505,  4872,  4873,  4874,
		2508,  4876,  4877,  4878,  4879,  4880,  2513,  2150,
		2151,  2152,  2153,  2154,  2155,  0,     2157,  2158,
		2159,  2160,  2161,  2162,  2163,  2164,  2165,  2166,
		2167,  2168,  805,   2170,  2171,  2172,  2173,  2174,
		2175,  2176,  2177,  2178,  2179,  2180,  2181,  2182,
		2183,  2184,  2185,  2186,  2187,  2188,  2189,  2190,
		2191,  2192,  2193,  2194,  0,     4928,  4929,  4930,
		4931,  4932,  0,     4934,  4935,  4936,  4937,  7848,
		4939,  0,     0,     0,     4943,  4944,  4945,  4946,
		4947,  4948,  4949,  4950,  0,     4952,  4953,  4954,
		0,     0,     2546,  0,     0,     2546,  2546,  2546,
		4963,  -5990, 4965,  1530,  4967,  4968,  4969,  4970,
		4971,  8142,  8142,  8142,  8142,  1530,  2112,  4978,
		4979,  4980,  4981,  4982,  2644,  2645,  2646,  2647,
		2648,  2649,  2650,  2651,  2099,  2653,  4993,  4994,
		4995,  4996,  4997,  4998,  2092,  5000,  5001,  5002,
		5003,  5004,  5005,  287,   5007,  5008,  32767, 5009,
		5010,  5011,  5012,  -2457, 5014,  -2456, 5016,  5017,
		5018,  5019,  5020,  5021,  5022,  5023,  5024,  5025,
		5026,  5027,  5028,  -4624, 5030,  5031,  5032,  5033,
		5034,  5035,  5036,  5037,  5038,  2677,  2678,  2679,
		2680,  2681,  2682,  2683,  2684,  2685,  2686,  -6383,
		2687,  2688,  2689,  2690,  2691,  2692,  2693,  2694,
		2695,  2696,  2697,  2698,  2699,  2700,  2701,  2702,
		2703,  2704,  2705,  32767, 2706,  2707,  2708,  2709,
		32767, 32767, 32767, 32767, 2710,  4682,  4683,  32767,
		2713,  32767, 2714,  906,   907,   908,   909,   2719,
		2720,  910,   2722,  2723,  2724,  2725,  2726,  2727,
		2728,  2729,  2730,  2731,  2732,  2733,  2734,  2735,
		2736,  2737,  2738,  2739,  2740,  2741,  2742,  2743,
		2744,  2745,  2746,  2747,  2748,  2749,  2750,  2751,
		2752,  2753,  2974,  2755,  174,   2757,  2758,  2759,
		2760,  2761,  2762,  2763,  2764,  2765,  2766,  2767,
		2768,  2769,  2770,  6689,  6690,  6691,  2774,  2775,
		2776,  2777,  2778,  2779,  2780,  2781,  2782,  2783,
		2784,  2785,  2786,  2787,  2788,  2789,  2790,  2791,
		2792,  2793,  2794,  2795,  2796,  2797,  2798,  2799,
		2800,  2801,  2802,  2803,  2804,  2805,  2806,  2807,
		2808,  2809,  2810,  2811,  2812,  2813,  2814,  2815,
		2816,  5259,  2818,  3298,  3299,  2821,  2822,  2823,
		2824,  2825,  2826,  3170,  2828,  2829,  2830,  2831,
		2832,  2833,  2834,  3177,  3177,  3177,  3177,  3177,
		3177,  2841,  2842,  2843,  2844,  2845,  2846,  2847,
		2848,  32767, 32767, 32767, 32767, 32767, 2849,  2850,
		2851,  2852,  2853,  2854,  2855,  2856,  2857,  2858,
		2859,  2860,  2861,  2862,  2863,  2864,  2865,  2866,
		2867,  2868,  2869,  2870,  2871,  2872,  2873,  2874,
		2875,  2876,  2877,  2878,  2879,  2880,  2881,  2882,
		2883,  -1495, 2885,  2886,  4004,  4005,  4006,  4007,
		4008,  -1495, -1495, 2894,  2895,  -1495, -1495, 2898,
		2899,  2900,  2901,  2902,  2903,  2904,  2905,  -1494,
		2907,  2908,  2909,  2910,  2911,  2912,  2913,  -1494,
		2915,  2916,  2917,  2918,  3677,  2920,  3680,  3681,
		3129,  2924,  2925,  2926,  2927,  2928,  2929,  2930,
		2931,  2932,  2933,  2934,  2935,  2936,  2937,  2938,
		2939,  2940,  2941,  2942,  2943,  2944,  2945,  2946,
		2947,  2948,  2949,  2950,  2951,  2952,  2953,  2954,
		2955,  2956,  -1468, -1468, 2959,  2960,  2961,  2962,
		2963,  2964,  2965,  2966,  2967,  2968,  2969,  2970,
		2971,  2972,  2973,  2974,  2975,  2976,  2977,  2978,
		2979,  2980,  2981,  2982,  2983,  2984,  2985,  2986,
		2987,  2988,  2989,  2990,  2991,  2992,  2993,  2994,
		2995,  2996,  2997,  2998,  2999,  3000,  3001,  3002,
		3003,  3004,  3005,  3006,  3007,  3008,  3009,  3010,
		3011,  3012,  3013,  3014,  3015,  3016,  3017,  3018,
		3019,  3020,  3021,  3022,  3023,  3024,  3025,  3026,
		3027,  3028,  3029,  3030,  3031,  3032,  3033,  3034,
		3035,  3036,  3037,  3038,  32767, 32767, 32767, 3039,
		3040,  3041,  3042,  3043,  3044,  32767, 32767, 3045,
		3046,  3047,  3048,  3049,  3050,  32767, 32767, 3051,
		3052,  3053,  3054,  3055,  3056,  32767, 32767, 3057,
		3058,  3059,  32767, 32767, 32767, 3060,  3061,  3062,
		3063,  3064,  3065,  3066,  32767, 3067,  3068,  3069,
		3070,  3071,  3072,  3073,  32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 0,     32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		0,     0,     0,     0,     32767, 0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		32767, 0,     0,     32767, 0,     32767, 32767, 0,
		32767, 0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     32767, 0,     0,     0,     0,
		32767, 0,     32767, 0,     32767, 32767, 32767, 32767,
		32767, 32767, 0,     32767, 32767, 32767, 32767, 0,
		32767, 0,     32767, 0,     32767, 0,     0,     0,
		32767, 0,     0,     32767, 0,     32767, 32767, 0,
		32767, 0,     32767, 0,     32767, 0,     32767, 0,
		32767, 0,     0,     32767, 0,     32767, 32767, 0,
		0,     0,     0,     32767, 0,     0,     0,     0,
		0,     0,     0,     32767, 0,     0,     0,     0,
		32767, 0,     0,     0,     0,     32767, 0,     32767,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     32767, 0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     32767, 32767, 32767, 32767,
		32767, 0,     0,     0,     32767, 0,     0,     0,
		0,     0,     32767, -1358, -1358, 0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, -847,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		5181,  32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, -2069, 32767, 32767, -2071,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 0,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 0,     -2105, -2105, 32767, 32767, -2107,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 256,
		257,   258,   32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 0,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 4210,  4211,  4212,  4213,  32767,
		4214,  4215,  4216,  4217,  4218,  4219,  4220,  4221,
		4222,  4223,  4224,  4225,  4226,  4227,  4228,  4229,
		4230,  4231,  4232,  4233,  4234,  4235,  4236,  4237,
		4238,  4239,  4240,  32767, 4241,  4242,  32767, 4243,
		32767, 32767, 4244,  32767, 4245,  4246,  4247,  4248,
		4249,  4250,  4251,  4252,  4253,  4254,  32767, 4255,
		4256,  4257,  4258,  32767, 4259,  32767, 4260,  32767,
		32767, 32767, 32767, 32767, 32767, 4261,  32767, 32767,
		32767, 32767, 4262,  32767, 4263,  32767, 4264,  32767,
		4265,  4266,  4267,  32767, 4268,  4269,  32767, 4270,
		32767, 32767, 4271,  32767, 4272,  32767, 4273,  32767,
		4274,  32767, 4275,  32767, 4276,  4277,  32767, 4278,
		32767, 32767, 4279,  4280,  4281,  4282,  32767, 4283,
		4284,  4285,  4286,  4287,  4288,  4289,  32767, 4290,
		4291,  4292,  4293,  32767, 4294,  4295,  4296,  4297,
		32767, 4298,  32767, 4299,  4300,  4301,  4302,  4303,
		4304,  4305,  4306,  4307,  4308,  0,     4309,  4310,
		4311,  4312,  4313,  4314,  4315,  4316,  4317,  4318,
		4319,  4320,  4321,  4322,  4323,  4324,  4325,  0,
		0,     0,     0,     32767, 4326,  4327,  4328,  32767,
		4329,  4330,  4331,  4332,  4333,  32767, 5692,  5693,
		4336,  4337,  4338,  4339,  4340,  4341,  4342,  4343,
		4344,  4345,  4346,  4347,  4348,  4349,  4350,  0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     483,   0,     0,
		0,     0,     32767, 32767, 0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     -1717, 0,     32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 0,     0,     0,     0,     0,     0,
		0,     0,     0,     -1769, 0,     0,     0,     0,
		0,     0,     0,     0,     0,     -1778, 0,     0,
		0,     0,     -1782, 0,     0,     0,     0,     -1786,
		0,     0,     0,     0,     -1790, 0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     -1802, 0,     0,     0,     0,     0,     0,
		0,     0,     0,     -1811, 0,     -1812, -1812, -1812,
		-1812, -1812, 0,     0,     0,     0,     0,     0,
		0,     -1819, 0,     0,     0,     0,     0,     0,
		-459,  -458,  0,     0,     0,     0,     -3016, 0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		32767, 32767, 32767, 32767, 32767, 32767, 0,     32767,
		0,     32767, 0,     0,     32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		223,   32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     4379,
		0,     0,     -1117, -1117, -1117, -1117, -1117, 4387,
		4388,  0,     0,     4391,  4392,  0,     0,     0,
		0,     0,     0,     0,     0,     4400,  0,     0,
		0,     0,     0,     0,     0,     4408,  0,     0,
		0,     0,     -758,  0,     -759,  -759,  -206,  0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		4425,  4426,  0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     32767, 32767, 32767, 0,     0,     0,
		0,     0,     0,     32767, 32767, 0,     0,     0,
		0,     0,     0,     32767, 32767, 0,     0,     0,
		0,     0,     0,     32767, 32767, 0,     0,     0,
		32767, 32767, 32767, 0,     0,     0,     0,     0,
		0,     0,     32767, 0,     0,     0,     0,     0,
		0,     0,     32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 4429,
		4430,  4431,  32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 4432,
		4433,  4434,  4435,  4436,  4437,  4438,  4439,  4440,
		4441,  4442,  4443,  4444,  4445,  4446,  4447,  4448,
		4449,  4450,  4451,  4452,  4453,  4454,  4455,  4456,
		4457,  4458,  4459,  4460,  4461,  4462,  4463,  4464,
		4465,  4466,  4467,  4468,  4469,  4470,  4471,  4472,
		4473,  4474,  4475,  32767, 32767, 32767, 32767, 4476,
		4477,  4478,  4479,  4480,  4481,  539,   540,   4484,
		0,     0,     0,     0,     0,     0,     0,     4485,
		1146,  0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     -750,  0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     6227,
		6228,  -3161, 6230,  238,   234,   6232,  6233,  7714,
		7715,  7716,  7717,  7718,  7719,  7720,  7721,  7722,
		7723,  -1929, 7725,  7726,  7727,  7728,  7729,  7730,
		7731,  7732,  7733,  5372,  5373,  5374,  5375,  5376,
		5377,  5378,  -3687, 5381,  5382,  -3687, 5383,  5384,
		5385,  5386,  5387,  5388,  5389,  5390,  5391,  -3687,
		-3687, -3687, -3687, -3687, -3687, -3687, -3687, -3687,
		3129,  32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, -3700, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		285,   -3734, -3734, 288,   289,   -3734, -3734, -3734,
		-3734, -3734, -3734, -3734, -3734, -3734, -3734, -3734,
		-3734, 1547,  -956,  -956,  -3734, -3734, -3734, 3440,
		-3734, -3734, -3734, -3734, -3734, -3734, -3734, -3734,
		-3734, -3734, -3734, -3734, -3734, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		322,   323,   32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 0,     32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 114,   115,   116,   117,   118,   119,   120,
		121,   32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 0,
		0,     0,     32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     32767, 32767, 32767, 32767, 0,
		0,     0,     0,     0,     0,     3943,  3943,  0,
		0,     32767, 32767, 32767, 32767, 32767, 32767, 0,
		3340,  32767, 325,   32767, 326,   32767, 327,   32767,
		328,   32767, 329,   32767, 330,   32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 331,   32767, 332,   333,   334,   335,
		336,   32767, 32767, 32767, 32767, 32767, 32767, 32767,
		337,   32767, 338,   32767, 339,   340,   341,   32767,
		32767, 32767, 342,   32767, 32767, 32767, 32767, 32767,
		32767, 32767, 343,   32767, 344,   345,   346,   32767,
		32767, 32767, 347,   32767, 32767, 32767, 32767, 32767,
		32767, 32767, 348,   32767, 349,   350,   351,   32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		352,   32767, 353,   32767, 354,   355,   32767, 32767,
		-1816, -1816, -1816, -1816, -1816, -1816, -1816, -1816,
		-1816, -1816, -1816, 32767, 32767, 32767, 32767, 32767,
		32767, -1822, 32767, 32767, 32767, 32767, 32767, 368,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 369,   370,   371,   32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 372,
		32767, 32767, 32767, 373,   374,   32767, 375,   376,
		32767, 32767, 32767, 32767, 6367,  0,     -1152, 0,
		-1023, -1023, 0,     0,     0,     0,     0,     -7763,
		-1150, -1731, -1730, -1729, -1728, -1727, -1726, -1725,
		-1724, -1723, -1722, -1721, 2672,  -1719, -1718, -1717,
		-1716, -1715, -1714, -1713, -1712, -1711, -1710, -1709,
		-1708, -1707, -1706, -1705, 0,     0,     0,     0,
		0,     5490,  0,     0,     -3153, -3153, 6237,  -3153,
		2840,  -4631, -3153, -3153, -4633, -4633, -4633, -4633,
		-4633, -4633, -4633, -4633, -4633, -4633, 5020,  -4633,
		-4633, -4633, -4633, -4633, -4633, -4633, -4633, -4633,
		-2271, -2271, -2271, -2271, -2271, -2271, -2271, 6795,
		-2272, -2272, 6798,  -2271, -2271, -2271, -2271, -2271,
		-2271, -2271, -2271, -2271, -2271, 6808,  6809,  6810,
		6811,  6812,  6813,  6814,  6815,  0,     0,     0,
		-2281, 0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     2579,  7519,  1152,  0,     0,     128,   128,
		1151,  0,     1150,  1150,  1150,  -6613, 0,     -581,
		-580,  -579,  -578,  -577,  -576,  395,   -575,  -574,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     -1653, 0,     -1652, -1651, 0,     0,
		-1650, -1649, -1648, -1516, -1647, 473,   474,   475,
		476,   477,   32767, 32767, 32767, 32767, -7018, 479,
		480,   481,   482,   32767, 32767, 32767, 32767, 32767,
		32767, 483,   484,   485,   486,   487,   488,   489,
		490,   491,   492,   493,   494,   495,   496,   497,
		498,   499,   500,   501,   502,   503,   504,   9308,
		506,   507,   508,   509,   510,   511,   512,   513,
		514,   515,   3684,  3684,  3684,  519,   3685,  3685,
		9912,  9913,  524,   9915,  3923,  3919,  9917,  9918,
		11399, 0,     11402, 11403, 11404, 11405, 11406, 11407,
		11408, 11409, 1757,  11411, 11412, 11413, 11414, 11415,
		11416, 11417, 11418, 11419, 0,     9060,  9061,  9062,
		9063,  9064,  9065,  0,     9068,  9069,  0,     9070,
		9071,  9072,  9073,  9074,  9075,  9076,  9077,  9078,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     2659,  0,     0,     0,     0,
		0,     0,     0,     0,     0,     2745,  2745,  2745,
		2745,  2745,  2745,  2745,  2745,  2745,  0,     0,
		0,     -4730, 6052,  0,     -4732, -4732, -4732, -4732,
		0,     0,     4919,  -4734, -4734, 0,     -4735, -4735,
		-4735, 4019,  0,     0,     4022,  4023,  0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     5281,  2778,  2778,  0,     0,     0,
		7174,  0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     2067,  918,
		32767, 534,   -2763, 0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 0,     0,     0,     0,     0,
		0,     0,     0,     553,   0,     32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, -2360, 5111,  -2360, -2360, -2360,
		-2360, -2360, -2360, -2360, -2360, -2360, -2360, -2360,
		-2360, -2360, 7293,  -2360, -2360, -2360, -2360, -2360,
		3144,  3145,  -2362, -2362, 0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     32767, 0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     32767, 0,     0,     0,     0,     32767,
		32767, 32767, 32767, 0,     -1971, -1971, 32767, 0,
		32767, 0,     1809,  1809,  1809,  1809,  0,     0,
		1811,  0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     -220,  0,     2582,  0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     -3918, -3918, -3918, 0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		-2442, 0,     -479,  -479,  0,     0,     0,     0,
		0,     0,     -343,  0,     0,     0,     0,     0,
		0,     0,     -342,  -341,  -340,  -339,  -338,  -337,
		0,     0,     0,     0,     0,     0,     0,     0,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 2495,  2496,  0,     0,     2499,  2500,  2501,
		2502,  0,     2504,  2505,  2506,  2507,  2508,  1141,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     -363,
		0,     -362,  0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     -2546, -2546, 0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     484,   100,   -3197, 0,     -3198, -3198,
		0,     0,     0,     8750,  -3943, 0,     0,     -3943,
		542,   543,   544,   545,   546,   547,   548,   -3936,
		-596,  551,   552,   553,   554,   555,   556,   557,
		558,   559,   560,   561,   562,   563,   564,   565,
		566,   567,   568,   569,   570,   571,   572,   573,
		574,   575,   576,   577,   578,   579,   580,   581,
		582,   583,   584,   585,   586,   587,   588,   589,
		590,   591,   592,   593,   594,   595,   596,   597,
		598,   599,   600,   601,   602,   603,   604,   605,
		606,   607,   608,   609,   610,   611,   612,   613,
		614,   615,   616,   617,   618,   619,   620,   621,
		622,   623,   624,   1375,  626,   627,   628,   629,
		630,   631,   632,   633,   634,   635,   636,   637,
		638,   639,   640,   641,   642,   643,   644,   645,
		646,   647,   648,   649,   650,   651,   652,   653,
		654,   655,   656,   657,   658,   659,   660,   661,
		662,   663,   664,   665,   666,   667,   668,   669,
		670,   671,   672,   673,   674,   675,   676,   32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		0,     32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 32767,
		32767, 32767, 32767, 32767, 32767, 32767, 32767, 0,
		0,     0,     0,     7470,  0,     7471,  0,     0,
		0,     0,     0,     0,     0,     0,     0,     0,
		0,     0,     0,     9653,  0,     0,     0,     0,
		0,     0,     0,     0,     0,     32767, 32767, 32767,
		3152,  3153,  3154,  3155,  3156,  3157,  3158,  3159,
		3160,  3161,  3162,  3163,  3164,  3165,  3166,  3167,
		3168,  3169,  3170,  3171,  3172,  3173,  3174,  3175,
		3176,  3177,  3178,  3179,  3180,  3181,  3182,  3183,
		3184,  3185,  3186,  3187,  3188,  3189,  3190,  3191,
		3192,  3193,  3194,  3195,  3196,  3197,  3198,  3199,
		3200,  3201,  3202,  3203,  3204,  3205,  3206,  3207,
		3208,  3209,  3210,  3211,  3212,  3213,  3214,  3215,
		3216,  3217,  3218,  3219,  3220,  3221,  3222,  3223,
		3224,  3225,  3226,  3227,  3228,  3229,  3230,  3231,
		3232,  3233,  3234,  3235,  3236,  32767, 3237,  3238,
		3239,  3240,  3241,  3242,  3243,  3244,  3245,  3246,
		3247,  3248,  1885,  3250,  3251,  3252,  3253,  3254,
		3255,  3256,  3257,  3258,  3259,  3260,  3261,  3262,
		3263,  3264,  3265,  3266,  3267,  3268,  3269,  3270,
		3271,  3272,  3273,  3274,  3275,  3276,  3277,  3278,
		3279,  3280,  3281,  3282,  3283,  3284,  3285,  3286,
		3287,  3288,  3289,  3290,  7683,  3292,  3293,  3294,
		3295,  3296,  3297,  3298,  3299,  3300,  3301,  3302,
		3303,  3304,  3305,  3306,  3307,  32767, 3308,  3309,
		32767, 32767, 3310,  32767, 32767, 3311,  3312,  32767,
		32767, 3313,  3314,  3315,  3316,  32767, 3317,  3318,
		3319,  3320,  3321,  3322,  3323,  3324,  3325,  3326,
		-1186, -1186, 4489,  -1187, 4491,  -1188, -1188, -1188,
		-1188, -1188, 3335,  3336,  32767, 3337,  3338,  3339,
		3340,  3341,  3342,  3343,  3344,  3345,  3346,  3347,
		3348,  -1659, 3350,  3351,  3352,  3353,  3354,  3355,
		3356,  3357,  3358,  3359,  3360,  3361,  3362,  4351,
		4352,  4353,  4354,  4355,  4356,  4357,  4358,  4359,
		4360,  4361,  3374,  3375,  3376,  3377,  3378,  4362,
		4363,  4364,  4365,  4366,  4367,  4368,  4369,  4370,
		4371,  4372,  4373,  4374,  4375,  4376,  4377,  4378,
		0,     4380,  4381,  5499,  5500,  5501,  5502,  5503,
		0,     0,     4389,  4390,  0,     0,     3406,  4393,
		4394,  4395,  4396,  4397,  4398,  4399,  0,     4401,
		4402,  4403,  4404,  4405,  4406,  4407,  0,     4409,
		4410,  4411,  4412,  5171,  4414,  5174,  5175,  4623,
		4418,  4419,  4420,  4421,  4422,  4423,  4424,  3437,
		3438,  3439,  3440,  3441,  3442,  3443,  3444,  3445,
		3446,  3447,  3448,  32767, 3449,  3450,  3451,  3131,
		32767, 3453,  3454,  3455,  3456,  3457,  32767, 3458,
		32767, 0,     0,     4427,  3460,  3461,  3462,  3463,
		3464,  3465,  32767, 3466,  3467,  3468,  3469,  3470,
		3471,  3472,  3473,  3474,  3475,  3476,  3477,  3478,
		3146,  3367,  3148,  567,   3150,  3151,  3485,  3486,
		3487,  3488,  3489,  3490,  3491,  3492,  3493,  4428,
		3495,  3496,  3497,  3498,  3499,  3500,  3501,  3502,
		3503,  3504,  3505,  3506,  3507,  3508,  3509,  3510,
		3511,  3512,  3513,  3514,  3515,  3516,  3517,  3518,
		3519,  3520,  3521,  3522,  3523,  3524,  3525,  3526,
		3527,  3528,  3529,  3530,  3531,  3532,  3533,  3534,
		3535,  3536,  3537,  3538,  3539,  3540,  3541
	};

	const unsigned char *k = (const unsigned char *) key;
	size_t		keylen = 4;
	uint32		a = 0;
	uint32		b = 1;

	while (keylen--)
	{
		unsigned char c = *k++;

		a = a * 257 + c;
		b = b * 8191 + c;
	}
	return h[a % 10079] + h[b % 10079];
}

/* Hash lookup information for NFKC_QC */
static const pg_unicode_norminfo UnicodeNormInfo_NFKC_QC = {
	UnicodeNormProps_NFKC_QC,
	NFKC_QC_hash_func,
	5039
};
