/*-------------------------------------------------------------------------
 *
 * pg_partitioned_table_d.h
 *    Macro definitions for pg_partitioned_table
 *
 * Portions Copyright (c) 1996-2023, PostgreSQL Global Development Group
 * Portions Copyright (c) 1994, Regents of the University of California
 *
 * NOTES
 *  ******************************
 *  *** DO NOT EDIT THIS FILE! ***
 *  ******************************
 *
 *  It has been GENERATED by src/backend/catalog/genbki.pl
 *
 *-------------------------------------------------------------------------
 */
#ifndef PG_PARTITIONED_TABLE_D_H
#define PG_PARTITIONED_TABLE_D_H

#define PartitionedRelationId 3350
#define PartitionedRelidIndexId 3351

#define Anum_pg_partitioned_table_partrelid 1
#define Anum_pg_partitioned_table_partstrat 2
#define Anum_pg_partitioned_table_partnatts 3
#define Anum_pg_partitioned_table_partdefid 4
#define Anum_pg_partitioned_table_partattrs 5
#define Anum_pg_partitioned_table_partclass 6
#define Anum_pg_partitioned_table_partcollation 7
#define Anum_pg_partitioned_table_partexprs 8

#define Natts_pg_partitioned_table 8


#endif							/* PG_PARTITIONED_TABLE_D_H */
