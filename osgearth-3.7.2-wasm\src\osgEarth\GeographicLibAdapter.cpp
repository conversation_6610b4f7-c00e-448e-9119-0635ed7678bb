#include "GeographicLibAdapter.h"
#include <osgEarth/Notify>
#include <GeographicLib/TransverseMercator.hpp>
#include <GeographicLib/UTMUPS.hpp>
#include <GeographicLib/Constants.hpp>
#include <GeographicLib/Geodesic.hpp>
#include <cmath>
#include <sstream>
#include <algorithm>
#include <unordered_map>
#include <iostream>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

using namespace osgEarth;
using namespace GeographicLib;

#define LC "[GeographicLibAdapter] "

// Web墨卡托常数
static const double WEB_MERCATOR_MAX_LAT = 85.0511287798; // 最大纬度
static const double EARTH_RADIUS = 6378137.0;             // WGS84椭球长半轴
// 使用与osgEarth一致的精确值
static const double WEB_MERCATOR_MAX_X = 20037508.34278925; // 最大X坐标
static const double WEB_MERCATOR_MAX_Y = 20037508.34278925; // 最大Y坐标

GeographicLibAdapter::GeographicLibAdapter()
{
}

GeographicLibAdapter::~GeographicLibAdapter()
{
}

GeographicLibAdapter::ProjectionParams
GeographicLibAdapter::parseProj4String(const std::string &proj4)
{
    ProjectionParams params;

    if (proj4.empty())
        return params;

    // 解析PROJ4参数
    std::unordered_map<std::string, std::string> paramMap;
    std::string cleanString = proj4;

    // 移除开头的'+'并分割参数
    std::replace(cleanString.begin(), cleanString.end(), '+', ' ');
    std::stringstream ss(cleanString);
    std::string param;

    while (ss >> param)
    {
        if (param.empty())
            continue;

        size_t eqPos = param.find('=');
        if (eqPos != std::string::npos)
        {
            std::string key = param.substr(0, eqPos);
            std::string value = param.substr(eqPos + 1);
            paramMap[key] = value;
        }
        else
        {
            paramMap[param] = "true";
        }
    }

    // 解析投影类型
    auto projIt = paramMap.find("proj");
    if (projIt != paramMap.end())
    {
        std::string projType = toLower(projIt->second);
        if (projType == "longlat" || projType == "latlong")
        {
            params.type = GEOGRAPHIC;
        }
        else if (projType == "merc" || projType == "webmerc")
        {
            params.type = WEB_MERCATOR;
        }
        else if (projType == "utm")
        {
            params.type = UTM;
            auto zoneIt = paramMap.find("zone");
            if (zoneIt != paramMap.end())
            {
                params.utmZone = parseInt(zoneIt->second);
            }
            params.isNorthern = paramMap.find("south") == paramMap.end();
        }
        else if (projType == "tmerc")
        {
            params.type = TRANSVERSE_MERCATOR;
        }
        else
        {
            params.type = UNKNOWN;
        }
    }

    // 解析其他参数
    auto lon0It = paramMap.find("lon_0");
    if (lon0It != paramMap.end())
    {
        params.centralMeridian = parseDouble(lon0It->second);
    }

    auto x0It = paramMap.find("x_0");
    if (x0It != paramMap.end())
    {
        params.falseEasting = parseDouble(x0It->second);
    }

    auto y0It = paramMap.find("y_0");
    if (y0It != paramMap.end())
    {
        params.falseNorthing = parseDouble(y0It->second);
    }

    auto kIt = paramMap.find("k");
    if (kIt != paramMap.end())
    {
        params.scaleFactor = parseDouble(kIt->second, 1.0);
    }

    return params;
}

GeographicLibAdapter::ProjectionParams
GeographicLibAdapter::parseWKTString(const std::string &wkt)
{
    ProjectionParams params;

    if (wkt.empty())
        return params;

    std::string wktLower = toLower(wkt);

    // 简单的WKT解析
    if (wktLower.find("geogcs") != std::string::npos ||
        wktLower.find("geogcrs") != std::string::npos)
    {
        params.type = GEOGRAPHIC;
    }
    else if (wktLower.find("projcs") != std::string::npos ||
             wktLower.find("projcrs") != std::string::npos)
    {
        if (wktLower.find("mercator") != std::string::npos)
        {
            if (wktLower.find("spherical") != std::string::npos ||
                wktLower.find("web") != std::string::npos)
            {
                params.type = WEB_MERCATOR;
            }
            else
            {
                params.type = TRANSVERSE_MERCATOR;
            }
        }
        else if (wktLower.find("utm") != std::string::npos)
        {
            params.type = UTM;
            // 尝试从WKT中提取UTM区域号
            // 这里可以添加更复杂的解析逻辑
        }
        else if (wktLower.find("transverse_mercator") != std::string::npos)
        {
            params.type = TRANSVERSE_MERCATOR;
        }
        else
        {
            params.type = UNKNOWN;
        }
    }

    return params;
}

GeographicLibAdapter::TransformResult
GeographicLibAdapter::geographicToWebMercator(double lon, double lat)
{
    if (!isValidGeographic(lon, lat))
    {
        return TransformResult(false, 0, 0, 0);
    }

    // 限制纬度范围
    if (lat > WEB_MERCATOR_MAX_LAT)
        lat = WEB_MERCATOR_MAX_LAT;
    else if (lat < -WEB_MERCATOR_MAX_LAT)
        lat = -WEB_MERCATOR_MAX_LAT;

    // Web墨卡托投影公式
    double x = EARTH_RADIUS * lon * M_PI / 180.0;
    double y = EARTH_RADIUS * log(tan(M_PI / 4.0 + lat * M_PI / 360.0));

    return TransformResult(true, x, y, 0);
}

GeographicLibAdapter::TransformResult
GeographicLibAdapter::webMercatorToGeographic(double x, double y)
{
    if (!isValidWebMercator(x, y))
    {
        return TransformResult(false, 0, 0, 0);
    }

    // Web墨卡托逆投影公式
    double lon = x / EARTH_RADIUS * 180.0 / M_PI;
    double lat = (2.0 * atan(exp(y / EARTH_RADIUS)) - M_PI / 2.0) * 180.0 / M_PI;

    return TransformResult(true, lon, lat, 0);
}

GeographicLibAdapter::TransformResult
GeographicLibAdapter::geographicToUTM(double lon, double lat, int zone, bool isNorthern)
{
    if (!isValidGeographic(lon, lat))
    {
        return TransformResult(false, 0, 0, 0);
    }

    try
    {
        if (zone == 0)
        {
            zone = calculateUTMZone(lon);
        }

        double x, y;
        int zoneOut;
        bool northpOut;

        UTMUPS::Forward(lat, lon, zoneOut, northpOut, x, y);

        return TransformResult(true, x, y, 0);
    }
    catch (const std::exception &e)
    {
        OE_WARN << LC << "UTM conversion failed: " << e.what() << std::endl;
        return TransformResult(false, 0, 0, 0);
    }
}

GeographicLibAdapter::TransformResult
GeographicLibAdapter::utmToGeographic(double x, double y, int zone, bool isNorthern)
{
    try
    {
        double lat, lon;
        UTMUPS::Reverse(zone, isNorthern, x, y, lat, lon);

        return TransformResult(true, lon, lat, 0);
    }
    catch (const std::exception &e)
    {
        OE_WARN << LC << "UTM inverse conversion failed: " << e.what() << std::endl;
        return TransformResult(false, 0, 0, 0);
    }
}

GeographicLibAdapter::TransformResult
GeographicLibAdapter::geographicToTransverseMercator(double lon, double lat, const ProjectionParams &params)
{
    if (!isValidGeographic(lon, lat))
    {
        return TransformResult(false, 0, 0, 0);
    }

    try
    {
        TransverseMercator tm(Constants::WGS84_a(), Constants::WGS84_f(), params.scaleFactor);
        double x, y;
        tm.Forward(params.centralMeridian, lat, lon, x, y);

        x += params.falseEasting;
        y += params.falseNorthing;

        return TransformResult(true, x, y, 0);
    }
    catch (const std::exception &e)
    {
        OE_WARN << LC << "Transverse Mercator conversion failed: " << e.what() << std::endl;
        return TransformResult(false, 0, 0, 0);
    }
}

GeographicLibAdapter::TransformResult
GeographicLibAdapter::transverseMercatorToGeographic(double x, double y, const ProjectionParams &params)
{
    try
    {
        x -= params.falseEasting;
        y -= params.falseNorthing;

        TransverseMercator tm(Constants::WGS84_a(), Constants::WGS84_f(), params.scaleFactor);
        double lat, lon;
        tm.Reverse(params.centralMeridian, x, y, lat, lon);

        return TransformResult(true, lon, lat, 0);
    }
    catch (const std::exception &e)
    {
        OE_WARN << LC << "Transverse Mercator inverse conversion failed: " << e.what() << std::endl;
        return TransformResult(false, 0, 0, 0);
    }
}

GeographicLibAdapter::TransformResult
GeographicLibAdapter::transform(double x, double y, double z,
                                const ProjectionParams &fromParams,
                                const ProjectionParams &toParams)
{
    // 如果源和目标投影相同，直接返回
    if (fromParams.type == toParams.type)
    {
        return TransformResult(true, x, y, z);
    }

    // 先转换到地理坐标系（WGS84）
    TransformResult geoResult;

    switch (fromParams.type)
    {
    case GEOGRAPHIC:
        geoResult = TransformResult(true, x, y, z);
        break;
    case WEB_MERCATOR:
        geoResult = webMercatorToGeographic(x, y);
        break;
    case UTM:
        geoResult = utmToGeographic(x, y, fromParams.utmZone, fromParams.isNorthern);
        break;
    case TRANSVERSE_MERCATOR:
        geoResult = transverseMercatorToGeographic(x, y, fromParams);
        break;
    default:
        return TransformResult(false, 0, 0, 0);
    }

    if (!geoResult.success)
        return geoResult;

    // 再从地理坐标系转换到目标投影
    switch (toParams.type)
    {
    case GEOGRAPHIC:
        return geoResult;
    case WEB_MERCATOR:
        return geographicToWebMercator(geoResult.x, geoResult.y);
    case UTM:
        return geographicToUTM(geoResult.x, geoResult.y, toParams.utmZone, toParams.isNorthern);
    case TRANSVERSE_MERCATOR:
        return geographicToTransverseMercator(geoResult.x, geoResult.y, toParams);
    default:
        return TransformResult(false, 0, 0, 0);
    }
}

bool GeographicLibAdapter::transformArray(double *x, double *y, double *z, unsigned count,
                                          const ProjectionParams &fromParams,
                                          const ProjectionParams &toParams)
{
    if (!x || !y || count == 0)
        return false;

    bool allSuccess = true;

    for (unsigned i = 0; i < count; ++i)
    {
        double zVal = z ? z[i] : 0.0;
        TransformResult result = transform(x[i], y[i], zVal, fromParams, toParams);

        if (result.success)
        {
            x[i] = result.x;
            y[i] = result.y;
            if (z)
                z[i] = result.z;
        }
        else
        {
            allSuccess = false;
        }
    }

    return allSuccess;
}

int GeographicLibAdapter::calculateUTMZone(double lon)
{
    // 标准UTM区域计算
    return static_cast<int>(floor((lon + 180.0) / 6.0)) + 1;
}

bool GeographicLibAdapter::isValidGeographic(double lon, double lat)
{
    return (lon >= -180.0 && lon <= 180.0 && lat >= -90.0 && lat <= 90.0);
}

bool GeographicLibAdapter::isValidWebMercator(double x, double y)
{
    return (std::abs(x) <= WEB_MERCATOR_MAX_X && std::abs(y) <= WEB_MERCATOR_MAX_Y);
}

std::string GeographicLibAdapter::trim(const std::string &str)
{
    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == std::string::npos)
        return "";

    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

std::string GeographicLibAdapter::toLower(const std::string &str)
{
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::tolower);
    return result;
}

double GeographicLibAdapter::parseDouble(const std::string &str, double defaultValue)
{
    try
    {
        return std::stod(trim(str));
    }
    catch (...)
    {
        return defaultValue;
    }
}

int GeographicLibAdapter::parseInt(const std::string &str, int defaultValue)
{
    try
    {
        return std::stoi(trim(str));
    }
    catch (...)
    {
        return defaultValue;
    }
}
