#include "GeographicLibAdapter.h"
#include <osgEarth/Notify>
#include <GeographicLib/TransverseMercator.hpp>
#include <GeographicLib/UTMUPS.hpp>
#include <GeographicLib/Constants.hpp>
#include <GeographicLib/Geodesic.hpp>
#include <cmath>
#include <sstream>
#include <algorithm>
#include <unordered_map>
#include <iostream>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

using namespace osgEarth;
using namespace GeographicLib;

#define LC "[GeographicLibAdapter] "

// Web墨卡托常数
static const double WEB_MERCATOR_MAX_LAT = 85.0511287798; // 最大纬度
static const double EARTH_RADIUS = 6378137.0;             // WGS84椭球长半轴
// 使用与osgEarth一致的精确值
static const double WEB_MERCATOR_MAX_X = 20037508.34278925; // 最大X坐标
static const double WEB_MERCATOR_MAX_Y = 20037508.34278925; // 最大Y坐标

// 辅助函数
static std::string toLower(const std::string& str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::tolower);
    return result;
}

static int parseInt(const std::string& str) {
    try {
        return std::stoi(str);
    } catch (...) {
        return 0;
    }
}

static double parseDouble(const std::string& str) {
    try {
        return std::stod(str);
    } catch (...) {
        return 0.0;
    }
}

GeographicLibAdapter::GeographicLibAdapter() : isValid_(false)
{
}

GeographicLibAdapter::~GeographicLibAdapter()
{
}

GeographicLibAdapter::ProjectionParams
GeographicLibAdapter::parseProj4String(const std::string &proj4)
{
    ProjectionParams params;

    if (proj4.empty())
        return params;

    // 解析PROJ4参数
    std::unordered_map<std::string, std::string> paramMap;
    std::string cleanString = proj4;

    // 移除开头的'+'并分割参数
    std::replace(cleanString.begin(), cleanString.end(), '+', ' ');
    std::stringstream ss(cleanString);
    std::string param;

    while (ss >> param)
    {
        if (param.empty())
            continue;

        size_t eqPos = param.find('=');
        if (eqPos != std::string::npos)
        {
            std::string key = param.substr(0, eqPos);
            std::string value = param.substr(eqPos + 1);
            paramMap[key] = value;
        }
        else
        {
            paramMap[param] = "true";
        }
    }

    // 解析投影类型
    auto projIt = paramMap.find("proj");
    if (projIt != paramMap.end())
    {
        std::string projType = toLower(projIt->second);
        if (projType == "longlat" || projType == "latlong")
        {
            params.type = GEOGRAPHIC;
        }
        else if (projType == "merc" || projType == "webmerc")
        {
            params.type = WEB_MERCATOR;
        }
        else if (projType == "utm")
        {
            params.type = UTM;
            auto zoneIt = paramMap.find("zone");
            if (zoneIt != paramMap.end())
            {
                params.utmZone = parseInt(zoneIt->second);
            }
            params.isNorthern = paramMap.find("south") == paramMap.end();
        }
        else if (projType == "tmerc")
        {
            params.type = TRANSVERSE_MERCATOR;
        }
    }

    // 解析其他参数
    auto lonIt = paramMap.find("lon_0");
    if (lonIt != paramMap.end())
    {
        params.centralMeridian = parseDouble(lonIt->second);
    }

    auto xIt = paramMap.find("x_0");
    if (xIt != paramMap.end())
    {
        params.falseEasting = parseDouble(xIt->second);
    }

    auto yIt = paramMap.find("y_0");
    if (yIt != paramMap.end())
    {
        params.falseNorthing = parseDouble(yIt->second);
    }

    auto kIt = paramMap.find("k_0");
    if (kIt != paramMap.end())
    {
        params.scaleFactor = parseDouble(kIt->second);
    }

    return params;
}

GeographicLibAdapter::ProjectionParams
GeographicLibAdapter::parseWKTString(const std::string &wkt)
{
    ProjectionParams params;
    
    if (wkt.empty())
        return params;

    std::string upperWkt = wkt;
    std::transform(upperWkt.begin(), upperWkt.end(), upperWkt.begin(), ::toupper);

    // 简单的WKT解析
    if (upperWkt.find("GEOGCS") != std::string::npos || 
        upperWkt.find("GEOGRAPHIC") != std::string::npos)
    {
        params.type = GEOGRAPHIC;
    }
    else if (upperWkt.find("MERCATOR") != std::string::npos)
    {
        if (upperWkt.find("WEB") != std::string::npos || 
            upperWkt.find("3857") != std::string::npos)
        {
            params.type = WEB_MERCATOR;
        }
        else
        {
            params.type = TRANSVERSE_MERCATOR;
        }
    }
    else if (upperWkt.find("UTM") != std::string::npos)
    {
        params.type = UTM;
        // 尝试提取UTM区域号
        size_t zonePos = upperWkt.find("ZONE");
        if (zonePos != std::string::npos)
        {
            std::string zoneStr = upperWkt.substr(zonePos + 4);
            size_t numStart = zoneStr.find_first_of("0123456789");
            if (numStart != std::string::npos)
            {
                size_t numEnd = zoneStr.find_first_not_of("0123456789", numStart);
                if (numEnd != std::string::npos)
                {
                    params.utmZone = parseInt(zoneStr.substr(numStart, numEnd - numStart));
                }
            }
        }
        params.isNorthern = upperWkt.find("SOUTH") == std::string::npos;
    }

    return params;
}

// 地理坐标转Web墨卡托
GeographicLibAdapter::TransformResult
GeographicLibAdapter::geographicToWebMercator(double lon, double lat, double alt)
{
    // 检查输入范围
    if (std::abs(lat) > WEB_MERCATOR_MAX_LAT)
    {
        return TransformResult(false, 0, 0, alt);
    }

    // 标准化经度
    while (lon > 180.0) lon -= 360.0;
    while (lon < -180.0) lon += 360.0;

    // 转换为弧度
    double lonRad = lon * M_PI / 180.0;
    double latRad = lat * M_PI / 180.0;

    // Web墨卡托投影公式
    double x = EARTH_RADIUS * lonRad;
    double y = EARTH_RADIUS * std::log(std::tan(M_PI / 4.0 + latRad / 2.0));

    return TransformResult(true, x, y, alt);
}

// Web墨卡托转地理坐标
GeographicLibAdapter::TransformResult
GeographicLibAdapter::webMercatorToGeographic(double x, double y, double z)
{
    // 检查输入范围
    if (std::abs(x) > WEB_MERCATOR_MAX_X || std::abs(y) > WEB_MERCATOR_MAX_Y)
    {
        return TransformResult(false, 0, 0, z);
    }

    // Web墨卡托逆投影公式
    double lon = (x / EARTH_RADIUS) * 180.0 / M_PI;
    double lat = (2.0 * std::atan(std::exp(y / EARTH_RADIUS)) - M_PI / 2.0) * 180.0 / M_PI;

    return TransformResult(true, lon, lat, z);
}

// 地理坐标转UTM
GeographicLibAdapter::TransformResult
GeographicLibAdapter::geographicToUTM(double lon, double lat, double alt, int zone)
{
    try
    {
        if (zone == 0)
        {
            zone = calculateUTMZone(lon);
        }

        bool northp = lat >= 0;
        double x, y;
        int zoneOut;
        
        UTMUPS::Forward(lat, lon, zoneOut, northp, x, y);
        
        return TransformResult(true, x, y, alt);
    }
    catch (const std::exception& e)
    {
        return TransformResult(false, 0, 0, alt);
    }
}

// UTM转地理坐标
GeographicLibAdapter::TransformResult
GeographicLibAdapter::utmToGeographic(double x, double y, double z, int zone, bool isNorthern)
{
    try
    {
        double lat, lon;
        UTMUPS::Reverse(zone, isNorthern, x, y, lat, lon);
        
        return TransformResult(true, lon, lat, z);
    }
    catch (const std::exception& e)
    {
        return TransformResult(false, 0, 0, z);
    }
}

// 计算UTM区域
int GeographicLibAdapter::calculateUTMZone(double longitude)
{
    // 标准化经度到[-180, 180]
    while (longitude > 180.0) longitude -= 360.0;
    while (longitude < -180.0) longitude += 360.0;
    
    return static_cast<int>((longitude + 180.0) / 6.0) + 1;
}

// 全局辅助函数实现
namespace osgEarth { namespace GeographicLibUtils {

bool isValidGeographic(double lon, double lat)
{
    return (lon >= -180.0 && lon <= 180.0 && lat >= -90.0 && lat <= 90.0);
}

bool isValidWebMercator(double x, double y)
{
    return (std::abs(x) <= WEB_MERCATOR_MAX_X && std::abs(y) <= WEB_MERCATOR_MAX_Y);
}

double normalizeLongitude(double lon)
{
    while (lon > 180.0) lon -= 360.0;
    while (lon < -180.0) lon += 360.0;
    return lon;
}

double normalizeLatitude(double lat)
{
    return std::max(-90.0, std::min(90.0, lat));
}

}} // namespace osgEarth::GeographicLibUtils
