/*-------------------------------------------------------------------------
 *
 * pg_collation_d.h
 *    Macro definitions for pg_collation
 *
 * Portions Copyright (c) 1996-2023, PostgreSQL Global Development Group
 * Portions Copyright (c) 1994, Regents of the University of California
 *
 * NOTES
 *  ******************************
 *  *** DO NOT EDIT THIS FILE! ***
 *  ******************************
 *
 *  It has been GENERATED by src/backend/catalog/genbki.pl
 *
 *-------------------------------------------------------------------------
 */
#ifndef PG_COLLATION_D_H
#define PG_COLLATION_D_H

#define CollationRelationId 3456
#define CollationNameEncNspIndexId 3164
#define CollationOidIndexId 3085

#define Anum_pg_collation_oid 1
#define Anum_pg_collation_collname 2
#define Anum_pg_collation_collnamespace 3
#define Anum_pg_collation_collowner 4
#define Anum_pg_collation_collprovider 5
#define Anum_pg_collation_collisdeterministic 6
#define Anum_pg_collation_collencoding 7
#define Anum_pg_collation_collcollate 8
#define Anum_pg_collation_collctype 9
#define Anum_pg_collation_colliculocale 10
#define Anum_pg_collation_collicurules 11
#define Anum_pg_collation_collversion 12

#define Natts_pg_collation 12


#define COLLPROVIDER_DEFAULT	'd'
#define COLLPROVIDER_ICU		'i'
#define COLLPROVIDER_LIBC		'c'

static inline const char *
collprovider_name(char c)
{
	switch (c)
	{
		case COLLPROVIDER_ICU:
			return "icu";
		case COLLPROVIDER_LIBC:
			return "libc";
		default:
			return "???";
	}
}

#define DEFAULT_COLLATION_OID 100
#define C_COLLATION_OID 950
#define POSIX_COLLATION_OID 951

#endif							/* PG_COLLATION_D_H */
