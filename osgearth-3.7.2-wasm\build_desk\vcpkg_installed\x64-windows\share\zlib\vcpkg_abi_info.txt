0001-Prevent-invalid-inclusions-when-HAVE_-is-set-to-0.patch 750b9542cb55e6328cca01d3ca997f1373b9530afa95e04213168676936e7bfa
0002-build-static-or-shared-not-both.patch dd4945e8af55d9feb65af706cb0254a6925dc4ac12789586c0d28acf323ad075
0003-android-and-mingw-fixes.patch 63e62c9bae3952b8cca832e01aa96ef3b2696b21976c5031d6d6d79937d8d54c
cmake 3.30.1
features core
portfile.cmake b14cee469c2da06e036e63293c0b6e6509924462e9b341b317986df46b281695
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
powershell 7.5.1
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-9322494aa025c04635f453cbc392b0ca170d297c
usage be22662327df993eebc437495add75acb365ab18d37c7e5de735d4ea4f5d3083
vcpkg-cmake fa0a421976433b6b0635f7ccfd6e08f02a20aef877e749d6792bc1d9ae8233af
vcpkg-cmake-wrapper.cmake 5d49ef2ee6448479c2aad0e5f732e2676eaba0411860f9bebabe6002d66f57d1
vcpkg.json 8ad7072167e1bf56d2ed531e43ecc95026d5b305ed0393140d5ec78f5c6b6c00
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
vcpkg_replace_string b450deb79207478b37119743e00808ebc42de0628e7b98c14ab24728bd5c78b8
