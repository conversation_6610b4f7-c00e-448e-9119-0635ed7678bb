/*-------------------------------------------------------------------------
 *
 * pg_default_acl_d.h
 *    Macro definitions for pg_default_acl
 *
 * Portions Copyright (c) 1996-2023, PostgreSQL Global Development Group
 * Portions Copyright (c) 1994, Regents of the University of California
 *
 * NOTES
 *  ******************************
 *  *** DO NOT EDIT THIS FILE! ***
 *  ******************************
 *
 *  It has been GENERATED by src/backend/catalog/genbki.pl
 *
 *-------------------------------------------------------------------------
 */
#ifndef PG_DEFAULT_ACL_D_H
#define PG_DEFAULT_ACL_D_H

#define DefaultAclRelationId 826
#define DefaultAclRoleNspObjIndexId 827
#define DefaultAclOidIndexId 828

#define Anum_pg_default_acl_oid 1
#define Anum_pg_default_acl_defaclrole 2
#define Anum_pg_default_acl_defaclnamespace 3
#define Anum_pg_default_acl_defaclobjtype 4
#define Anum_pg_default_acl_defaclacl 5

#define Natts_pg_default_acl 5


/*
 * Types of objects for which the user is allowed to specify default
 * permissions through pg_default_acl.  These codes are used in the
 * defaclobjtype column.
 */
#define DEFACLOBJ_RELATION		'r' /* table, view */
#define DEFACLOBJ_SEQUENCE		'S' /* sequence */
#define DEFACLOBJ_FUNCTION		'f' /* function */
#define DEFACLOBJ_TYPE			'T' /* type */
#define DEFACLOBJ_NAMESPACE		'n' /* namespace */


#endif							/* PG_DEFAULT_ACL_D_H */
