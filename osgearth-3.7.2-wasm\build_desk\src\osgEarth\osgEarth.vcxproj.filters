﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AltitudeFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AltitudeSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AnnotationData.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AnnotationLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AnnotationNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AnnotationRegistry.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AnnotationSettings.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AnnotationUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ArcGISServer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ArcGISTilePackage.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AtlasBuilder.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AttributesFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AutoClipPlaneHandler.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AzureMaps.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BboxDrawable.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BBoxSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BillboardResource.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BillboardSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Bing.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Bounds.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BufferFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BuildGeometryFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BuildTextFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Cache.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CacheBin.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CachePolicy.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CacheSeed.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Callouts.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CameraUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Capabilities.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CascadeDrapingDecorator.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CentroidFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CesiumIon.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Chonk.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CircleNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ClampableNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ClampCallback.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Clamping.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ClampingTechnique.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ClipSpace.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ClusterNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Color.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ColorFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Composite.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CompositeFeatureSource.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CompositeTiledModelLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Compressors.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CompressedArray.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Config.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ContourMap.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ConvertTypeFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CoverageLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CoverageSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CropFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CssUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Cube.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CullingUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DateTime.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DateTimeRange.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DebugImageLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DecalLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DepthOffset.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Draggers.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DrapeableNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DrapingCullSet.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DrapingTechnique.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DrawInstanced.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\EarthManipulator.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ECEF.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Elevation.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ElevationLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ElevationLOD.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ElevationPool.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ElevationQuery.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ElevationRanges.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\EllipseNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Ellipsoid.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Ephemeris.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ExampleResources.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Expression.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Extension.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ExtrudeGeometryFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ExtrusionSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FadeEffect.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Feature.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureCursor.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureDisplayLayout.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureElevationLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureImageLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureImageRTTLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureModelGraph.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureModelLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureModelSource.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureRasterizer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureSDFLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureSource.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureSourceIndexNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureStyleSorter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FileUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Fill.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Filter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FilterContext.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FilteredFeatureSource.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FlatteningLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FractalElevationLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FrameClock.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GARSGraticule.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoData.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeodeticGraticule.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeodeticLabelingEngine.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Geoid.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoMath.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Geometry.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeometryClamper.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeometryCloud.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeometryCompiler.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeometryFactory.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeometryRasterizer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeometryUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoPositionNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoPositionNodeAutoScaler.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GEOS.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeographicLibAdapter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeosFeatureSource.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoJSONReader.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShapefileReader.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoTransform.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GLSLChunker.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GLUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GraticuleLabelingEngine.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\HeightFieldUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Horizon.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\HorizonClipPlane.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\HTM.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\HTTPClient.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\IconResource.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\IconSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageMosaic.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageOverlay.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageOverlayEditor.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageToFeatureLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageToHeightFieldConverter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\InstanceBuilder.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\InstanceCloud.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\InstanceResource.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\InstanceSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\IntersectionPicker.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\IOTypes.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\JoinLines.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\JsonUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LabelNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LandCover.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LandCoverLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LatLongFormatter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Layer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LayerShader.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Lighting.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LinearLineOfSight.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LineDrawable.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LineSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LocalGeometryNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LocalTangentPlane.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LODGenerator.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LogarithmicDepthBuffer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Map.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MapboxGLGlyphManager.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MapboxGLImageLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MapCallback.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MapNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MaterialLoader.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Math.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MBTiles.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MeasureTool.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MemCache.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MemoryUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MeshConsolidator.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MeshFlattener.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MeshSubdivider.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MetadataNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MetaTile.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Metrics.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MGRSFormatter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MGRSGraticule.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ModelLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ModelNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ModelResource.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ModelSource.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ModelSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MVT.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\NetworkMonitor.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\NodeUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\NoiseTextureFactory.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Notify.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ObjectIDPicker.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ObjectIndex.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\OgrUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\OverlayDecorator.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PagedNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PatchLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PBRMaterial.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PhongLightingEffect.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PlaceNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PointDrawable.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PointSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PolygonizeLines.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PolygonSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PowerlineLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PrimitiveIntersector.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Profile.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Progress.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Query.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\RadialLineOfSight.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Random.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\RectangleNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Registry.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\RenderSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ResampleFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Resource.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ResourceCache.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ResourceLibrary.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Revisioning.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\RTTPicker.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScaleFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScatterFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SceneGraphCallback.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScreenSpaceLayout.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScriptEngine.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScriptFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SDF.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SelectExtentTool.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Session.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShaderFactory.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShaderGenerator.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShaderLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShaderLoader.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShaderMerger.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShaderUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Shadowing.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SimpleOceanLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SimplePager.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SimplexNoise.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SimplifyFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Skins.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Sky.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SkyView.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SpatialReference.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StateSetCache.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Status.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Stroke.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Style.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StyleSelector.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StyleSheet.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SubstituteModelFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Symbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TDTiles.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Terrain.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainConstraintLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainEngineNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainMeshLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainOptions.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainProfile.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainResources.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainTileModel.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainTileModelFactory.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TessellateOperator.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Tessellator.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Text.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TextSymbol.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TextSymbolizer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TextureArena.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TextureBuffer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TextureBufferSerializer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TFSPackager.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Threading.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ThreeDTilesLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileCache.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TiledFeatureModelLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TiledModelLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileEstimator.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileHandler.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileKey.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileMesher.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileRasterizer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileSource.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileSourceElevationLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileSourceImageLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileVisitor.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TimeControl.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TimeSeriesImage.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TMS.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TMSBackFiller.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TopologyGraph.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TrackNode.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TransformFilter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Units.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\URI.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Utils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\UTMGraticule.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\UTMLabelingEngine.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Version.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\VerticalDatum.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\VideoLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ViewFitter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Viewpoint.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\VirtualProgram.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\VisibleLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WindLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WireLines.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WMS.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\XmlUtils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\XYZ.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\XYZModelLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\tinyxml\tinyxml.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\tinyxml\tinyxmlerror.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\tinyxml\tinystr.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\tinyxml\tinyxmlparser.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\AutoGenShaders.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\vector_tile.pb.cc">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\glyphs.pb.cc">
      <Filter>Sources</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AGG.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeographicLibAdapter.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoJSONReader.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShapefileReader.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\rtree.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\weemesh.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\weejobs.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\tinyxml\tinyxml.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\tinyxml\tinystr.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\vector_tile.pb.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\glyphs.pb.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BuildConfig.in">
      <Filter>Templates</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Version.in">
      <Filter>Templates</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Shaders.cpp.in">
      <Filter>Templates</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\4a484bbde562c39e043aa51bdc145c9a\vector_tile.pb.h.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\4a484bbde562c39e043aa51bdc145c9a\glyphs.pb.h.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\CMakeFiles\4a484bbde562c39e043aa51bdc145c9a\AutoGenShaders.cpp.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AltitudeFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AltitudeSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AnnotationData">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AnnotationLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AnnotationNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AnnotationRegistry">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AnnotationSettings">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AnnotationUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ArcGISServer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ArcGISTilePackage">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AtlasBuilder">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AttributesFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AutoClipPlaneHandler">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AutoScaleCallback">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\AzureMaps">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BboxDrawable">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BBoxSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BillboardResource">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BillboardSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Bing">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Bounds">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BufferFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BuildGeometryFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\BuildTextFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Cache">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CacheBin">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CachePolicy">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CacheSeed">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Callbacks">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Callouts">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CameraUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Capabilities">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CascadeDrapingDecorator">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CentroidFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CesiumIon">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Chonk">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CircleNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ClampableNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ClampCallback">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Clamping">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ClampingTechnique">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ClipSpace">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ClusterNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Color">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ColorFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Common">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Composite">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CompositeFeatureSource">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CompressedArray">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CompositeTiledModelLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Config">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Containers">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ContourMap">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ConvertTypeFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Coverage">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CoverageLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CoverageSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CropFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CssUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Cube">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CullingUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DateTime">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DateTimeRange">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DebugImageLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DecalLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DepthOffset">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Draggers">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DrapeableNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DrapingCullSet">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DrapingTechnique">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DrawInstanced">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\EarthManipulator">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ECEF">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Elevation">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ElevationLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ElevationLOD">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ElevationPool">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ElevationQuery">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ElevationRanges">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\EllipseNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Ellipsoid">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Endian">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Ephemeris">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ExampleResources">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Export">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Expression">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Extension">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ExtrudeGeometryFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ExtrusionSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FadeEffect">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Feature">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureCursor">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureDisplayLayout">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureElevationLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureImageLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureImageRTTLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureIndex">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureModelGraph">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureModelLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureModelSource">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureRasterizer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureSDFLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureSource">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureSourceIndexNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FeatureStyleSorter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FileUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Fill">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Filter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FilterContext">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FilteredFeatureSource">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FlatteningLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Formatter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FractalElevationLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\FrameClock">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GARSGraticule">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoCommon">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoData">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeodeticGraticule">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeodeticLabelingEngine">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Geoid">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoMath">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Geometry">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeometryClamper">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeometryCloud">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeometryCompiler">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeometryFactory">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeometryRasterizer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeometryUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoPositionNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoPositionNodeAutoScaler">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GEOS">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeosFeatureSource">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoTransform">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GLSLChunker">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GLUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GraticuleLabelingEngine">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\HeightFieldUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Horizon">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\HorizonClipPlane">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\HTM">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\HTTPClient">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\IconResource">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\IconSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageMosaic">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageOverlay">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageOverlayEditor">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageToFeatureLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageToHeightFieldConverter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ImageUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\InstanceBuilder">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\InstanceCloud">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\InstanceResource">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\InstanceSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\IntersectionPicker">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\IOTypes">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\JoinLines">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\JsonUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LabelNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LandCover">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LandCoverLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LatLongFormatter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Layer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LayerReference">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LayerShader">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Lighting">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LinearLineOfSight">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LineDrawable">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LineFunctor">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LineOfSight">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LineSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LoadableNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LocalGeometryNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LocalTangentPlane">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Locators">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LODGenerator">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LogarithmicDepthBuffer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Map">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MapboxGLGlyphManager">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MapboxGLImageLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MapCallback">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MapModelChange">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MapNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MapNodeObserver">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MaterialLoader">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Math">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MBTiles">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MeasureTool">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MemCache">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MemoryUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MeshConsolidator">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MeshFlattener">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MeshSubdivider">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MetadataNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MetaTile">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Metrics">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MGRSFormatter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MGRSGraticule">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ModelLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ModelNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ModelResource">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ModelSource">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ModelSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MVT">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\NativeProgramAdapter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\NetworkMonitor">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\NodeUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\NoiseTextureFactory">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Notify">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ObjectIDPicker">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ObjectIndex">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\OGRFeatureSource">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\OgrUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\optional">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\OverlayDecorator">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PagedNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PatchLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PBRMaterial">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PhongLightingEffect">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Picker">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PlaceNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PluginLoader">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PointDrawable">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PointSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PolygonizeLines">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PolygonSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PowerlineLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PrimitiveIntersector">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Profile">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Progress">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Query">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\RadialLineOfSight">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Random">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\RectangleNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\RefinePolicy">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Registry">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\RenderSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ResampleFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Resource">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ResourceCache">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ResourceLibrary">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Revisioning">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\RTTPicker">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScaleFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScatterFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SceneGraphCallback">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScreenSpaceLayout">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScreenSpaceLayoutCallout">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScreenSpaceLayoutDeclutter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScreenSpaceLayoutImpl">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Script">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScriptEngine">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ScriptFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SDF">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SelectExtentTool">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Session">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShaderFactory">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShaderGenerator">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShaderLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShaderLoader">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShaderMerger">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Shaders">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShaderUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Shadowing">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SimpleOceanLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SimplePager">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SimplexNoise">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SimplifyFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Skins">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Sky">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SkyView">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SpatialReference">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StarData">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StateSetCache">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StateTransition">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Status">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Stroke">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Style">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StyleSelector">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StyleSheet">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SubstituteModelFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Symbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Tags">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TDTiles">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Terrain">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainConstraintLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainEffect">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainEngineNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainEngineRequirements">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainMeshLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainOptions">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainProfile">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainResources">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainTileModel">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainTileModelFactory">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TerrainTileNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TessellateOperator">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Tessellator">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Text">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TextSymbol">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TextSymbolizer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TextureArena">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TextureBuffer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TFS">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TFSPackager">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Threading">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ThreeDTilesLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileCache">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TiledFeatureModelLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TiledModelLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileEstimator">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileHandler">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileIndex">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileIndexBuilder">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileKey">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileMesher">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileRasterizer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileSource">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileSourceElevationLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileSourceImageLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TileVisitor">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TimeControl">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TimeSeriesImage">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TMS">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TMSBackFiller">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TopologyGraph">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TrackNode">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\TransformFilter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Units">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\URI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Utils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\UTMGraticule">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\UTMLabelingEngine">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\VerticalDatum">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\VideoLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ViewFitter">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Viewpoint">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\VirtualProgram">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\VisibleLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WFS">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WindLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WireLines">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WMS">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\XmlUtils">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\XYZ">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\XYZFeatureSource">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\XYZModelLayer">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include\osgEarth\BuildConfig">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\build_include\osgEarth\Version">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\CascadeDraping.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Chonk.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Chonk.Culling.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DepthOffset.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Draping.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\DrawInstancedAttribute.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GPUClamping.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GPUClamping.lib.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\HexTiling.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Instancing.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LineDrawable.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\MetadataNode.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WireLines.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PhongLighting.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PointDrawable.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Text.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Text_legacy.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ContourMap.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeodeticGraticule.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LogDepthBuffer.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\LogDepthBuffer.VertOnly.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\ShadowCaster.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\SimpleOceanLayer.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\RTTPicker.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\WindLayer.CS.glsl">
      <Filter>Shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\PBR.glsl">
      <Filter>Shaders</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{5AB7FE58-5006-39D0-8A5D-1F373C4D284C}</UniqueIdentifier>
    </Filter>
    <Filter Include="Headers">
      <UniqueIdentifier>{774AAB6F-870D-33E2-BFF4-0DA7C058CDAA}</UniqueIdentifier>
    </Filter>
    <Filter Include="Shaders">
      <UniqueIdentifier>{4BEF637A-E64F-36A9-B611-12FA255F01E8}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sources">
      <UniqueIdentifier>{391FC889-D7E0-3048-AA64-902B5FF26B08}</UniqueIdentifier>
    </Filter>
    <Filter Include="Templates">
      <UniqueIdentifier>{ADA8900A-0430-346C-BB87-12DB7A1B002C}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
