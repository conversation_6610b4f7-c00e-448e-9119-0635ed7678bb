/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#include "ShapefileReader.h"
#include <osgEarth/Notify>
#include <osgEarth/StringUtils>
#include <osgEarth/FileUtils>
#include <algorithm>
#include <cstring>

using namespace osgEarth;

#define LC "[ShapefileReader] "

ShapefileReader::ShapefileReader() :
    _isOpen(false),
    _dbfOpen(false),
    _recordCount(0)
{
    memset(&_header, 0, sizeof(_header));
    memset(&_dbfHeader, 0, sizeof(_dbfHeader));
}

ShapefileReader::~ShapefileReader()
{
    close();
}

bool ShapefileReader::open(const std::string& filename)
{
    close();
    
    _filename = filename;
    
    // Open the .shp file
    _shpFile.open(filename, std::ios::binary);
    if (!_shpFile.is_open())
    {
        setError("Cannot open shapefile: " + filename);
        return false;
    }
    
    // Read the header
    if (!readHeader())
    {
        close();
        return false;
    }
    
    // Try to open associated DBF file
    openDBF(filename);
    
    _isOpen = true;
    return true;
}

void ShapefileReader::close()
{
    if (_shpFile.is_open())
        _shpFile.close();
    
    closeDBF();
    
    _isOpen = false;
    _filename.clear();
    _lastError.clear();
}

bool ShapefileReader::readHeader()
{
    if (!_shpFile.is_open())
        return false;
    
    _shpFile.seekg(0, std::ios::beg);
    
    // Read file code (big endian)
    _header.fileCode = readInt32();
    if (isLittleEndian())
        _header.fileCode = swapEndian(_header.fileCode);
    
    if (_header.fileCode != SHP_FILE_CODE)
    {
        setError("Invalid shapefile format");
        return false;
    }
    
    // Skip unused bytes (20 bytes)
    _shpFile.seekg(24, std::ios::beg);
    
    // Read file length (big endian, in 16-bit words)
    _header.fileLength = readInt32();
    if (isLittleEndian())
        _header.fileLength = swapEndian(_header.fileLength);
    
    // Read version (little endian)
    _header.version = readInt32();
    
    // Read shape type (little endian)
    _header.shapeType = readInt32();
    
    // Read bounding box (little endian)
    _header.xMin = readDouble();
    _header.yMin = readDouble();
    _header.xMax = readDouble();
    _header.yMax = readDouble();
    _header.zMin = readDouble();
    _header.zMax = readDouble();
    _header.mMin = readDouble();
    _header.mMax = readDouble();
    
    return true;
}

bool ShapefileReader::readFeatures(std::vector<osg::ref_ptr<Feature>>& features)
{
    if (!_isOpen)
        return false;
    
    features.clear();
    
    // Position after header
    _shpFile.seekg(HEADER_SIZE, std::ios::beg);
    
    int recordIndex = 0;
    while (!_shpFile.eof())
    {
        osg::ref_ptr<Feature> feature;
        if (readRecord(feature))
        {
            if (feature.valid())
            {
                // Read DBF attributes if available
                if (_dbfOpen)
                {
                    AttributeTable attributes;
                    if (readDBFRecord(recordIndex, attributes))
                    {
                        feature->setAttrs(attributes);
                    }
                }
                
                features.push_back(feature);
            }
            recordIndex++;
        }
        else
        {
            break; // End of file or error
        }
    }
    
    return true;
}

bool ShapefileReader::readRecord(osg::ref_ptr<Feature>& feature)
{
    if (!_shpFile.is_open())
        return false;
    
    // Check if we're at end of file
    std::streampos currentPos = _shpFile.tellg();
    _shpFile.seekg(0, std::ios::end);
    std::streampos endPos = _shpFile.tellg();
    _shpFile.seekg(currentPos);
    
    if (currentPos >= endPos)
        return false;
    
    // Read record header
    RecordHeader recordHeader;
    recordHeader.recordNumber = readInt32();
    recordHeader.contentLength = readInt32();
    
    if (isLittleEndian())
    {
        recordHeader.recordNumber = swapEndian(recordHeader.recordNumber);
        recordHeader.contentLength = swapEndian(recordHeader.contentLength);
    }
    
    // Read shape type
    int shapeType = readInt32();
    
    Geometry* geometry = nullptr;
    
    switch (shapeType)
    {
    case SHAPE_POINT:
    case SHAPE_POINTZ:
    case SHAPE_POINTM:
        geometry = readPointGeometry();
        break;
        
    case SHAPE_POLYLINE:
    case SHAPE_POLYLINEZ:
    case SHAPE_POLYLINEM:
        geometry = readPolylineGeometry();
        break;
        
    case SHAPE_POLYGON:
    case SHAPE_POLYGONZ:
    case SHAPE_POLYGONM:
        geometry = readPolygonGeometry();
        break;
        
    case SHAPE_NULL:
        // Null shape - create feature with no geometry
        break;
        
    default:
        OE_WARN << LC << "Unsupported shape type: " << shapeType << std::endl;
        // Skip this record
        _shpFile.seekg(recordHeader.contentLength * 2 - 4, std::ios::cur);
        return true;
    }
    
    // Create feature
    feature = new Feature(geometry, SpatialReference::get("wgs84"));
    feature->setFID(recordHeader.recordNumber);
    
    return true;
}

Geometry* ShapefileReader::readPointGeometry()
{
    double x = readDouble();
    double y = readDouble();
    
    if (!isValidCoordinate(x, y))
        return nullptr;
    
    return createPoint(x, y);
}

Geometry* ShapefileReader::readPolylineGeometry()
{
    // Read bounding box
    double xMin = readDouble();
    double yMin = readDouble();
    double xMax = readDouble();
    double yMax = readDouble();
    
    // Read number of parts and points
    int numParts = readInt32();
    int numPoints = readInt32();
    
    if (numParts <= 0 || numPoints <= 0)
        return nullptr;
    
    // Read part indices
    std::vector<int> partIndices(numParts);
    for (int i = 0; i < numParts; ++i)
    {
        partIndices[i] = readInt32();
    }
    
    // Read points
    std::vector<osg::Vec3d> points(numPoints);
    for (int i = 0; i < numPoints; ++i)
    {
        double x = readDouble();
        double y = readDouble();
        points[i] = osg::Vec3d(x, y, 0.0);
    }
    
    if (numParts == 1)
    {
        // Single line string
        return createLineString(points);
    }
    else
    {
        // Multi line string
        MultiGeometry* multiGeom = createMultiGeometry();
        
        for (int i = 0; i < numParts; ++i)
        {
            int startIdx = partIndices[i];
            int endIdx = (i + 1 < numParts) ? partIndices[i + 1] : numPoints;
            
            std::vector<osg::Vec3d> partPoints(points.begin() + startIdx, 
                                             points.begin() + endIdx);
            
            LineString* lineString = createLineString(partPoints);
            if (lineString)
                multiGeom->getComponents().push_back(lineString);
        }
        
        return multiGeom;
    }
}

Geometry* ShapefileReader::readPolygonGeometry()
{
    // Read bounding box
    double xMin = readDouble();
    double yMin = readDouble();
    double xMax = readDouble();
    double yMax = readDouble();
    
    // Read number of parts and points
    int numParts = readInt32();
    int numPoints = readInt32();
    
    if (numParts <= 0 || numPoints <= 0)
        return nullptr;
    
    // Read part indices
    std::vector<int> partIndices(numParts);
    for (int i = 0; i < numParts; ++i)
    {
        partIndices[i] = readInt32();
    }
    
    // Read points
    std::vector<osg::Vec3d> points(numPoints);
    for (int i = 0; i < numPoints; ++i)
    {
        double x = readDouble();
        double y = readDouble();
        points[i] = osg::Vec3d(x, y, 0.0);
    }
    
    // Create polygon rings
    std::vector<std::vector<osg::Vec3d>> rings;
    
    for (int i = 0; i < numParts; ++i)
    {
        int startIdx = partIndices[i];
        int endIdx = (i + 1 < numParts) ? partIndices[i + 1] : numPoints;
        
        std::vector<osg::Vec3d> ring(points.begin() + startIdx, 
                                   points.begin() + endIdx);
        rings.push_back(ring);
    }
    
    return createPolygon(rings);
}

// Utility functions implementation
int ShapefileReader::readInt32()
{
    int value;
    _shpFile.read(reinterpret_cast<char*>(&value), sizeof(value));
    return value;
}

double ShapefileReader::readDouble()
{
    double value;
    _shpFile.read(reinterpret_cast<char*>(&value), sizeof(value));
    return value;
}

void ShapefileReader::readBytes(char* buffer, int count)
{
    _shpFile.read(buffer, count);
}

bool ShapefileReader::seekTo(std::streampos pos)
{
    _shpFile.seekg(pos);
    return _shpFile.good();
}

int ShapefileReader::swapEndian(int value)
{
    return ((value & 0xFF000000) >> 24) |
           ((value & 0x00FF0000) >> 8)  |
           ((value & 0x0000FF00) << 8)  |
           ((value & 0x000000FF) << 24);
}

double ShapefileReader::swapEndian(double value)
{
    union { double d; uint64_t i; } u;
    u.d = value;
    u.i = ((u.i & 0xFF00000000000000ULL) >> 56) |
          ((u.i & 0x00FF000000000000ULL) >> 40) |
          ((u.i & 0x0000FF0000000000ULL) >> 24) |
          ((u.i & 0x000000FF00000000ULL) >> 8)  |
          ((u.i & 0x00000000FF000000ULL) << 8)  |
          ((u.i & 0x0000000000FF0000ULL) << 24) |
          ((u.i & 0x000000000000FF00ULL) << 40) |
          ((u.i & 0x00000000000000FFULL) << 56);
    return u.d;
}

bool ShapefileReader::isLittleEndian()
{
    int test = 1;
    return *reinterpret_cast<char*>(&test) == 1;
}

void ShapefileReader::setError(const std::string& error)
{
    _lastError = error;
    OE_WARN << LC << error << std::endl;
}

Point* ShapefileReader::createPoint(double x, double y, double z)
{
    return new Point(osg::Vec3d(x, y, z));
}

LineString* ShapefileReader::createLineString(const std::vector<osg::Vec3d>& points)
{
    LineString* lineString = new LineString();
    for (const auto& point : points)
    {
        lineString->push_back(point);
    }
    return lineString;
}

Polygon* ShapefileReader::createPolygon(const std::vector<std::vector<osg::Vec3d>>& rings)
{
    if (rings.empty())
        return nullptr;
    
    Polygon* polygon = new Polygon();
    
    // First ring is exterior
    for (const auto& point : rings[0])
    {
        polygon->push_back(point);
    }
    
    // Additional rings are holes
    for (size_t i = 1; i < rings.size(); ++i)
    {
        Ring* hole = new Ring();
        for (const auto& point : rings[i])
        {
            hole->push_back(point);
        }
        polygon->getHoles().push_back(hole);
    }
    
    return polygon;
}

MultiGeometry* ShapefileReader::createMultiGeometry()
{
    return new MultiGeometry();
}

bool ShapefileReader::isValidCoordinate(double x, double y)
{
    return !std::isnan(x) && !std::isnan(y) && 
           std::isfinite(x) && std::isfinite(y);
}

void ShapefileReader::getBounds(double& xMin, double& yMin, double& xMax, double& yMax) const
{
    xMin = _header.xMin;
    yMin = _header.yMin;
    xMax = _header.xMax;
    yMax = _header.yMax;
}

bool ShapefileReader::openDBF(const std::string& shpFilename)
{
    std::string dbfFilename = shpFilename;
    size_t dotPos = dbfFilename.find_last_of('.');
    if (dotPos != std::string::npos)
    {
        dbfFilename = dbfFilename.substr(0, dotPos) + ".dbf";
    }
    else
    {
        dbfFilename += ".dbf";
    }
    
    _dbfFile.open(dbfFilename, std::ios::binary);
    if (_dbfFile.is_open())
    {
        _dbfOpen = readDBFHeader();
    }
    
    return _dbfOpen;
}

void ShapefileReader::closeDBF()
{
    if (_dbfFile.is_open())
        _dbfFile.close();
    _dbfOpen = false;
}

bool ShapefileReader::readDBFHeader()
{
    // Simplified DBF header reading - implement as needed
    return true;
}

bool ShapefileReader::readDBFRecord(int recordIndex, AttributeTable& attributes)
{
    // Simplified DBF record reading - implement as needed
    return true;
}
