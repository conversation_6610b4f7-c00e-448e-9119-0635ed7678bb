/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#ifndef OSGEARTH_XYZ_MODEL_LAYER
#define OSGEARTH_XYZ_MODEL_LAYER 1

#include <osgEarth/Common>
#include <osgEarth/Layer>
#include <osgEarth/LayerReference>
#include <osgEarth/TiledModelLayer>
#include <osgEarth/StateSetCache>
#include <osgEarth/TextureArena>


namespace osgEarth {
    class Map;
}

namespace osgEarth
{
    /**
     * Layer that loads a pregenerated quadtree of model files
     */
    class OSGEARTH_EXPORT XYZModelLayer : public TiledModelLayer
    {
    public: // serialization
        struct OSGEARTH_EXPORT Options : public TiledModelLayer::Options
        {
            META_LayerOptions(osgEarth, Options, TiledModelLayer::Options);
            OE_OPTION(URI, url);
            OE_OPTION(bool, invertY);
            OE_OPTION(int, minLevel);
            OE_OPTION(int, maxLevel);

            OE_OPTION(ProfileOptions, profile);
            Config getConfig() const override;
            void fromConfig(const Config& conf);
        };

    public:
        META_Layer(osgEarth, XYZModelLayer, Options, TiledModelLayer, XYZModel);

        //! Tiling profile (required)
        void setProfile(const Profile* profile);
        const Profile* getProfile() const override { return _profile.get(); }

        //! Whether to flip the Y axis for tile indexing
        //! TODO: do we need this? Since the URL supports ${-y}?
        void setInvertY(const bool& value);
        const bool& getInvertY() const;

        //! Base URL for requests
        void setURL(const URI& value);
        const URI& getURL() const;

        //! Whether new LODs should be added to the scene (true) or replace existing ones (false)
        void setAdditive(const bool& value);
        const bool& getAdditive() const;

        //! Minimum level of detail to access
        void setMinLevel(unsigned value);
        unsigned getMinLevel() const override;

        //! Maximum level of detail to access
        void setMaxLevel(unsigned value);
        unsigned getMaxLevel() const override;

    public: // Layer

        //! opens the layer and returns the status
        Status openImplementation() override;

        void addedToMap(const Map* map) override;

        //! Serialization
        Config getConfig() const override;

        // post-ctor initialization
        void init() override;

    protected: // TiledModelLayer

        //! Creates an OSG node from a tile key.
        osg::ref_ptr<osg::Node> createTileImplementation(const TileKey&, ProgressCallback*) const override;

    protected:

        virtual ~XYZModelLayer();

    private:
        osg::ref_ptr<const Profile> _profile;
        osg::ref_ptr< osgDB::Options > _readOptions;
#if 0
        osg::ref_ptr< osgEarth::StateSetCache > _statesetCache;
        osg::ref_ptr< TextureArena > _textures;
        mutable std::vector<Texture::WeakPtr> _texturesCache;
        mutable std::mutex _texturesCacheMutex;
#endif
    };

} // namespace osgEarth

#endif // OSGEARTH_XYZ_MODEL_LAYER
