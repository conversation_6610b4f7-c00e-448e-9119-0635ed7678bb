// ***DO NOT EDIT THIS FILE - IT IS AUTOMATICALLY GENERATED BY CMAKE***

#include <osgEarth/Shaders>

namespace osgEarth { namespace Util
{
    Shaders::Shaders()
    {
        // Cascade Draping
        CascadeDraping = "CascadeDraping.glsl";
        _sources[CascadeDraping] = 
R"(#pragma vp_name     Draping Vertex Shader
#pragma vp_function oe_Draping_VS, vertex_view
out vec4 oe_Draping_vertexView;
void oe_Draping_VS(inout vec4 vertexView)
{
    oe_Draping_vertexView = vertexView;
}
[break]
#pragma vp_name       Draping Fragment Shader
#pragma vp_entryPoint oe_Draping_FS
#pragma vp_location   fragment_coloring
#pragma vp_order      0.6
#pragma import_defines(OE_IS_PICK_CAMERA)
#pragma import_defines(OE_DISABLE_DRAPING)
#pragma import_defines(OE_DRAPING_MAX_CASCADES)
uniform sampler2DArray oe_Draping_tex;
uniform mat4 oe_Draping_texMatrix[OE_DRAPING_MAX_CASCADES];
in vec4 oe_Draping_vertexView;
void oe_Draping_FS(inout vec4 color)
{
#ifdef OE_DISABLE_DRAPING
    return;
#endif
    vec4 texel = vec4(0,0,0,0);
    // the [3][3] slot will be zero if we have exceeded the current number of cascades
    for (int i=0; i<OE_DRAPING_MAX_CASCADES && oe_Draping_texMatrix[i][3][3] != 0; ++i)
    {
        vec4 coord = oe_Draping_texMatrix[i] * oe_Draping_vertexView;
        coord.xy /= coord.w;
        if (clamp(coord.xy, 0, 1) == coord.xy)
        {
            texel = texture(oe_Draping_tex, vec3(coord.xy, i));
            break;
        }
    }
#ifdef OE_IS_PICK_CAMERA
    color = texel;
#else
    color = vec4( mix( color.rgb, texel.rgb, texel.a ), color.a);
#endif
}
)";

        // Chonk Rendering
        Chonk = "Chonk.glsl";
        _sources[Chonk] = 
R"(#pragma vp_function oe_chonk_default_vertex_model, vertex_model, 0.0
#pragma import_defines(OE_IS_SHADOW_CAMERA)
#pragma import_defines(OE_IS_DEPTH_CAMERA)
#pragma import_defines(OE_CHONK_MAX_LOD_FOR_NORMAL_MAPS)
#pragma import_defines(OE_CHONK_MAX_LOD_FOR_PBR_MAPS)
#ifndef OE_CHONK_MAX_LOD_FOR_NORMAL_MAPS
#define OE_CHONK_MAX_LOD_FOR_NORMAL_MAPS 99
#endif
#ifndef OE_CHONK_MAX_LOD_FOR_PBR_MAPS
#define OE_CHONK_MAX_LOD_FOR_PBR_MAPS 99
#endif
struct Instance
{
    mat4 xform;
    vec2 local_uv;
    uint lod;
    float visibility[2]; // per LOD
    float radius;
    float alpha_cutoff;
    uint first_lod_cmd_index;
};
layout(binding = 0, std430) buffer Instances {
    Instance instances[];
};
layout(binding = 1, std430) buffer TextureArena {
    uint64_t textures[];
};
layout(location = 0) in vec3 position;
layout(location = 1) in vec3 normal;
layout(location = 2) in uint normal_technique;
layout(location = 3) in vec4 color;
layout(location = 4) in vec2 uv;
layout(location = 5) in vec3 flex;
layout(location = 6) in int albedo_index;
layout(location = 7) in int normalmap_index;
layout(location = 8) in int pbr_index;
layout(location = 9) in ivec2 extended_materials;
#define NT_DEFAULT 0
#define NT_ZAXIS 1
#define NT_HEMISPHERE 2 
// stage global
mat3 xform3;
uint chonk_lod;
// outputs
out vec3 vp_Normal;
out vec4 vp_Color;
out float oe_fade;
out vec2 oe_tex_uv;
out vec3 oe_position_vec;
out vec3 oe_position_view;
flat out uint oe_normal_technique;
flat out float oe_alpha_cutoff;
flat out uint64_t oe_albedo_tex;
flat out uint64_t oe_normal_tex;
flat out uint64_t oe_pbr_tex;
flat out ivec2 oe_extended_materials;
void oe_chonk_default_vertex_model(inout vec4 vertex)
{
    int i = gl_BaseInstance + gl_InstanceID;
    chonk_lod = instances[i].lod;
    vertex = instances[i].xform * vec4(position, 1.0);
    vp_Color = color;
    xform3 = mat3(instances[i].xform);
    vp_Normal = xform3 * normal;
    oe_normal_technique = normal_technique;
    oe_tex_uv = uv;
    oe_alpha_cutoff = instances[i].alpha_cutoff;
    oe_fade = instances[i].visibility[chonk_lod];
    oe_albedo_tex = albedo_index >= 0 ? textures[albedo_index] : 0;
    oe_extended_materials = extended_materials;
#if defined(OE_IS_SHADOW_CAMERA) || defined(OE_IS_DEPTH_CAMERA)
    oe_fade = 1.0;
    return;
#endif
    // stuff we need only for a non-depth or non-shadow camera
    if (oe_normal_technique == NT_HEMISPHERE)
    {
        // Position vector scaled by the (scaled) radius of the instance
        oe_position_vec = gl_NormalMatrix *
            ((xform3 * position.xyz) / instances[i].radius);
    }
    // FS needs this to make a TBN
    oe_position_view = (gl_ModelViewMatrix * vertex).xyz;
    // disable/ignore normal maps as directed:
    oe_normal_tex = 0;
    if (normalmap_index >= 0 && chonk_lod <= OE_CHONK_MAX_LOD_FOR_NORMAL_MAPS)
    {
        oe_normal_tex = textures[normalmap_index];
    }
    oe_pbr_tex = 0;
    if (pbr_index >= 0 && chonk_lod <= OE_CHONK_MAX_LOD_FOR_PBR_MAPS)
    {
        oe_pbr_tex = textures[pbr_index];
    }
}
[break]
#pragma vp_function oe_chonk_default_fragment, fragment
#pragma import_defines(OE_IS_SHADOW_CAMERA)
#pragma import_defines(OE_IS_DEPTH_CAMERA)
#pragma import_defines(OE_USE_ALPHA_TO_COVERAGE)
#pragma import_defines(OE_GL_RG_COMPRESSED_NORMALS)
#pragma import_defines(OE_GPUCULL_DEBUG)
#pragma import_defines(OE_CHONK_SINGLE_SIDED)
struct OE_PBR { float displacement, roughness, ao, metal; } oe_pbr;
// inputs
in vec3 vp_Normal;
in vec3 oe_position_vec;
in vec3 oe_position_view;
in vec2 oe_tex_uv;
in vec3 oe_UpVectorView;
in float oe_fade;
flat in uint64_t oe_albedo_tex;
flat in uint64_t oe_normal_tex;
flat in uint64_t oe_pbr_tex;
flat in float oe_alpha_cutoff;
flat in uint oe_normal_technique;
#define NT_DEFAULT 0
#define NT_ZAXIS 1
#define NT_HEMISPHERE 2 
const float oe_normal_attenuation = 0.65;
// make a TBN from normal, vertex position, and texture uv
// https://gamedev.stackexchange.com/a/86543
mat3 make_tbn(vec3 N, vec3 p, vec2 uv)
{
    // get edge vectors of the pixel triangle
    vec3 dp1 = dFdx(p);
    vec3 dp2 = dFdy(p);
    vec2 duv1 = dFdx(uv);
    vec2 duv2 = dFdy(uv);
    // solve the linear system
    vec3 dp2perp = cross(dp2, N);
    vec3 dp1perp = cross(N, dp1);
    vec3 T = dp2perp * duv1.x + dp1perp * duv2.x;
    vec3 B = dp2perp * duv1.y + dp1perp * duv2.y;
    // construct a scale-invariant frame 
    float invmax = inversesqrt(max(dot(T, T), dot(B, B)));
    return mat3(T * invmax, B * invmax, N);
}
void oe_chonk_default_fragment(inout vec4 color)
{
    const float alpha_discard_threshold = 0.5;
    // When simulating normals, we invert the texture coordinates
    // for backfacing geometry
    if (!gl_FrontFacing && oe_normal_technique != NT_DEFAULT)
    {
        oe_tex_uv.s = 1.0 - oe_tex_uv.s;
    }
    // Apply the base color:
    if (oe_albedo_tex > 0)
    {
        color *= texture(sampler2D(oe_albedo_tex), oe_tex_uv);
    }
    else
    {
        //color = vec4(1, 0, 0, 1); // testing
        // no texture ... use the vertex color
    }
#if defined(OE_IS_SHADOW_CAMERA) || defined(OE_IS_DEPTH_CAMERA)
    // for shadowing cameras, just do a simple step discard.
    color.a = step(alpha_discard_threshold, color.a * oe_fade);
    if (color.a < 1.0)
        discard;
#else // !OE_IS_SHADOW_CAMERA && !OE_IS_DEPTH_CAMERA
#if OE_GPUCULL_DEBUG
    // apply the high fade from the instancer
    if (oe_fade <= 1.0) color.a *= oe_fade; // color.rgb = vec3(oe_fade, oe_fade, oe_fade); // color.a *= oe_fade;
    else if (oe_fade <= 2.0) color.rgb = vec3(1, 0, 0); // REASON_FRUSTUM
    else if (oe_fade <= 3.0) color.rgb = vec3(1, 1, 0); // REASON_SSE
    else if (oe_fade <= 4.0) color.rgb = vec3(0, 1, 0); // REASON_NEARCLIP
    else color.rgb = vec3(1, 0, 1); // should never happen :)
#else // normal rendering path:
    // Adjust the alpha based on the calculated mipmap level.
    // Looks better and actually helps performance a bit as well.
    // https://bgolus.medium.com/anti-aliased-alpha-test-the-esoteric-alpha-to-coverage-8b177335ae4f
    // https://tinyurl.com/fhu4zdxz
    if (oe_albedo_tex > 0UL)
    {
        vec2 miplevel = textureQueryLod(sampler2D(oe_albedo_tex), oe_tex_uv);
        color.a *= (1.0 + miplevel.x * oe_alpha_cutoff);
    }
    color.a *= oe_fade;
  #ifndef OE_USE_ALPHA_TO_COVERAGE
    // When A2C is not available, we force the alpha to 0 or 1 and then
    // discard the invisible fragments.    
    // This is necessary because the GPU culler generates the draw commands in an
    // arbitrary order and not depth sorted. Even if we did depth-sort the results,
    // there are plenty of overlapping geometries in vegetation that would cause
    // flickering artifacts.
    // (TODO: consider a cheap alpha-only pass that we can sample to prevent overdraw
    // and discard in the expensive shader)
    color.a = step(alpha_discard_threshold, color.a);
    if (color.a < 1.0)
        discard;
  #endif // !OE_USE_ALPHA_TO_COVERAGE
#endif // !OE_GPUCULL_DEBUG
    vec3 normal_view = vp_Normal;
    if (oe_normal_technique == NT_HEMISPHERE)
    {
        // for billboarded normals, adjust the normal so its coverage
        // is a hemisphere facing the viewer. Should we recalculate the TBN here?
        // Probably, but let's not if it already looks good enough.
        vec3 v3d = oe_position_vec; // do not normalize!
        vec3 v2d = vec3(v3d.x, v3d.y, 0.0);
        float size2d = length(v2d) * 1.2021; // adjust for radius, bbox diff
        size2d = mix(0.0, oe_normal_attenuation, clamp(size2d, 0.0, 1.0));
        normal_view = mix(vec3(0, 0, 1), normalize(v2d), size2d);
    }
    vec3 pixel_normal = vec3(0, 0, 1);
    if (oe_normal_tex > 0)
    {
        vec4 n = texture(sampler2D(oe_normal_tex), oe_tex_uv);
        if (n.a == 0) // swizzled in GLUtils::storage2D to indicate a compressed normal
        {
            n.xy = n.xy*2.0 - 1.0;
            n.z = 1.0 - abs(n.x) - abs(n.y);
            float t = clamp(-n.z, 0, 1);
            n.x += (n.x > 0) ? -t : t;
            n.y += (n.y > 0) ? -t : t;
            pixel_normal = n.xyz;
        }
        else
        {
            pixel_normal = normalize(n.xyz*2.0 - 1.0);
        }
    }
    //pixel_normal = vec3(0, 0, 1); // testing
    mat3 TBN = make_tbn(
        normalize(normal_view),
        oe_position_view,
        oe_tex_uv);
    vp_Normal = TBN * pixel_normal;
    if (oe_normal_technique == NT_ZAXIS)
    {
        // attenuate the normal to a z-up orientation
        vec3 world_up = gl_NormalMatrix * vec3(0,0,1);
        vec3 face_up = TBN[1].xyz;
        vp_Normal = normalize(mix(world_up, face_up, 0.25));
    }
    if (oe_pbr_tex > 0)
    {
        // apply PBR maps:
        vec4 texel = texture(sampler2D(oe_pbr_tex), oe_tex_uv);
        oe_pbr.displacement = texel[0];
        oe_pbr.roughness *= texel[1];
        oe_pbr.ao *= texel[2];
        oe_pbr.metal = clamp(oe_pbr.metal + texel[3], 0, 1);
    }
#endif // !OE_IS_SHADOW_CAMERA
}
)";

        // Chonk GPU Culling
        ChonkCulling = "Chonk.Culling.glsl";
        _sources[ChonkCulling] = 
R"(#version 460
#extension GL_NV_gpu_shader5 : enable
#pragma import_defines(OE_GPUCULL_DEBUG)
#pragma import_defines(OE_IS_SHADOW_CAMERA)
#pragma import_defines(OE_LOD_SCALE_UNIFORM)
layout(local_size_x = 32, local_size_y = 1, local_size_z = 1) in;
struct DrawElementsIndirectCommand
{
    uint count;
    uint instanceCount;
    uint firstIndex;
    uint baseVertex;
    uint baseInstance;
};
struct BindlessPtrNV
{
    uint index;
    uint reserved;
    uint64_t address;
    uint64_t length;
};
struct DrawElementsIndirectBindlessCommandNV
{
    DrawElementsIndirectCommand cmd;
    uint reserved;
    BindlessPtrNV indexBuffer;
    BindlessPtrNV vertexBuffer;
};
struct ChonkLOD
{
    vec4 bs;
    float far_pixel_scale;
    float near_pixel_scale;
    // chonk-globals:
    float alpha_cutoff;
    float birthday;
    float fade_near;
    float fade_far;
    uint num_lods;
    // globals:
    uint total_num_commands;
};
struct Instance
{
    mat4 xform;
    vec2 local_uv;
    uint lod;
    float visibility[2]; // per LOD
    float radius;
    float alpha_cutoff;
    int first_lod_cmd_index; // -1 means unused
};
layout(binding = 0) buffer OutputBuffer
{
    Instance output_instances[];
};
layout(binding = 29) buffer Commands
{
    DrawElementsIndirectBindlessCommandNV commands[];
};
layout(binding = 30) buffer ChonkLODs
{
    ChonkLOD chonks[];
};
layout(binding = 31) buffer InputBuffer
{
    Instance input_instances[];
};
uniform vec3 oe_Camera;
uniform float oe_sse;
uniform vec4 oe_lod_scale;
uniform float osg_FrameTime;
uniform float oe_chonk_lod_transition_factor = 0.0;
// Support a user-defined LOD scale uniform. When not present,
// default to osgEarth's LOD scale value in oe_Camera.z.
#ifdef OE_LOD_SCALE_UNIFORM
uniform float OE_LOD_SCALE_UNIFORM;
#else
#define OE_LOD_SCALE_UNIFORM oe_Camera.z
#endif
#if OE_GPUCULL_DEBUG
//#ifdef OE_GPUCULL_DEBUG
#define REJECT(X) if (fade==1.0) { fade=(X);}
#else
#define REJECT(X) return
#endif
#define REASON_FRUSTUM 1.5
#define REASON_SSE 2.5
#define REASON_NEARCLIP 3.5
void cull()
{
    const uint i = gl_GlobalInvocationID.x; // instance
    const uint lod = gl_GlobalInvocationID.y; // lod
    // skip instances that exist only to pad the instance array to the workgroup size:
    if (input_instances[i].first_lod_cmd_index < 0)
        return;
    // initialize by clearing the visibility for this LOD:
    input_instances[i].visibility[lod] = 0.0;
    // bail if our chonk does not have this LOD
    uint v = input_instances[i].first_lod_cmd_index + lod;
    if (lod >= chonks[v].num_lods)
        return;
    // intialize:
    float fade = 1.0;
    // transform the bounding sphere to a view-space bbox.
    mat4 xform = input_instances[i].xform;
    vec4 center = xform * vec4(chonks[v].bs.xyz, 1);
    vec4 center_view = gl_ModelViewMatrix * center;
    float max_scale = max(xform[0][0], max(xform[1][1], xform[2][2]));
    float r = chonks[v].bs.w * max_scale;
    // Trivially reject low-LOD instances that intersect the near clip plane:
    if ((lod > 0) && (gl_ProjectionMatrix[3][3] < 0.01)) // is perspective camera
    {
        float near = gl_ProjectionMatrix[2][3] / (gl_ProjectionMatrix[2][2] - 1.0);
        if (-(center_view.z + r) <= near)
        {
            REJECT(REASON_NEARCLIP);
        }
    }
    // find the clip-space MBR and intersect with the clip frustum:
    vec4 LL, UR, temp;
    temp = gl_ProjectionMatrix * (center_view + vec4(-r, -r, -r, 0)); temp /= temp.w;
    LL = temp; UR = temp;
    temp = gl_ProjectionMatrix * (center_view + vec4(-r, -r, +r, 0)); temp /= temp.w;
    LL = min(LL, temp); UR = max(UR, temp);
    temp = gl_ProjectionMatrix * (center_view + vec4(-r, +r, -r, 0)); temp /= temp.w;
    LL = min(LL, temp); UR = max(UR, temp);
    temp = gl_ProjectionMatrix * (center_view + vec4(-r, +r, +r, 0)); temp /= temp.w;
    LL = min(LL, temp); UR = max(UR, temp);
    temp = gl_ProjectionMatrix * (center_view + vec4(+r, -r, -r, 0)); temp /= temp.w;
    LL = min(LL, temp); UR = max(UR, temp);
    temp = gl_ProjectionMatrix * (center_view + vec4(+r, -r, +r, 0)); temp /= temp.w;
    LL = min(LL, temp); UR = max(UR, temp);
    temp = gl_ProjectionMatrix * (center_view + vec4(+r, +r, -r, 0)); temp /= temp.w;
    LL = min(LL, temp); UR = max(UR, temp);
    temp = gl_ProjectionMatrix * (center_view + vec4(+r, +r, +r, 0)); temp /= temp.w;
    LL = min(LL, temp); UR = max(UR, temp);
#if OE_GPUCULL_DEBUG
    float threshold = 0.95; // 0.75;
#else
    float threshold = 1.0;
#endif
    if (LL.x > threshold || LL.y > threshold)
        REJECT(REASON_FRUSTUM);
    if (UR.x < -threshold || UR.y < -threshold)
        REJECT(REASON_FRUSTUM);
#ifndef OE_IS_SHADOW_CAMERA
    // OK, it is in view - now check pixel size on screen for this LOD:
    vec2 dims = 0.5*(UR.xy - LL.xy)*oe_Camera.xy;
    float pixelSize = min(dims.x, dims.y);
    float pixelSizePad = pixelSize * oe_chonk_lod_transition_factor;
    float minPixelSize = oe_sse * chonks[v].far_pixel_scale * oe_lod_scale[lod];
    if (pixelSize < (minPixelSize - pixelSizePad))
        REJECT(REASON_SSE);
    float maxPixelSize = 3e38; // 1e10;
    if (lod > 0)
    {
        float near_scale = chonks[v].near_pixel_scale * oe_lod_scale[lod - 1];
        maxPixelSize = oe_sse * near_scale;
        if (pixelSize > (maxPixelSize + pixelSizePad))
            REJECT(REASON_SSE);
    }
    if (fade == 1.0)  // good to go, set the proper fade:
    {
        pixelSizePad = max(pixelSizePad, 1.0);
        if (pixelSize > maxPixelSize)
            fade = 1.0 - (pixelSize - maxPixelSize) / pixelSizePad;
        else if (pixelSize < minPixelSize)
            fade = 1.0 - (minPixelSize - pixelSize) / pixelSizePad;
    }
    // Birthday-based fading:
    const float fadein_time = 2.0; // seconds
    float birth = clamp((osg_FrameTime - chonks[v].birthday) / fadein_time, 0.0, 1.0);
    fade *= birth;
    // Distance-based fading:
    float fade_range = chonks[v].fade_far - chonks[v].fade_near;
    if (fade_range > 0.0)
    {
        float dist = length(center_view.xyz) * OE_LOD_SCALE_UNIFORM;
        fade *= clamp((chonks[v].fade_far - dist) / fade_range, 0.0, 1.0);
    }
    if (fade < 0.1)
        return;
#endif // !OE_IS_SHADOW_CAMERA
    // Pass! Set the visibility for this LOD:
    input_instances[i].visibility[lod] = fade;
    // Send along the other values:
    input_instances[i].alpha_cutoff = chonks[v].alpha_cutoff;
    // Send along the scaled radius of this instance
    input_instances[i].radius = r;
    // Bump all baseInstances following this one:
    const uint cmd_count = chonks[v].total_num_commands;
    for (uint i = v + 1; i < cmd_count; ++i)
    {
        atomicAdd(commands[i].cmd.baseInstance, 1);
    }
}
// Copies the visible instances to a compacted output buffer.
void compact()
{
    const uint i = gl_GlobalInvocationID.x; // instance
    const uint lod = gl_GlobalInvocationID.y; // lod
    float fade = input_instances[i].visibility[lod];
    if (fade < 0.1)
        return;
    uint v = input_instances[i].first_lod_cmd_index + lod;
    uint offset = commands[v].cmd.baseInstance;
    uint index = atomicAdd(commands[v].cmd.instanceCount, 1);
    // Lazy! Re-using the instance struct for render leaves..
    output_instances[offset + index] = input_instances[i];
    output_instances[offset + index].lod = lod;
}
// Entry point.
uniform int oe_pass;
void main()
{
    if (oe_pass == 0)
        cull();
    else // if (oe_pass == 1)
        compact();
}
)";

        // Depth Offset
        DepthOffset = "DepthOffset.glsl";
        _sources[DepthOffset] = 
R"(#pragma vp_entryPoint oe_DepthOffset_vertex
#pragma vp_location   vertex_view
#pragma vp_order      0.8
uniform vec4 oe_DepthOffset_params;
void oe_DepthOffset_vertex(inout vec4 vertexView)
{
    // calculate range to target:
    float vertex_range = length(vertexView.xyz);
    // extract params for clarity.
    float minBias = oe_DepthOffset_params[0];
    float maxBias = oe_DepthOffset_params[1];
    float minRange = oe_DepthOffset_params[2];
    float maxRange = oe_DepthOffset_params[3];
    // calculate the depth offset bias for this range:
    float bias = minRange;
    if (maxRange > minRange && maxBias > minBias)
    {
        float ratio = (clamp(vertex_range, minRange, maxRange) - minRange) / (maxRange - minRange);
        bias = minBias + ratio * (maxBias - minBias);
    }
	// clamp the bias to 1/2 of the range of the vertex. We don't want to 
    // pull the vertex TOO close to the camera and certainly not behind it.
    bias = min(bias, vertex_range *0.5);
    
    if (maxBias > minBias)
    {
        bias = min(bias, maxBias);
    }
    // pull the vertex towards the camera.
    vec3 pullVec = normalize(vertexView.xyz);
    vec3 simVert3 = vertexView.xyz - pullVec*bias;
    vertexView = vec4(simVert3, 1.0);
}
)";

        // Draping
        Draping = "Draping.glsl";
        _sources[Draping] = 
R"(#pragma vp_entryPoint oe_overlay_vertex
#pragma vp_location   vertex_view
uniform mat4 oe_overlay_texmatrix;
uniform float oe_overlay_rttLimitZ;
out vec4 oe_overlay_texcoord;
void oe_overlay_vertex(inout vec4 vertexVIEW)
{
    oe_overlay_texcoord = oe_overlay_texmatrix * vertexVIEW;
}
[break]
#pragma vp_entryPoint oe_overlay_fragment
#pragma vp_location   fragment_coloring
#pragma vp_order      1.1
#pragma import_defines(OE_IS_PICK_CAMERA)
#pragma import_defines(OE_DISABLE_DRAPING)
uniform sampler2D oe_overlay_tex;
in vec4 oe_overlay_texcoord;
void oe_overlay_fragment(inout vec4 color)
{
#ifdef OE_DISABLE_DRAPING
    return;
#endif
    vec4 texel = textureProj(oe_overlay_tex, oe_overlay_texcoord);
#ifdef OE_IS_PICK_CAMERA
    color = texel;
#else
    color = vec4( mix( color.rgb, texel.rgb, texel.a ), color.a);
#endif
}
)";

        // DrawInstanced with attributes
        DrawInstancedAttribute = "DrawInstancedAttribute.glsl";
        _sources[DrawInstancedAttribute] = 
R"(#extension GL_EXT_gpu_shader4 : enable
#extension GL_ARB_draw_instanced: enable
#pragma vp_entryPoint oe_draw_instanced_attribute_VS_MODEL
#pragma vp_location   vertex_model
#pragma vp_order      0.0
in vec3 oe_DrawInstancedAttribute_position;
in vec4 oe_DrawInstancedAttribute_rotation;
in vec3 oe_DrawInstancedAttribute_scale;
vec3 vp_Normal;
vec3 rotateQuatPt(vec4 q, vec3 v)
{
    vec3 u = q.xyz;
    float s = q.w;
    return 2 * dot(u, v) * u + (s *s - dot(u, u)) * v + 2 * s * cross(u, v);
}
void oe_draw_instanced_attribute_VS_MODEL(inout vec4 currVertex)
{
    vec3 result = oe_DrawInstancedAttribute_scale * currVertex.xyz;
    result = rotateQuatPt(oe_DrawInstancedAttribute_rotation, result);
    result = result + oe_DrawInstancedAttribute_position;
    currVertex.xyz = result;
    // Transform normal by transpose of inverse transformation
    vp_Normal = vp_Normal / oe_DrawInstancedAttribute_scale;
    vp_Normal = rotateQuatPt(oe_DrawInstancedAttribute_rotation, vp_Normal);
    vp_Normal = normalize(vp_Normal);
}
)";

        // GPU Clamping
        GPUClamping = "GPUClamping.glsl";
        _sources[GPUClamping] = 
R"(#pragma vp_entryPoint oe_clamp_vertex
#pragma vp_location   vertex_view
#pragma vp_order      0.5
#pragma import_defines(OE_CLAMP_HAS_ATTRIBUTES)
#pragma import_defines(OE_IS_GEOCENTRIC)
#pragma include GPUClamping.lib.glsl
#ifdef OE_CLAMP_HAS_ATTRIBUTES
in vec4 oe_clamp_attrs;     // vertex attribute
in float oe_clamp_height;   // vertex attribute
#endif
out float oe_clamp_alpha;
uniform float oe_clamp_altitudeOffset;
uniform float oe_clamp_horizonDistance2;
void oe_clamp_clampViewSpaceVertex(inout vec4 vertexView)
{
#ifdef OE_CLAMP_HAS_ATTRIBUTES
    bool relativeToAnchor = (oe_clamp_attrs.a == 1.0); // 1.0 = ClampToAnchor
    float verticalOffset = oe_clamp_attrs.z;
    float clampHeight = oe_clamp_height;
    // if we are using the anchor point, xform it into view space to prepare
    // for clamping. Force Z=0 for anchoring.
    vec4 pointToClamp = relativeToAnchor ?
        gl_ModelViewMatrix * vec4(oe_clamp_attrs.xy, 0.0, 1.0) :
        vertexView;
#else
    bool relativeToAnchor = false;
    float verticalOffset = 0.0;
    vec4 pointToClamp = vertexView;
    float clampHeight = 0.0;
#endif
    // clamp the point and remember it's depth:
    vec4 clampedPoint;
    float depth;
    oe_getClampedViewVertex(pointToClamp, clampedPoint, depth);
    float dh = verticalOffset + oe_clamp_altitudeOffset;
    if (relativeToAnchor)
    {
        // if we are clamping relative to the anchor point, adjust the HAT based on the
        // distance from the anchor point to the terrain. Since distance() is unsigned,
        // we use the vector dot product to calculate whether to adjust up or down.
        float dist = distance(pointToClamp, clampedPoint);
        float dir = sign(dot(clampedPoint - pointToClamp, vertexView - pointToClamp));
        dh += (dist * dir);
    }
    else
    {
        // if we are clamping to the terrain, the vertex becomes the
        // clamped point
        vertexView.xyz = clampedPoint.xyz;
        dh += clampHeight;
    }
    // calculate the up vector along which clamping will occur (in either direction)
    vec3 up;
    oe_getClampingUpVector(up);
    vertexView.xyz += up*dh;
    // if the clamped depth value is near the far plane, suppress drawing
    // to avoid rendering anomalies.
    oe_clamp_alpha = 1.0 - step(0.9999, depth);
}
void oe_clamp_vertex(inout vec4 vertexView)
{
    // check distance; alpha out if its beyone the horizon distance.
#ifdef OE_IS_GEOCENTRIC
    oe_clamp_alpha = clamp(oe_clamp_horizonDistance2 - (vertexView.z*vertexView.z), 0.0, 1.0);
#else
    oe_clamp_alpha = 1.0;
#endif
    // if visible, calculate clamping.
    // note: no branch divergence in the vertex shader
    if ( oe_clamp_alpha > 0.0 )
    {
        oe_clamp_clampViewSpaceVertex(vertexView);
    }
}
[break]
#pragma vp_entryPoint oe_clamp_fragment
#pragma vp_location   fragment_coloring
in float oe_clamp_alpha;
void oe_clamp_fragment(inout vec4 color)
{
    // adjust the alpha component to "hide" geometry beyond the visible horizon.
    color.a *= oe_clamp_alpha;
}
)";

        GPUClampingLib = "GPUClamping.lib.glsl";
        _sources[GPUClampingLib] = 
R"(// note: this is an include file
// depth texture captures by the clamping technique
uniform sampler2D oe_clamp_depthTex;
// matrix transforming from view space to depth-texture clip space
uniform mat4 oe_clamp_cameraView2depthClip;
// matrix transform from depth-tecture clip space to view space
uniform mat4 oe_clamp_depthClip2cameraView;
// Given a vertex in view space, clamp it to the "ground" as represented
// by an orthographic depth texture. Return the clamped vertex in view space,
// along with the associated depth value.
void oe_getClampedViewVertex(in vec4 vertView, out vec4 out_clampedVertView, out float out_depth)
{
    // transform the vertex into the depth texture's clip coordinates.
    vec4 vertDepthClip = oe_clamp_cameraView2depthClip * vertView;
    // sample the depth map
    out_depth = textureProj( oe_clamp_depthTex, vertDepthClip ).r;
    // now transform into depth-view space so we can apply the height-above-ground:
    vec4 clampedVertDepthClip = vec4(vertDepthClip.x, vertDepthClip.y, out_depth, 1.0);
    // convert back into view space.
    out_clampedVertView = oe_clamp_depthClip2cameraView * clampedVertDepthClip;
}
// Returns a vector indicating the "down" direction.
void oe_getClampingUpVector(out vec3 up)
{
    up = normalize(mat3(oe_clamp_depthClip2cameraView) * vec3(0,0,-1));
}
)";

        HexTilingLib = "HexTiling.glsl";
        _sources[HexTilingLib] = 
R"(#pragma vp_name osgEarth Hex Tiling Library
#pragma import_defines(OE_ENABLE_HEX_TILER_ANISOTROPIC_FILTERING)
// Adapted and ported to GLSL from:
// https://github.com/mmikk/hextile-demo
float ht_g_fallOffContrast = 0.6;
float ht_g_exp = 7;
#ifdef VP_STAGE_FRAGMENT
#ifndef mul
#define mul(X, Y) ((X)*(Y))
#endif
#ifndef M_PI
#define M_PI 3.1415927
#endif
#define HEX_SCALE 3.46410161
// Output:\ weights associated with each hex tile and integer centers
void ht_TriangleGrid(
    out float w1, out float w2, out float w3,
    out ivec2 vertex1, out ivec2 vertex2, out ivec2 vertex3,
    in vec2 st)
{
    // Scaling of the input
    st *= HEX_SCALE; // 2 * 1.sqrt(3);
    // Skew input space into simplex triangle grid
    mat2 gridToSkewedGrid = mat2(1.0, -0.57735027, 0.0, 1.15470054);
    vec2 skewedCoord = mul(gridToSkewedGrid, st);
    ivec2 baseId = ivec2(floor(skewedCoord));
    vec3 temp = vec3(fract(skewedCoord), 0);
    temp.z = 1.0 - temp.x - temp.y;
    float s = step(0.0, -temp.z);
    float s2 = 2 * s - 1;
    w1 = -temp.z * s2;
    w2 = s - temp.y * s2;
    w3 = s - temp.x * s2;
    vertex1 = baseId + ivec2(s, s);
    vertex2 = baseId + ivec2(s, 1 - s);
    vertex3 = baseId + ivec2(1 - s, s);
}
// Output:\ weights associated with each hex tile and integer centers
void ht_TriangleGrid_f(
    out float w1, out float w2, out float w3,
    out vec2 vertex1, out vec2 vertex2, out vec2 vertex3,
    in vec2 st)
{
    // Scaling of the input
    st *= HEX_SCALE; // 2 * 1.sqrt(3);
    // Skew input space into simplex triangle grid
    const mat2 gridToSkewedGrid = mat2(1.0, -0.57735027, 0.0, 1.15470054);
    vec2 skewedCoord = mul(gridToSkewedGrid, st);
    vec2 baseId = floor(skewedCoord);
    vec3 temp = vec3(fract(skewedCoord), 0);
    temp.z = 1.0 - temp.x - temp.y;
    float s = step(0.0, -temp.z);
    float s2 = 2 * s - 1;
    w1 = -temp.z * s2;
    w2 = s - temp.y * s2;
    w3 = s - temp.x * s2;
    vertex1 = baseId + vec2(s, s);
    vertex2 = baseId + vec2(s, 1 - s);
    vertex3 = baseId + vec2(1 - s, s);
}
vec2 ht_hash(vec2 p)
{
    vec2 r = mat2(127.1, 311.7, 269.5, 183.3) * p;
    return fract(sin(r) * 43758.5453);
}
vec2 ht_MakeCenST(ivec2 Vertex)
{
    const mat2 invSkewMat = mat2(1.0, 0.5, 0.0, 1.0 / 1.15470054);
    return mul(invSkewMat, Vertex) / HEX_SCALE;
}
mat2 ht_LoadRot2x2(ivec2 idx, float rotStrength)
{
    float angle = abs(idx.x * idx.y) + abs(idx.x + idx.y) + M_PI;
    // remap to +/-pi
    angle = mod(angle, 2 * M_PI);
    if (angle < 0) angle += 2 * M_PI;
    if (angle > M_PI) angle -= 2 * M_PI;
    angle *= rotStrength;
    float cs = cos(angle), si = sin(angle);
    return mat2(cs, -si, si, cs);
}
vec3 ht_Gain3(vec3 x, float r)
{
    // increase contrast when r>0.5 and
    // reduce contrast if less
    float k = log(1 - r) / log(0.5);
    vec3 s = 2 * step(0.5, x);
    vec3 m = 2 * (1 - s);
    vec3 res = 0.5 * s + 0.25 * m * pow(max(vec3(0.0), s + x * m), vec3(k));
    return res.xyz / (res.x + res.y + res.z);
}
vec3 ht_ProduceHexWeights(vec3 W, ivec2 vertex1, ivec2 vertex2, ivec2 vertex3)
{
    vec3 res;
    int v1 = (vertex1.x - vertex1.y) % 3;
    if (v1 < 0) v1 += 3;
    int vh = v1 < 2 ? (v1 + 1) : 0;
    int vl = v1 > 0 ? (v1 - 1) : 2;
    int v2 = vertex1.x < vertex3.x ? vl : vh;
    int v3 = vertex1.x < vertex3.x ? vh : vl;
    res.x = v3 == 0 ? W.z : (v2 == 0 ? W.y : W.x);
    res.y = v3 == 1 ? W.z : (v2 == 1 ? W.y : W.x);
    res.z = v3 == 2 ? W.z : (v2 == 2 ? W.y : W.x);
    return res;
}
// Input: vM is tangent space normal in [-1;1].
// Output: convert vM to a derivative.
vec2 ht_TspaceNormalToDerivative(in vec3 vM)
{
    float scale = 1.0 / 128.0;
    // Ensure vM delivers a positive third component using abs() and
    // constrain vM.z so the range of the derivative is [-128; 128].
    vec3 vMa = abs(vM);
    float z_ma = max(vMa.z, scale * max(vMa.x, vMa.y));
    // Set to match positive vertical texture coordinate axis.
    bool gFlipVertDeriv = true;
    float s = gFlipVertDeriv ? -1.0 : 1.0;
    return -vec2(vM.x, s * vM.y) / z_ma;
}
vec2 ht_sampleDeriv(sampler2D nmap, vec2 st, vec2 dSTdx, vec2 dSTdy)
{
    // sample
    vec3 vM = 2.0 * textureGrad(nmap, st, dSTdx, dSTdy).xyz - 1.0;
    return ht_TspaceNormalToDerivative(vM);
}
// Input:\ nmap is a normal map
// Input:\ r increase contrast when r>0.5
// Output:\ deriv is a derivative dHduv wrt units in pixels
// Output:\ weights shows the weight of each hex tile
void bumphex2derivNMap(
    out vec2 deriv, out vec3 weights,
    sampler2D nmap, in vec2 st,
    float rotStrength, float r)
{
    vec2 dSTdx = dFdx(st);
    vec2 dSTdy = dFdy(st);
    // Get triangle info
    float w1, w2, w3;
    ivec2 vertex1, vertex2, vertex3;
    ht_TriangleGrid(w1, w2, w3, vertex1, vertex2, vertex3, st);
    mat2 rot1 = ht_LoadRot2x2(vertex1, rotStrength);
    mat2 rot2 = ht_LoadRot2x2(vertex2, rotStrength);
    mat2 rot3 = ht_LoadRot2x2(vertex3, rotStrength);
    vec2 cen1 = ht_MakeCenST(vertex1);
    vec2 cen2 = ht_MakeCenST(vertex2);
    vec2 cen3 = ht_MakeCenST(vertex3);
    vec2 st1 = mul(st - cen1, rot1) + cen1 + ht_hash(vertex1);
    vec2 st2 = mul(st - cen2, rot2) + cen2 + ht_hash(vertex2);
    vec2 st3 = mul(st - cen3, rot3) + cen3 + ht_hash(vertex3);
    // Fetch input
    vec2 d1 = ht_sampleDeriv(nmap, st1,
        mul(dSTdx, rot1), mul(dSTdy, rot1));
    vec2 d2 = ht_sampleDeriv(nmap, st2,
        mul(dSTdx, rot2), mul(dSTdy, rot2));
    vec2 d3 = ht_sampleDeriv(nmap, st3,
        mul(dSTdx, rot3), mul(dSTdy, rot3));
    d1 = mul(rot1, d1); d2 = mul(rot2, d2); d3 = mul(rot3, d3);
    // produce sine to the angle between the conceptual normal
    // in tangent space and the Z-axis
    vec3 D = vec3(dot(d1, d1), dot(d2, d2), dot(d3, d3));
    vec3 Dw = sqrt(D / (1.0 + D));
    Dw = mix(vec3(1.0), Dw, ht_g_fallOffContrast);	// 0.6
    vec3 W = Dw * pow(vec3(w1, w2, w3), vec3(ht_g_exp));	// 7
    W /= (W.x + W.y + W.z);
    if (r != 0.5) W = ht_Gain3(W, r);
    deriv = W.x * d1 + W.y * d2 + W.z * d3;
    weights = ht_ProduceHexWeights(W.xyz, vertex1, vertex2, vertex3);
}
float ht_get_lod(in ivec2 dim, in vec2 x, in vec2 y)
{
    vec2 ddx = x * float(dim.x), ddy = y * float(dim.y);
    return 0.5 * log2(max(dot(ddx, ddx), dot(ddy, ddy)));
}
// tex = sampler to sample
// st = texture coordinates
// rotStrength = amount of rotation offset
// transStrength = amount of translation offset
vec4 ht_hex2col(in sampler2D tex, in vec2 st, in float rotStrength, in float transStength)
{
    vec2 dSTdx = dFdx(st), dSTdy = dFdy(st);
    // Get triangle info
    float w1, w2, w3;
    ivec2 vertex1, vertex2, vertex3;
    ht_TriangleGrid(w1, w2, w3, vertex1, vertex2, vertex3, st);
    mat2 rot1 = ht_LoadRot2x2(vertex1, rotStrength);
    mat2 rot2 = ht_LoadRot2x2(vertex2, rotStrength);
    mat2 rot3 = ht_LoadRot2x2(vertex3, rotStrength);
    vec2 cen1 = ht_MakeCenST(vertex1);
    vec2 cen2 = ht_MakeCenST(vertex2);
    vec2 cen3 = ht_MakeCenST(vertex3);
    vec2 st1 = mul(st - cen1, rot1) + cen1 + ht_hash(vertex1) * transStength;
    vec2 st2 = mul(st - cen2, rot2) + cen2 + ht_hash(vertex2) * transStength;
    vec2 st3 = mul(st - cen3, rot3) + cen3 + ht_hash(vertex3) * transStength;
    ivec2 dim = textureSize(tex, 0);
    vec4 c1 = textureLod(tex, st1, ht_get_lod(dim, dSTdx * rot1, dSTdy * rot1));
    vec4 c2 = textureLod(tex, st2, ht_get_lod(dim, dSTdx * rot2, dSTdy * rot2));
    vec4 c3 = textureLod(tex, st3, ht_get_lod(dim, dSTdx * rot3, dSTdy * rot3));
    //vec4 c1 = textureGrad(tex, st1, dSTdx*rot1, dSTdy*rot1);
    //vec4 c2 = textureGrad(tex, st2, dSTdx*rot2, dSTdy*rot2);
    //vec4 c3 = textureGrad(tex, st3, dSTdx*rot3, dSTdy*rot3);
    // use luminance as weight
    vec3 Lw = vec3(0.299, 0.587, 0.114);
    vec3 Dw = vec3(dot(c1.xyz, Lw), dot(c2.xyz, Lw), dot(c3.xyz, Lw));
    Dw = mix(vec3(1.0), Dw, ht_g_fallOffContrast);	// 0.6
    vec3 W = Dw * pow(vec3(w1, w2, w3), vec3(ht_g_exp));	// 7
    W /= (W.x + W.y + W.z);
    //if (r != 0.5) W = Gain3(W, r);
    vec4 color = W.x * c1 + W.y * c2 + W.z * c3;
    //weights = ProduceHexWeights(W.xyz, vertex1, vertex2, vertex3);
    return color;
}
uniform float oe_hex_tiler_gradient_bias = 0.0;
// Hextiling function optimized for no rotations and to 
// sample and interpolate both color and material vectors
void ht_hex2colTex_optimized(
    in sampler2D color_tex,
    in sampler2D material_tex,
    in vec2 st,
    out vec4 color,
    out vec4 material,
    inout vec3 weighting)
{
    // Get triangle info
    vec3 weights;
    vec2 vertex1, vertex2, vertex3;
    ht_TriangleGrid_f(weights[0], weights[1], weights[2], vertex1, vertex2, vertex3, st);
    // randomize the sampling offsets:
    vec2 st1 = st + ht_hash(vertex1);
    vec2 st2 = st + ht_hash(vertex2);
    vec2 st3 = st + ht_hash(vertex3);
#if defined(OE_ENABLE_HEX_TILER_ANISOTROPIC_FILTERING) && (OE_ENABLE_HEX_TILER_ANISOTROPIC_FILTERING)
    // apply a mip bias for sharpness:
    // https://bgolus.medium.com/sharper-mipmapping-using-shader-based-supersampling-ed7aadb47bec
    float bias = pow(2.0, oe_hex_tiler_gradient_bias);
    // Use the same partial derivitives to sample all three locations
    // to avoid rendering artifacts.
    vec2 ddx = dFdx(st) * bias, ddy = dFdy(st) * bias;
    vec4 c1 = textureGrad(color_tex, st1, ddx, ddy);
    vec4 c2 = textureGrad(color_tex, st2, ddx, ddy);
    vec4 c3 = textureGrad(color_tex, st3, ddx, ddy);
    vec4 m1 = textureGrad(material_tex, st1, ddx, ddy);
    vec4 m2 = textureGrad(material_tex, st2, ddx, ddy);
    vec4 m3 = textureGrad(material_tex, st3, ddx, ddy);
#else
    // Fast way: replace textureGrad by manually calculating the LOD
    // and using textureLod instead (much faster than textureGrad, but
    // you lose the ability to do anisotropic filtering)
    // https://solidpixel.github.io/2022/03/27/texture_sampling_tips.html
    ivec2 tex_dim;
    vec2 ddx, ddy;
    float lod;
    vec2 st_ddx = dFdx(st), st_ddy = dFdy(st);
    tex_dim = textureSize(color_tex, 0);
    ddx = st_ddx * float(tex_dim.x), ddy = st_ddy * float(tex_dim.y);
    lod = 0.5 * log2(max(dot(ddx, ddx), dot(ddy, ddy)));
    vec4 c1 = textureLod(color_tex, st1, lod);
    vec4 c2 = textureLod(color_tex, st2, lod);
    vec4 c3 = textureLod(color_tex, st3, lod);
    tex_dim = textureSize(material_tex, 0);
    ddx = st_ddx * float(tex_dim.x), ddy = st_ddy * float(tex_dim.y);
    lod = 0.5 * log2(max(dot(ddx, ddx), dot(ddy, ddy)));
    vec4 m1 = textureLod(material_tex, st1, lod);
    vec4 m2 = textureLod(material_tex, st2, lod);
    vec4 m3 = textureLod(material_tex, st3, lod);
#endif
    vec3 W = weighting;
    if (W == vec3(0))
    {
        // Use color's luminance as weighting factor
        vec3 Lw = vec3(0.299, 0.587, 0.114);
        vec3 Dw = vec3(dot(c1.xyz, Lw), dot(c2.xyz, Lw), dot(c3.xyz, Lw));
        Dw = mix(vec3(1.0), Dw, ht_g_fallOffContrast);
        W = Dw * pow(weights, vec3(ht_g_exp));
        W /= (W.x + W.y + W.z);
    }
    weighting = W;
    color = W.x * c1 + W.y * c2 + W.z * c3;
    material = W.x * m1 + W.y * m2 + W.z * m3;
}
#endif // VP_STAGE_FRAGMENT
)";

        // DrawInstanced
        Instancing = "Instancing.glsl";
        _sources[Instancing] = 
R"(#extension GL_EXT_gpu_shader4 : enable
#extension GL_ARB_draw_instanced: enable
#pragma vp_entryPoint oe_di_setInstancePosition
#pragma vp_location   vertex_model
#pragma vp_order      0.0
uniform samplerBuffer oe_di_postex_TBO;
// Stage-global containing object ID
uint oe_index_objectid;
vec3 vp_Normal;
void oe_di_setInstancePosition(inout vec4 VertexMODEL)
{ 
    int index = 4 * gl_InstanceID;
    vec4 m0 = texelFetch(oe_di_postex_TBO, index);
    vec4 m1 = texelFetch(oe_di_postex_TBO, index+1); 
    vec4 m2 = texelFetch(oe_di_postex_TBO, index+2); 
    vec4 m3 = texelFetch(oe_di_postex_TBO, index+3);
    // decode the ObjectID from the last column:
    
    oe_index_objectid = uint(m3[0]) + (uint(m3[1]) << 8u) + (uint(m3[2]) << 16u) + (uint(m3[3]) << 24u);
    
    // rebuild positioning matrix and transform the vert. (Note, the matrix is actually
    // transposed so we have to reverse the multiplication order.)
    mat4 xform = mat4(m0, m1, m2, vec4(0,0,0,1));
    VertexMODEL = VertexMODEL * xform;
    // rotate the normal vector in the same manner.
    vp_Normal = vp_Normal * mat3(xform);
}
)";

        // LineDrawable
        LineDrawable = "LineDrawable.glsl";
        _sources[LineDrawable] = 
R"(#pragma vp_name GPU Lines Screen Projected Model
#pragma vp_entryPoint oe_LineDrawable_VS_VIEW
#pragma vp_location vertex_view
#pragma vp_order last
uniform vec2 oe_LineDrawable_limits;
flat out int oe_LineDrawable_draw;
// Input attributes for adjacent points
in vec3 oe_LineDrawable_prev;
in vec3 oe_LineDrawable_next;
// Shared stage globals
vec4 oe_LineDrawable_prevView;
vec4 oe_LineDrawable_nextView;
void oe_LineDrawable_VS_VIEW(inout vec4 currView)
{
    // Enforce the start/end limits on the line:
    oe_LineDrawable_draw = 1;
    int first = int(oe_LineDrawable_limits[0]);
    int last = int(oe_LineDrawable_limits[1]);
    if (first >= 0)
    {
        if (gl_VertexID < first || (last > 0 && gl_VertexID > last))
        {
            oe_LineDrawable_draw = 0;
        }
    }
    // Compute the change in the view vertex so that we can apply the same
    // delta to the prev and next vectors. (An example would be if the verts
    // were GPU-clamped or otherwise permuted in another shader component.)
    vec4 originalView = gl_ModelViewMatrix * gl_Vertex;
    vec4 deltaView = currView - originalView;
    // calculate prev/next points in post-transform view space:
    oe_LineDrawable_prevView = gl_ModelViewMatrix * vec4(oe_LineDrawable_prev,1) + deltaView;
    oe_LineDrawable_nextView = gl_ModelViewMatrix * vec4(oe_LineDrawable_next,1) + deltaView;
}
[break]
#pragma vp_name GPU Lines Screen Projected Clip
#pragma vp_entryPoint oe_LineDrawable_VS_CLIP
#pragma vp_location vertex_clip
#pragma import_defines(OE_LINE_SMOOTH)
#pragma import_defines(OE_LINE_QUANTIZE)
// Set by the InstallCameraUniform callback
uniform vec3 oe_Camera;
// Set by GLUtils methods
uniform float oe_GL_LineWidth = 1.0;
uniform int oe_GL_LineStipplePattern = 0xffff;
// Input attributes for adjacent points
in vec3 oe_LineDrawable_prev;
in vec3 oe_LineDrawable_next;
flat out int oe_LineDrawable_draw;
flat out vec2 oe_LineDrawable_stippleDir;
// Shared stage globals
vec4 oe_LineDrawable_prevView;
vec4 oe_LineDrawable_nextView;
#ifdef OE_LINE_SMOOTH
out float oe_LineDrawable_lateral;
#else
float oe_LineDrawable_lateral;
#endif
#define OE_LINE_QUANTIZE_ENABLED
#ifdef OE_LINE_QUANTIZE
const float oe_line_quantize = OE_LINE_QUANTIZE;
#else
const float oe_line_quantize = 8.0;
#endif
void oe_LineDrawable_VS_CLIP(inout vec4 currClip)
{
    if (oe_LineDrawable_draw == 0)
        return;
    // Transform the prev and next points in clip space.
    vec4 prevClip = gl_ProjectionMatrix * oe_LineDrawable_prevView;
    vec4 nextClip = gl_ProjectionMatrix * oe_LineDrawable_nextView;
	// use these for calculating stippling direction. We must
    // transform into pixel space to account to aspect ratio.
    vec2 currPixel = (currClip.xy/currClip.w) * oe_Camera.xy;
    vec2 prevPixel = (prevClip.xy/prevClip.w) * oe_Camera.xy;
    vec2 nextPixel = (nextClip.xy/nextClip.w) * oe_Camera.xy;
#ifdef OE_LINE_SMOOTH
    float thickness = floor(oe_GL_LineWidth + 1.0);
#else
    float thickness = max(0.5, floor(oe_GL_LineWidth));
#endif
    float len = thickness;
    int code = (gl_VertexID+2) & 3; // gl_VertexID % 4
    bool isStart = code <= 1;
    bool isRight = code==0 || code==2;
    oe_LineDrawable_lateral = isRight? -1.0 : 1.0;
    vec2 dir;
    vec2 stippleDir;
    // The following vertex comparisons must be done in model 
    // space because the equivalency gets mashed after projection.
    // starting point uses (next - current)
    if (gl_Vertex.xyz == oe_LineDrawable_prev)
    {
        dir = normalize(nextPixel - currPixel);
        stippleDir = dir;
    }
    
    // ending point uses (current - previous)
    else if (gl_Vertex.xyz == oe_LineDrawable_next)
    {
        dir = normalize(currPixel - prevPixel);
        stippleDir = dir;
    }
    else
    {
        vec2 dirIn  = normalize(currPixel - prevPixel);
        vec2 dirOut = normalize(nextPixel - currPixel);
        if (dot(dirIn,dirOut) < -0.999999)
        {
            dir = isStart? dirOut : dirIn;
        }
        else
        {
            vec2 tangent = normalize(dirIn+dirOut);
            vec2 perp = vec2(-dirIn.y, dirIn.x);
            vec2 miter = vec2(-tangent.y, tangent.x);
            dir = tangent;
            len = thickness / dot(miter, perp);
            // limit the length of a mitered corner, to prevent unsightly spikes
            const float limit = 2.0;
            if (len > thickness*limit)
            {
                len = thickness;
                dir = isStart? dirOut : dirIn;
            }
        }
        stippleDir = dirOut;
    }
    // calculate the extrusion vector in pixels
    // note: seems like it should be len/2, BUT remember we are in [-w..w] space
    vec2 extrudePixel = vec2(-dir.y, dir.x) * len;
    // and convert to unit space:
    vec2 extrudeUnit = extrudePixel / oe_Camera.xy;
    // calculate the offset in clip space and apply it.
    vec2 offset = extrudeUnit * oe_LineDrawable_lateral;
    currClip.xy += (offset * currClip.w);
#ifdef OE_LINE_QUANTIZE_ENABLED
    if (oe_line_quantize > 0.0 && oe_GL_LineStipplePattern != 0xffff)
    {
        // Calculate the (quantized) rotation angle that will project the
        // fragment coord onto the X-axis for stipple pattern sampling.
        // Note: this relies on the GLSL "provoking vertex" being at the 
        // beginning of the line segment!
        const float r2d = 57.29577951;
        const float d2r = 1.0 / r2d;
        int a = int(r2d*(atan(stippleDir.y, stippleDir.x)) + 180.0);
        int q = int(360.0 / oe_line_quantize);
        int r = a % q;
        int qa = (r > q / 2) ? a + q - r : a - r;
        float qangle = d2r*(float(qa) - 180.0);
        stippleDir = vec2(cos(qangle), sin(qangle));
    }
#endif
    // pass the stippling direction to frag shader
    oe_LineDrawable_stippleDir = stippleDir;
}
[break]
#pragma vp_name GPU Lines Screen Projected FS
#pragma vp_entryPoint oe_LineDrawable_Stippler_FS
#pragma vp_location fragment_coloring
#pragma import_defines(OE_LINE_SMOOTH)
uniform int oe_GL_LineStippleFactor;
uniform int oe_GL_LineStipplePattern;
flat in vec2 oe_LineDrawable_stippleDir;
flat in int oe_LineDrawable_draw;
#ifdef OE_LINE_SMOOTH
in float oe_LineDrawable_lateral;
#endif
void oe_LineDrawable_Stippler_FS(inout vec4 color)
{
    if (oe_LineDrawable_draw == 0)
        discard;
    if (oe_GL_LineStipplePattern != 0xffff)
    {
        // coordinate of the fragment, shifted to 0:
        vec2 coord = (gl_FragCoord.xy - 0.5);
        // rotate the frag coord onto the X-axis to sample the stipple pattern linearly
        // note: the mat2 inverts the y coordinate (sin(angle)) because we want the 
        // rotation angle to be the negative of the stipple direction angle.
        vec2 rv = normalize(oe_LineDrawable_stippleDir);
        vec2 coordProj = mat2(rv.x, -rv.y, rv.y, rv.x) * coord;
        // sample the stippling pattern (16-bits repeating)
        int cx = int(coordProj.x);
        int ci = (cx % (16 * oe_GL_LineStippleFactor)) / oe_GL_LineStippleFactor;
        int pattern16 = 0xffff & (oe_GL_LineStipplePattern & (1 << ci));
        if (pattern16 == 0)
            discard;
        // debugging
        //color.r = (rv.x + 1.0)*0.5;
        //color.g = (rv.y + 1.0)*0.5;
        //color.b = 0.0;
    }
#ifdef OE_LINE_SMOOTH
    // anti-aliasing
    float L = abs(oe_LineDrawable_lateral);
    color.a = color.a * smoothstep(0.0, 1.0, 1.0-(L*L));
#endif
    
}
)";    

        // PointDrawable
        PointDrawable = "PointDrawable.glsl";
        _sources[PointDrawable] = 
R"(#pragma vp_name PointDrawable
#pragma vp_entryPoint oe_PointDrawable_VS_VIEW
#pragma vp_location vertex_view
#pragma vp_order last
uniform float oe_GL_PointSize = 1.0;
void oe_PointDrawable_VS_VIEW(inout vec4 vertexView)
{
    gl_PointSize = oe_GL_PointSize;
}
[break]
#pragma vp_name PointDrawable FS
#pragma vp_entryPoint oe_PointDrawable_FS
#pragma vp_location fragment_coloring
#pragma import_defines(OE_POINT_SMOOTH)
void oe_PointDrawable_FS(inout vec4 color)
{
#ifdef OE_POINT_SMOOTH
    vec2 c = 2.0*gl_PointCoord-1.0;
    float r = dot(c, c);
    float d = 0.0;
  #ifdef GL_OES_standard_derivatives
    d = fwidth(r);
  #endif
    color.a *= 1.0 - smoothstep(1.0-d, 1.0+d, r);
    if (color.a < 0.1)
        discard;
#endif
}
)";          

        // WireLines
        WireLines = "WireLines.glsl";
        _sources[WireLines] = 
R"(#pragma vp_name Wire Lines Vertex Shader Model
#pragma vp_entryPoint oe_WireLine_VS_MODEL
#pragma vp_location vertex_model
uniform float oe_WireDrawable_radius;
// Set by the InstallCameraUniform callback
uniform vec3 oe_Camera;
out vec3 vp_Normal;
out vec4 vp_Color;
// Scale the wire geometry so it covers at least 1 pixel, and scale
// the alpha (as coverage) to compensate. We assume that the wire is
// horizontal because we don't want the alpha value to change as the
// orientation changes.
//
// Given a point v = [x, y , z, 1] in eye space and projection matrix P,
// P * v = [xc, yc, zc, w]; yc / w = -(P[1][2] * z + p[1][1] * y) / z;
// the derivative diff(yc, y) gives the clip space change in height
// with respect to a change in eye space y. diff(yc, y) = -p[1][1] / z,
// or p[1][1] / w.
//
// Clip space goes from -1 to 1, so the pixel height of an object with
// a height h is screen_height * p11 * h / (2 * w).
void oe_WireLine_VS_MODEL(inout vec4 curVertex)
{
    vec4 clipVertex = gl_ModelViewProjectionMatrix * curVertex;
    // Multiply by 2 to get diameter.
    float pixSize = (2.0 * oe_WireDrawable_radius) * oe_Camera.y * gl_ProjectionMatrix[1][1]
        / (2.0 * clipVertex.w);
    float scale = 1.0;
    if (pixSize < 1.0)
    {
        scale = 1.0 / pixSize;
        vp_Color.a = pixSize;
    }
    curVertex.xyz += vp_Normal * oe_WireDrawable_radius * scale;
}
)";

        // PhongLightingEffect
        PhongLighting = "PhongLighting.glsl";
        _sources[PhongLighting] = 
R"(#pragma vp_name       Phong Lighting Vertex Stage
#pragma vp_entryPoint oe_phong_vertex
#pragma vp_location   vertex_view
#pragma import_defines(OE_LIGHTING)
out vec3 oe_phong_vertexView3;
void oe_phong_vertex(inout vec4 VertexVIEW)
{
#ifndef OE_LIGHTING
    return;
#endif
    oe_phong_vertexView3 = VertexVIEW.xyz / VertexVIEW.w;
}
[break]
#pragma vp_name       Phong Lighting Fragment Stage
#pragma vp_entryPoint oe_phong_fragment
#pragma vp_location   fragment_lighting
#pragma import_defines(OE_LIGHTING)
#pragma import_defines(OE_NUM_LIGHTS)
#ifdef OE_LIGHTING
in vec3 oe_phong_vertexView3; 
// stage global
in vec3 vp_Normal;
// Parameters of each light:
struct osg_LightSourceParameters 
{   
   vec4 ambient;
   vec4 diffuse;
   vec4 specular;
   vec4 position;
   vec3 spotDirection;
   float spotExponent;
   float spotCutoff;
   float spotCosCutoff;
   float constantAttenuation;
   float linearAttenuation;
   float quadraticAttenuation;
   bool enabled;
};  
uniform osg_LightSourceParameters osg_LightSource[OE_NUM_LIGHTS];
// Surface material:
struct osg_MaterialParameters  
{   
   vec4 emission;    // Ecm   
   vec4 ambient;     // Acm   
   vec4 diffuse;     // Dcm   
   vec4 specular;    // Scm   
   float shininess;  // Srm  
};  
uniform osg_MaterialParameters osg_FrontMaterial; 
void oe_phong_fragment(inout vec4 color) 
{
    // See:
    // https://en.wikipedia.org/wiki/Phong_reflection_model
    // https://www.opengl.org/sdk/docs/tutorials/ClockworkCoders/lighting.php
    // https://en.wikibooks.org/wiki/GLSL_Programming/GLUT/Multiple_Lights
    vec3 N = normalize(vp_Normal);
    float shine = clamp(osg_FrontMaterial.shininess, 1.0, 128.0);
    vec3 surfaceSpecularity = osg_FrontMaterial.specular.rgb;
    // Accumulate the lighting, starting with material emission.
    vec3 totalDiffuse = vec3(0.0);
    vec3 totalAmbient = vec3(0.0);
    vec3 totalSpecular = vec3(0.0);
    
    int numLights = OE_NUM_LIGHTS;
    for (int i=0; i<numLights; ++i)
    {
        if (osg_LightSource[i].enabled)
        {
            float attenuation = 1.0;
            // L is the normalized camera-to-light vector.
            vec3 L = normalize(osg_LightSource[i].position.xyz);
            // V is the normalized vertex-to-camera vector.
            vec3 V = -normalize(oe_phong_vertexView3);
            // point or spot light:
            if (osg_LightSource[i].position.w != 0.0)
            {
                // calculate VL, the vertex-to-light vector:
                vec4 VL = vec4(oe_phong_vertexView3, 1.0) * osg_LightSource[i].position.w;
                vec4 VL4 = osg_LightSource[i].position - VL;
                L = normalize(VL4.xyz);
                // calculate attenuation:
                float distance = length(VL4);
                attenuation = 1.0 / (
                    osg_LightSource[i].constantAttenuation +
                    osg_LightSource[i].linearAttenuation * distance +
                    osg_LightSource[i].quadraticAttenuation * distance * distance);
                // for a spot light, the attenuation help form the cone:
                if (osg_LightSource[i].spotCutoff <= 90.0)
                {
                    vec3 D = normalize(osg_LightSource[i].spotDirection);
                    float clampedCos = max(0.0, dot(-L,D));
                    attenuation = clampedCos < osg_LightSource[i].spotCosCutoff ?
                        0.0 :
                        attenuation * pow(clampedCos, osg_LightSource[i].spotExponent);
                }
            }
            vec3 ambientReflection =
                attenuation *
                osg_LightSource[i].ambient.rgb;
            float NdotL = max(dot(N,L), 0.0); 
            vec3 diffuseReflection =
                attenuation
                * osg_LightSource[i].diffuse.rgb
                * NdotL;
                
            vec3 specularReflection = vec3(0.0);
            if (NdotL > 0.0)
            {
                vec3 H = reflect(-L,N); 
                float HdotV = max(dot(H,V), 0.0); 
                specularReflection =
                    attenuation
                    * osg_LightSource[i].specular.rgb
                    * surfaceSpecularity
                    * pow(HdotV, shine);
            }
            totalDiffuse += diffuseReflection;
            totalAmbient += ambientReflection;
            totalSpecular += specularReflection;
        }
    }
    vec3 lightColor =
        osg_FrontMaterial.emission.rgb +
        totalDiffuse * osg_FrontMaterial.diffuse.rgb +
        totalAmbient * osg_FrontMaterial.ambient.rgb;
    color.rgb =
        color.rgb * lightColor +
        totalSpecular;
}
#else
// nop
void oe_phong_fragment(inout vec4 color) { }
#endif
)";

		// Text
		Text = "Text.glsl";
		_sources[Text] = 
R"(out vec2 oe_Text_texCoord;
#pragma vp_entryPoint oe_Text_VS
#pragma vp_location   vertex_view
void oe_Text_VS(inout vec4 position)
{
    oe_Text_texCoord = gl_MultiTexCoord0.xy;
}
[break]
#ifdef GL_ES
    #extension GL_OES_standard_derivatives : enable
    #ifndef GL_OES_standard_derivatives
        #undef SIGNED_DISTANCE_FIELD
    #endif
#endif
#if !defined(GL_ES)
    #if __VERSION__>=400
        #define osg_TextureQueryLOD textureQueryLod
    #else
        #extension GL_ARB_texture_query_lod : enable
        #ifdef GL_ARB_texture_query_lod
            #define osg_TextureQueryLOD textureQueryLOD
        #endif
    #endif
#endif
#pragma vp_entryPoint oe_Text_FS
#pragma vp_location   fragment_coloring
#pragma import_defines(BACKDROP_COLOR, SHADOW, OUTLINE)
#pragma import_defines(SIGNED_DISTANCE_FIELD, TEXTURE_DIMENSION, GLYPH_DIMENSION)
#pragma import_defines(OSGTEXT_GLYPH_ALPHA_FORMAT_IS_RED)
#if __VERSION__>=130
    #define TEXTURE texture
    #define TEXTURELOD textureLod
#else
    #define TEXTURE texture2D
    #define TEXTURELOD texture2DLod
#endif
//#if !defined(GL_ES) && __VERSION__>=130
#ifdef OSGTEXT_GLYPH_ALPHA_FORMAT_IS_RED
    #define ALPHA r
    #define SDF g
#else
    #define ALPHA a
    #define SDF r
#endif
uniform sampler2D glyphTexture;
in vec2 oe_Text_texCoord;
vec4 vertexColor;
#ifndef TEXTURE_DIMENSION
    #define TEXTURE_DIMENSION float(1024.0)
#endif
#ifndef GLYPH_DIMENSION
    #define GLYPH_DIMENSION float(32.0)
#endif
#ifdef SIGNED_DISTANCE_FIELD
float distanceFromEdge(vec2 tc)
{
    float center_alpha = TEXTURELOD(glyphTexture, tc, 0.0).SDF;
    if (center_alpha==0.0) return -1.0;
    //float distance_scale = (1.0/4.0)*1.41;
    float distance_scale = (1.0/6.0)*1.41;
    //float distance_scale = (1.0/8.0)*1.41;
    return (center_alpha-0.5)*distance_scale;
}
vec4 distanceFieldColorSample(float edge_distance, float blend_width, float  blend_half_width)
{
#ifdef OUTLINE
    float outline_width = OUTLINE*0.5;
    if (edge_distance>blend_half_width)
    {
        return vertexColor;
    }
    else if (edge_distance>-blend_half_width)
    {
        return mix(vertexColor, vec4(BACKDROP_COLOR.rgb, BACKDROP_COLOR.a*vertexColor.a), smoothstep(0.0, 1.0, (blend_half_width-edge_distance)/(blend_width)));
    }
    else if (edge_distance>(blend_half_width-outline_width))
    {
        return vec4(BACKDROP_COLOR.rgb, BACKDROP_COLOR.a*vertexColor.a);
    }
    else if (edge_distance>-(outline_width+blend_half_width))
    {
        return vec4(BACKDROP_COLOR.rgb, vertexColor.a * ((blend_half_width+outline_width+edge_distance)/blend_width));
    }
    else
    {
        return vec4(0.0, 0.0, 0.0, 0.0);
    }
#else
    if (edge_distance>blend_half_width)
    {
        return vertexColor;
    }
    else if (edge_distance>-blend_half_width)
    {
        return vec4(vertexColor.rgb, vertexColor.a * smoothstep(1.0, 0.0, (blend_half_width-edge_distance)/(blend_width)));
    }
    else
    {
        return vec4(0.0, 0.0, 0.0, 0.0);
    }
#endif
}
vec4 textColor(vec2 src_texCoord)
{
    float sample_distance_scale = 0.75;
    vec2 dx = dFdx(src_texCoord)*sample_distance_scale;
    vec2 dy = dFdy(src_texCoord)*sample_distance_scale;
    float distance_across_pixel = length(dx+dy)*(TEXTURE_DIMENSION/GLYPH_DIMENSION);
    // compute the appropriate number of samples required to avoid aliasing.
    int maxNumSamplesAcrossSide = 4;
    int numSamplesX = int(TEXTURE_DIMENSION * length(dx));
    int numSamplesY = int(TEXTURE_DIMENSION * length(dy));
    if (numSamplesX<2) numSamplesX = 2;
    if (numSamplesY<2) numSamplesY = 2;
    if (numSamplesX>maxNumSamplesAcrossSide) numSamplesX = maxNumSamplesAcrossSide;
    if (numSamplesY>maxNumSamplesAcrossSide) numSamplesY = maxNumSamplesAcrossSide;
    vec2 delta_tx = dx/float(numSamplesX-1);
    vec2 delta_ty = dy/float(numSamplesY-1);
    float numSamples = float(numSamplesX)*float(numSamplesY);
    float scale = 1.0/numSamples;
    vec4 total_color = vec4(0.0,0.0,0.0,0.0);
    float blend_width = 1.5*distance_across_pixel/numSamples;
    float blend_half_width = blend_width*0.5;
    // check whether fragment is wholly within or outwith glyph body+outline
    float cd = distanceFromEdge(src_texCoord); // central distance (distance from center to edge)
    if (cd-blend_half_width>distance_across_pixel) return vertexColor; // pixel fully within glyph body
    #ifdef OUTLINE
    float outline_width = OUTLINE*0.5;
    if ((-cd-outline_width-blend_half_width)>distance_across_pixel) return vec4(0.0, 0.0, 0.0, 0.0); // pixel fully outside outline+glyph body
    #else
    if (-cd-blend_half_width>distance_across_pixel) return vec4(0.0, 0.0, 0.0, 0.0); // pixel fully outside glyph body
    #endif
    // use multi-sampling to provide high quality antialised fragments
    vec2 origin = src_texCoord - dx*0.5 - dy*0.5;
    for(;numSamplesY>0; --numSamplesY)
    {
        vec2 pos = origin;
        int numX = numSamplesX;
        for(;numX>0; --numX)
        {
            vec4 c = distanceFieldColorSample(distanceFromEdge(pos), blend_width, blend_half_width);
            total_color = total_color + c * c.a;
            pos += delta_tx;
        }
        origin += delta_ty;
    }
    total_color.rgb /= total_color.a;
    total_color.a *= scale;
    return total_color;
}
#else
vec4 textColor(vec2 src_texCoord)
{
#ifdef OUTLINE
    float alpha = TEXTURE(glyphTexture, src_texCoord).ALPHA;
    float delta_tc = 1.6*OUTLINE*GLYPH_DIMENSION/TEXTURE_DIMENSION;
    float outline_alpha = alpha;
    vec2 origin = src_texCoord-vec2(delta_tc*0.5, delta_tc*0.5);
    float numSamples = 3.0;
    delta_tc = delta_tc/(numSamples-1.0);
    float background_alpha = 1.0;
    for(float i=0.0; i<numSamples; ++i)
    {
        for(float j=0.0; j<numSamples; ++j)
        {
            float local_alpha = TEXTURE(glyphTexture, origin + vec2(i*delta_tc, j*delta_tc)).ALPHA;
            outline_alpha = max(outline_alpha, local_alpha);
            background_alpha = background_alpha * (1.0-local_alpha);
        }
    }
    #ifdef osg_TextureQueryLOD
        float mipmapLevel = osg_TextureQueryLOD(glyphTexture, src_texCoord).x;
        if (mipmapLevel<1.0)
        {
            outline_alpha = mix(1.0-background_alpha, outline_alpha, mipmapLevel/1.0);
        }
    #endif
    if (outline_alpha<alpha) outline_alpha = alpha;
    if (outline_alpha>1.0) outline_alpha = 1.0;
    if (outline_alpha==0.0) return vec4(0.0, 0.0, 0.0, 0.0); // outside glyph and outline
    vec4 color = mix(BACKDROP_COLOR, vertexColor, smoothstep(0.0, 1.0, alpha));
    color.a = vertexColor.a * smoothstep(0.0, 1.0, outline_alpha);
    return color;
#else
    float alpha = TEXTURE(glyphTexture, src_texCoord).ALPHA;
    if (alpha==0.0) vec4(0.0, 0.0, 0.0, 0.0);
    return vec4(vertexColor.rgb, vertexColor.a * alpha);
#endif
}
#endif
void oe_Text_FS(inout vec4 color)
{
    float originalAlpha = color.a;
    vertexColor = vec4(color.rgb, 1.0);
    if (oe_Text_texCoord.x<0.0 && oe_Text_texCoord.y<0.0)
    {
        return;
    }
#ifdef SHADOW
    float scale = -1.0*GLYPH_DIMENSION/TEXTURE_DIMENSION;
    vec2 delta_tc = SHADOW*scale;
    vec4 shadow_color = textColor(oe_Text_texCoord+delta_tc);
    shadow_color.rgb = BACKDROP_COLOR.rgb;
    vec4 glyph_color = textColor(oe_Text_texCoord);
    // lower the alpha_power value the greater the saturation, no need to be so aggressive with SDF than GREYSCALE
    #if SIGNED_DISTANCE_FIELD
        float alpha_power = 0.6;
    #else
        float alpha_power = 0.5;
    #endif
    // over saturate the alpha values to make sure the font and it's shadow are clear
    shadow_color.a = pow(shadow_color.a, alpha_power);
    glyph_color.a = pow(glyph_color.a, alpha_power);
    vec4 clr = mix(shadow_color, glyph_color, glyph_color.a);
#else
    vec4 clr = textColor(oe_Text_texCoord);
#endif
    if (clr.a==0.0) discard;
    color = clr;
    color.a *= originalAlpha;
}
)";

        TextLegacy = "Text_legacy.glsl";
        _sources[TextLegacy] = 
R"(#pragma vp_entryPoint oe_Text_VS
#pragma vp_location   vertex_view
out vec2 oe_Text_texcoord;
void oe_Text_VS(inout vec4 position)
{
    oe_Text_texcoord = gl_MultiTexCoord0.xy;
#if !defined(GL_ES) && __VERSION__<140
    gl_ClipVertex = gl_ModelViewMatrix * gl_Vertex;
#endif
}
[break]
#pragma vp_entryPoint oe_Text_FS
#pragma vp_location   fragment_coloring
uniform sampler2D glyphTexture;
in vec2 oe_Text_texcoord;
void oe_Text_FS(inout vec4 color)
{
    color.a *= texture(glyphTexture, oe_Text_texcoord).a;
}
)";
        
        ContourMap = "ContourMap.glsl";
        _sources[ContourMap] = 
R"(#pragma vp_function oe_contour_vertex, vertex_view
#pragma import_defines(OE_USE_GL4)
#pragma import_defines(OE_USE_GEOID)
out vec4 oe_layer_tilec;
#ifdef OE_USE_GEOID
  vec4 oe_tile_key;
  uniform sampler2D oe_contour_geoid;
  out float oe_contour_offset;
#endif
#ifdef OE_USE_GL4
#pragma include RexEngine.GL4.glsl
uint64_t oe_terrain_getElevationHandle();
vec2 oe_terrain_getElevationCoord(in vec2);
out vec2 oe_elev_coord;
flat out uint64_t oe_elev_tex;
void oe_contour_vertex(inout vec4 not_used)
{
    oe_elev_coord = oe_terrain_getElevationCoord(oe_layer_tilec.st);
    oe_elev_tex = oe_terrain_getElevationHandle();
#ifdef OE_USE_GEOID
    // calculate long and lat from [0..1] across the profile:
    vec2 r = (oe_tile_key.xy + oe_layer_tilec.xy) / exp2(oe_tile_key.z);
    vec2 uv = vec2(0.5 * r.x, r.y);
    oe_contour_offset = texture(oe_contour_geoid, uv).r;
#endif
}
#else
void oe_contour_vertex(inout vec4 not_used)
{
#ifdef OE_USE_GEOID
    // calculate long and lat from [0..1] across the profile:
    vec2 r = (oe_tile_key.xy + oe_layer_tilec.xy) / exp2(oe_tile_key.z);
    vec2 uv = vec2(0.5 * r.x, r.y);
    oe_contour_offset = texture(oe_contour_geoid, uv).r;
#endif
}
#endif
[break]
#pragma vp_function oe_contour_fragment, fragment_coloring
#pragma import_defines(OE_USE_GL4)
#pragma import_defines(OE_USE_GEOID)
uniform sampler1D oe_contour_xfer;
uniform float oe_contour_min;
uniform float oe_contour_range;
#ifdef OE_USE_GEOID
in float oe_contour_offset;
#endif
#ifdef OE_USE_GL4
flat in uint64_t oe_elev_tex;
in vec2 oe_elev_coord;
void oe_contour_fragment( inout vec4 color )
{
    if (oe_elev_tex > 0)
    {
        float offset = 0;
#ifdef OE_USE_GEOID
        offset = oe_contour_offset;
#endif
        float height = texture(sampler2D(oe_elev_tex), oe_elev_coord).r - offset;
        float height_normalized = (height - oe_contour_min) / oe_contour_range;
        float lookup = clamp(height_normalized, 0.0, 1.0);
        vec4 texel = texture(oe_contour_xfer, lookup);
        color.rgb = mix(color.rgb, texel.rgb, texel.a);
    }
}
#else // not OE_USE_GL4
// GL3 implementation:
float oe_terrain_getElevation(in vec2 uv);
in vec4 oe_layer_tilec;
void oe_contour_fragment(inout vec4 color)
{
    float offset = 0;
#ifdef OE_USE_GEOID
    offset = oe_contour_offset;
#endif
    float height = oe_terrain_getElevation(oe_layer_tilec.st) - offset;
    float height_normalized = (height - oe_contour_min) / oe_contour_range;
    float lookup = clamp(height_normalized, 0.0, 1.0);
    vec4 texel = texture(oe_contour_xfer, lookup);
    color.rgb = mix(color.rgb, texel.rgb, texel.a);
}
#endif // OE_USE_GL4
)";

        LogDepthBuffer = "LogDepthBuffer.glsl";
        _sources[LogDepthBuffer] = 
R"(#pragma vp_entryPoint oe_logDepth_vert
#pragma vp_location   vertex_clip
#pragma vp_order      0.99
out float oe_LogDepth_logz;
void oe_logDepth_vert(inout vec4 clip)
{
    if (gl_ProjectionMatrix[3][3] == 0) // perspective only
    {
        mat4 clip2view = inverse(gl_ProjectionMatrix);
        vec4 farPoint = clip2view * vec4(0,0,1,1);
        float FAR = -farPoint.z / farPoint.w;
        const float C = 0.001;
        float FC = 1.0 / log(FAR*C + 1);
        oe_LogDepth_logz = log(max(1e-6, clip.w*C + 1.0))*FC;
        clip.z = (2.0*oe_LogDepth_logz - 1.0)*clip.w;
    }
    else
    {
        oe_LogDepth_logz = -1.0;
    }
}
[break]
#pragma vp_entryPoint oe_logDepth_frag
#pragma vp_location   fragment_lighting
#pragma vp_order      0.99
in float oe_LogDepth_logz;
void oe_logDepth_frag(inout vec4 color)
{
    gl_FragDepth = oe_LogDepth_logz >= 0? oe_LogDepth_logz : gl_FragCoord.z;
}
)";

        LogDepthBuffer_VertOnly = "LogDepthBuffer.VertOnly.glsl";
        _sources[LogDepthBuffer_VertOnly] = 
R"(#pragma vp_entryPoint oe_logDepth_vert
#pragma vp_location   vertex_clip
#pragma vp_order      0.99
void oe_logDepth_vert(inout vec4 clip)
{
    if (gl_ProjectionMatrix[3][3] == 0.0) // perspective only
    {
        mat4 clip2view = inverse(gl_ProjectionMatrix);
        vec4 farPoint = clip2view * vec4(0,0,1,1);
        float FAR = -farPoint.z / farPoint.w;
        float FC = 2.0 / log2(FAR + 1);
        clip.z = (log2(max(1e-6, clip.w+1.0))*FC - 1.0) * clip.w;
    }
}
)";

        GeodeticGraticule = "GeodeticGraticule.glsl";
        _sources[GeodeticGraticule] = 
R"(#pragma vp_entryPoint oe_GeodeticGraticule_vertex
#pragma vp_location   vertex_view
#pragma vp_order      0.5
#pragma import_defines(OE_SHOW_GRID_LINES)
#ifndef OE_SHOW_GRID_LINES
#define OE_SHOW_GRID_LINES 1
#endif
out vec4 oe_layer_tilec;
out vec2 oe_GeodeticGraticule_coord;
vec4 oe_tile_key;
void oe_GeodeticGraticule_vertex(inout vec4 vertex)
{
#if OE_SHOW_GRID_LINES
    // calculate long and lat from [0..1] across the profile:
    vec2 r = (oe_tile_key.xy + oe_layer_tilec.xy)/exp2(oe_tile_key.z);
    oe_GeodeticGraticule_coord = vec2(0.5*r.x, r.y);
#endif
}
[break]
#pragma vp_entryPoint oe_GeodeticGraticule_fragment
#pragma vp_location   fragment_lighting
#pragma vp_order      1.1
#pragma import_defines(OE_SHOW_GRID_LINES)
#ifndef OE_SHOW_GRID_LINES
#define OE_SHOW_GRID_LINES 1
#endif
uniform float oe_GeodeticGraticule_lineWidth;
uniform float oe_GeodeticGraticule_resolution;
uniform vec4  oe_GeodeticGraticule_color;
uniform mat4 osg_ViewMatrixInverse;
in vec2 oe_GeodeticGraticule_coord;
void oe_GeodeticGraticule_fragment(inout vec4 color)
{
#if OE_SHOW_GRID_LINES
    // double the effective res for longitude since it has twice the span
    vec2 gr = vec2(0.5*oe_GeodeticGraticule_resolution, oe_GeodeticGraticule_resolution);
    vec2 distanceToLine = mod(oe_GeodeticGraticule_coord, gr);
    vec2 dx = abs(dFdx(oe_GeodeticGraticule_coord));
    vec2 dy = abs(dFdy(oe_GeodeticGraticule_coord));
    vec2 dF = vec2(max(dx.s, dy.s), max(dx.t, dy.t)) * oe_GeodeticGraticule_lineWidth;
    if ( any(lessThan(distanceToLine, dF)))
    {
        // calculate some anti-aliasing
        vec2 f = distanceToLine/dF;
        float antialias = 1.0 - 2.0*abs(0.5 - min(f.x,f.y));
        // Fade out the lines as you get closer to the ground.
        vec3 eye = osg_ViewMatrixInverse[3].xyz;
        float hae = length(eye) - 6378137.0;
        float maxHAE = 2000.0;
        float alpha = clamp(hae / maxHAE, 0.0, 1.0) * antialias;
        if (antialias < 0.)
          alpha = 0.;
        color.rgb = mix(color.rgb, oe_GeodeticGraticule_color.rgb, oe_GeodeticGraticule_color.a * alpha);
    }
#endif
}
)";

        ShadowCaster = "ShadowCaster.glsl";
        _sources[ShadowCaster] = 
R"(#pragma vp_name       Shadowing Vertex Shader
#pragma vp_entryPoint oe_shadow_vertex
#pragma vp_location   vertex_view
#pragma vp_order      last
uniform mat4 oe_shadow_matrix[$OE_SHADOW_NUM_SLICES];
uniform float oe_shadow_maxrange;
out vec4 oe_shadow_coord[$OE_SHADOW_NUM_SLICES];
out float oe_shadow_rf;
void oe_shadow_vertex(inout vec4 VertexVIEW)
{
    for(int i=0; i < $OE_SHADOW_NUM_SLICES; ++i)
    {
        oe_shadow_coord[i] = oe_shadow_matrix[i] * VertexVIEW;
    }
    oe_shadow_rf = clamp(-VertexVIEW.z / oe_shadow_maxrange, 0.0, 1.0);
    oe_shadow_rf = oe_shadow_rf * oe_shadow_rf * oe_shadow_rf;
}
[break]
#pragma vp_name       Shadowing Fragment Shader
#pragma vp_entryPoint oe_shadow_fragment
#pragma vp_location   fragment_lighting
#pragma vp_order      0.7
#pragma import_defines(OE_LIGHTING)
#pragma import_defines(OE_NUM_LIGHTS)
uniform sampler2DArray oe_shadow_map;
uniform float          oe_shadow_color;
uniform float          oe_shadow_blur;
in vec3 vp_Normal; // stage global
in vec4 oe_shadow_coord[$OE_SHADOW_NUM_SLICES];
in float oe_shadow_rf;
// fragment stage global PBR parameters.
struct OE_PBR { float displacement, roughness, ao, metal; } oe_pbr;
// Parameters of each light:
struct osg_LightSourceParameters 
{   
   vec4 ambient;
   vec4 diffuse;
   vec4 specular;
   vec4 position;
   vec3 spotDirection;
   float spotExponent;
   float spotCutoff;
   float spotCosCutoff;
   float constantAttenuation;
   float linearAttenuation;
   float quadraticAttenuation;
   bool enabled;
};  
uniform osg_LightSourceParameters osg_LightSource[OE_NUM_LIGHTS];
#define OE_SHADOW_NUM_SAMPLES 16
const vec2 oe_shadow_samples[16] = vec2[](
    vec2( -0.942016, -0.399062 ), vec2( 0.945586, -0.768907 ), vec2( -0.094184, -0.929389 ), vec2( 0.344959, 0.293878 ),
    vec2( -0.915886, 0.457714 ), vec2( -0.815442, -0.879125 ), vec2( -0.382775, 0.276768 ), vec2( 0.974844, 0.756484 ),
    vec2( 0.443233, -0.975116 ), vec2( 0.53743, -0.473734 ), vec2( -0.264969, -0.41893 ), vec2( 0.791975, 0.190909 ),
    vec2( -0.241888, 0.997065 ), vec2( -0.8141, 0.914376 ), vec2( 0.199841, 0.786414 ), vec2( 0.143832, -0.141008 )
);
float oe_shadow_rand(vec2 co)
{
   return fract(sin(dot(co.xy, vec2(12.9898,78.233))) * 43758.5453);
}
vec2 oe_shadow_rot(vec2 p, float a)
{
    vec2 sincos = vec2(sin(a), cos(a));
    return vec2(dot(p, vec2(sincos.y, -sincos.x)), dot(p, sincos.xy));
}
// slow PCF sampling.
float oe_shadow_multisample(in vec3 c, in float refvalue, in float blur)
{
    float shadowed = 0.0;
    float randomAngle = 6.283185 * oe_shadow_rand(c.xy);
    for(int i=0; i<OE_SHADOW_NUM_SAMPLES; ++i)
    {
        vec2 off = oe_shadow_rot(oe_shadow_samples[i], randomAngle);
        vec3 pc = vec3(c.xy + off*blur, c.z);
        float depth = texture(oe_shadow_map, pc).r;
        
        if (depth < 1.0 && depth < refvalue )
        {
           shadowed += 1.0;
        }
    }
    return 1.0-(shadowed/OE_SHADOW_NUM_SAMPLES);
}
void oe_shadow_fragment(inout vec4 color)
{
    float alpha = color.a;
    float out_of_shadow = 1.0;
    // pre-pixel biasing to reduce moire/acne
    const float b0 = 0.001;
    const float b1 = 0.01;
    vec3 L = normalize(osg_LightSource[0].position.xyz);
    vec3 N = normalize(vp_Normal);
    float costheta = clamp(dot(L,N), 0.0, 1.0);
    float bias = b0*tan(acos(costheta));
    float depth;
    // loop over the slices:
    for(int i=0; i<$OE_SHADOW_NUM_SLICES && out_of_shadow > 0.0; ++i)
    {
        vec4 c = oe_shadow_coord[i];
        vec3 coord = vec3(c.x, c.y, float(i));
        if ( oe_shadow_blur > 0.0 )
        {
            out_of_shadow = min(out_of_shadow, oe_shadow_multisample(coord, c.z-bias, oe_shadow_blur));
        }
        else
        {
            depth = texture(oe_shadow_map, coord).r;
            if ( depth < 1.0 && depth < c.z-bias )
                out_of_shadow = 0.0;
        }
    }
    oe_pbr.roughness = clamp(mix(oe_pbr.roughness*1.5, oe_pbr.roughness, out_of_shadow), 0, 1);
    color.rgb = mix(color.rgb * oe_shadow_color, color.rgb, out_of_shadow);
}
)";

        SimpleOceanLayer = "SimpleOceanLayer.glsl";
        _sources[SimpleOceanLayer] = 
R"(#pragma vp_entryPoint oe_ocean_VS
#pragma vp_location vertex_view
#pragma import_defines(OE_OCEAN_TEXTURE)
#pragma import_defines(OE_OCEAN_TEXTURE_LOD)
#pragma import_defines(OE_OCEAN_MASK_MATRIX)
uniform float oe_ocean_seaLevel;
#ifdef OE_OCEAN_TEXTURE
out vec2 oe_ocean_texCoord;
vec2 oe_terrain_scaleCoordsToRefLOD(in vec2 tc, in float refLOD); // from SDK
#endif
#ifdef OE_OCEAN_MASK_MATRIX
out vec2 oe_ocean_maskCoord;
uniform mat4 OE_OCEAN_MASK_MATRIX ;
#endif
vec4 oe_layer_tilec;
vec3 oe_UpVectorView;   // stage global
void oe_ocean_VS(inout vec4 vertexView)
{
    // move the surface to the new sea level:
    vertexView.xyz += oe_UpVectorView * oe_ocean_seaLevel;
#ifdef OE_OCEAN_TEXTURE
    oe_ocean_texCoord = oe_terrain_scaleCoordsToRefLOD(oe_layer_tilec.st, OE_OCEAN_TEXTURE_LOD);
#endif
    // if masking, calculate the mask coordinates
#ifdef OE_OCEAN_MASK_MATRIX
    oe_ocean_maskCoord = (OE_OCEAN_MASK_MATRIX * oe_layer_tilec).st;
#endif
}
[break]
#pragma vp_entryPoint oe_ocean_FS
#pragma vp_location fragment_coloring
#pragma import_defines(OE_OCEAN_TEXTURE)
#pragma import_defines(OE_OCEAN_MASK)
#pragma import_defines(OE_OCEAN_USE_BATHYMETRY)
float oe_terrain_getElevation();
//in float oe_layer_opacity; // from VisibleLayer
uniform vec4 oe_ocean_color;
uniform float oe_ocean_seaLevel;
#ifdef OE_OCEAN_TEXTURE
in vec2 oe_ocean_texCoord;
uniform sampler2D OE_OCEAN_TEXTURE ;
#endif
#ifdef OE_OCEAN_MASK
in vec2 oe_ocean_maskCoord;
uniform sampler2D OE_OCEAN_MASK ;
#endif
// fragment stage global PBR parameters.
struct OE_PBR { float displacement, roughness, ao, metal; } oe_pbr;
// remaps a value from [vmin..vmax] to [0..1] clamped
float oe_ocean_remap(float val, float vmin, float vmax, float r0, float r1)
{
    float vr = (clamp(val, vmin, vmax)-vmin)/(vmax-vmin);
    return r0 + vr * (r1-r0);
}
// entry point.
void oe_ocean_FS(inout vec4 color)
{
    float alpha = 1.0;
#ifdef OE_OCEAN_USE_BATHYMETRY
    const float lowF = -100.0;
    const float hiF = -10.0;
    float elevation = oe_terrain_getElevation();
    alpha = oe_ocean_remap(elevation, oe_ocean_seaLevel+lowF, oe_ocean_seaLevel+hiF, 1.0, 0.0);
#endif
#ifdef OE_OCEAN_MASK
    float mask = texture(OE_OCEAN_MASK, oe_ocean_maskCoord).a;
    alpha *= mask;
#endif
    color = vec4(oe_ocean_color.rgb, alpha*oe_ocean_color.a);
    
    oe_pbr.roughness = 0.3;
    oe_pbr.ao = 1.0;
#ifdef OE_OCEAN_TEXTURE
    color *= texture(OE_OCEAN_TEXTURE, oe_ocean_texCoord);
#endif
}
)";
        
        RTTPicker = "RTTPicker.glsl";
        _sources[RTTPicker] = 
R"(#pragma vp_entryPoint oe_pick_encodeObjectID
#pragma vp_location   vertex_clip
//#define USE_BITWISE_MATH
        
// Vertex stage global containing the Object ID; set in ObjectIndex shader.
uint oe_index_objectid;
// output encoded oid to fragment shader
flat out vec4 oe_pick_encoded_objectid;
// whether color already contains oid (written by another RTT camera)
flat out int oe_pick_color_contains_objectid;
void oe_pick_encodeObjectID(inout vec4 vertex)
{
    // the color will contain an encoded object ID for draped features.
    oe_pick_color_contains_objectid = (oe_index_objectid == 1u) ? 1 : 0;
    if ( oe_pick_color_contains_objectid == 0 )
    {
        // encode the objectID as a vec4 (color)
#ifdef USE_BITWISE_MATH
        uint u0 = (oe_index_objectid & 0xff000000u) >> 24u;
        uint u1 = (oe_index_objectid & 0x00ff0000u) >> 16u;
        uint u2 = (oe_index_objectid & 0x0000ff00u) >> 8u;
        uint u3 = (oe_index_objectid & 0x000000ffu);
#else
        uint u0 = (oe_index_objectid / 16777216u);
        uint u1 = (oe_index_objectid / 65536u) - (u0 * 256u);
        uint u2 = (oe_index_objectid / 256u) - (u1 * 256u) - (u0 * 65536u);
        uint u3 = (oe_index_objectid) - (u2 * 256u) - (u1 * 65536u) - (u0 * 16777216u);
#endif
        oe_pick_encoded_objectid = vec4(float(u0), float(u1), float(u2), float(u3)) / 255.0;
    }
}
[break]
#pragma vp_entryPoint oe_pick_renderEncodedObjectID
#pragma vp_location   fragment_output
#pragma vp_order      last
flat in vec4 oe_pick_encoded_objectid;
flat in int oe_pick_color_contains_objectid;
        
out vec4 fragColor;
void oe_pick_renderEncodedObjectID(inout vec4 color)
{
    if ( oe_pick_color_contains_objectid == 1 )
        fragColor = color;
    else
        fragColor = oe_pick_encoded_objectid;
}
)";
        
        WindComputer = "WindLayer.CS.glsl";
        _sources[WindComputer] = 
R"(#version 430
// local work matrix
layout(local_size_x=1, local_size_y=1, local_size_z=1) in;
// output image binding
layout(binding=0, rgba8) uniform image3D oe_wind_tex;
#define SAFETY_BAILOUT 4096 // prevent GPU lockup in case of internal error
// https://tinyurl.com/y6see7zz
#define MAX_WIND_SPEED 50.0 // meters per second
// keep me vec4-aligned
struct WindData {
    vec4 position;
    vec3 direction;
    float speed;
};
layout(binding=0, std430) readonly buffer BufferData {
    WindData wind[];
};
// matrix that transforms from texture space back to camera view space
uniform mat4 oe_wind_texToViewMatrix;
uniform float osg_FrameTime;
const float oe_wind_sway = 0.25;
void main()
{
    // coords is texture space:
    vec4 pixelNDC = vec4(
        float(gl_WorkGroupID.x) / float(gl_NumWorkGroups.x - 1),
        float(gl_WorkGroupID.y) / float(gl_NumWorkGroups.y - 1),
        float(gl_WorkGroupID.z) / float(gl_NumWorkGroups.z - 1),
        1.0);
    vec3 totalDirection = vec3(0);
    int i;
    for (i = 0; wind[i].speed >= 0.0 && i < SAFETY_BAILOUT; ++i)
    {
        if (wind[i].position.w == 1)
        {
            // point wind:
            vec4 windView = wind[i].position;
            vec4 pixelView = oe_wind_texToViewMatrix * pixelNDC;
            pixelView.xyz /= pixelView.w;
            vec3 dir = pixelView.xyz - windView.xyz;
            // speed attenuation
            float speed = wind[i].speed;
            speed = max(speed - length(dir), 0.0); // linearly interpolates for low-res textures
            totalDirection += normalize(dir) * speed;
        }
        else
        {
            // directional wind:
            totalDirection += wind[i].direction * wind[i].speed;
        }
    }
    float totalSpeed = length(totalDirection);
    vec4 pixel;
    // RGB holds normalized wind direction
    pixel.rgb = 0.5*(normalize(totalDirection)+1.0);
    // A holds normalized wind speed
    pixel.a = min(totalSpeed, MAX_WIND_SPEED) / MAX_WIND_SPEED;
    if (i == SAFETY_BAILOUT)
        pixel = vec4(1, 0, 0, 1);
    imageStore(oe_wind_tex, ivec3(gl_WorkGroupID), pixel);
}
)";

        PBR = "PBR.glsl";
        _sources[PBR] = 
R"(#pragma vp_function oe_pbr_init, fragment, first
// fragment stage global PBR parameters.
struct OE_PBR { float displacement, roughness, ao, metal; } oe_pbr;
void oe_pbr_init(inout vec4 ignore_me)
{
    oe_pbr.displacement = 0.5;
    oe_pbr.roughness = 1.0;
    oe_pbr.ao = 1.0;
    oe_pbr.metal = 0.0;
}
)";

		MetadataNode = "MetadataNode.glsl";
		_sources[MetadataNode] = 
R"(#pragma vp_entryPoint oe_metadata_vert
#pragma vp_location   vertex_view
#version 430
uniform int  oe_index_uniform = 0;
in uint      oe_index_attr;
uint         oe_index_objectid;         // Stage global containing the Object ID.
struct Instance
{
    uint objectID;
    uint visible;
};
layout(binding = 0, std430) buffer Instances {
    Instance instances[];
};
flat out uint out_visible;
void oe_metadata_vert(inout vec4 VertexVIEW)
{
    int index = -1;
    if (oe_index_attr > 0)
    {
        index = int(oe_index_attr-1);
    }
    else if (oe_index_uniform > 0)
    {
        index = int(oe_index_uniform - 1);
    }
    // This is a big ole hack but if the oe_index_objectid is already set (via instancing) then assume it is the index
    else if (oe_index_objectid > 0)
    {
        index = int(oe_index_objectid - 1);
    }
    if (index >= 0)
    {
        uint objectID = instances[index].objectID;
        oe_index_objectid = objectID;
        out_visible = instances[index].visible;
    }
    else
    {
        oe_index_objectid = 0;
        out_visible = 1;
    }
}
[break]
#pragma vp_entryPoint oe_metadata_frag
#pragma vp_location   fragment_coloring
#pragma vp_order      first
#version 430
flat in uint out_visible;
void oe_metadata_frag(inout vec4 color)
{
    if (out_visible == 0) discard;
}
)";
    }
} }
