/*-------------------------------------------------------------------------
 *
 * pg_foreign_data_wrapper_d.h
 *    Macro definitions for pg_foreign_data_wrapper
 *
 * Portions Copyright (c) 1996-2023, PostgreSQL Global Development Group
 * Portions Copyright (c) 1994, Regents of the University of California
 *
 * NOTES
 *  ******************************
 *  *** DO NOT EDIT THIS FILE! ***
 *  ******************************
 *
 *  It has been GENERATED by src/backend/catalog/genbki.pl
 *
 *-------------------------------------------------------------------------
 */
#ifndef PG_FOREIGN_DATA_WRAPPER_D_H
#define PG_FOREIGN_DATA_WRAPPER_D_H

#define ForeignDataWrapperRelationId 2328
#define ForeignDataWrapperOidIndexId 112
#define ForeignDataWrapperNameIndexId 548

#define Anum_pg_foreign_data_wrapper_oid 1
#define Anum_pg_foreign_data_wrapper_fdwname 2
#define Anum_pg_foreign_data_wrapper_fdwowner 3
#define Anum_pg_foreign_data_wrapper_fdwhandler 4
#define Anum_pg_foreign_data_wrapper_fdwvalidator 5
#define Anum_pg_foreign_data_wrapper_fdwacl 6
#define Anum_pg_foreign_data_wrapper_fdwoptions 7

#define Natts_pg_foreign_data_wrapper 7


#endif							/* PG_FOREIGN_DATA_WRAPPER_D_H */
