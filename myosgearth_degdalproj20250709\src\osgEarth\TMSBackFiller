/* osgEarth
* Copyright 2025 Pelican Mapping
* MIT License
*/
#ifndef OSGEARTHUTIL_TMS_BACKFILLER_H
#define OSGEARTHUTIL_TMS_BACKFILLER_H

#include <osgEarth/Common>
#include <osgEarth/Profile>
#include <osgEarth/TMS>

namespace osgEarth { namespace Contrib
{
    using namespace osgEarth;

    /**
     * Post processing utility for disk based TMS (Tile Map Service) repositories that takes data from a given level and "backfills" lower
     * levels of data by mosaciing and resampling the higher lod data.  This process is useful when processing web datasets that switch from one
     * dataset to another at distinct lods which looks fine when viewed in a 2D slippy map but look incorrect when viewed at an angle in 3D
     * in views that contain neighboring lods.
     */
    class OSGEARTH_EXPORT TMSBackFiller
    {
    public:
        TMSBackFiller();

        /**
        * Whether to dump out progress messages 
        * default = false
        */
        void setVerbose( bool value ) { _verbose = value; }
        bool getVerbose() const { return _verbose; }

        /**
        * The level to backfill up to
        */
        void setMinLevel( unsigned int value ) { _minLevel = value; }
        unsigned int getMinLevel() const { return _minLevel; }

        /**
        * The level to start backfilling from.  All tiles up to the min level will be regenerated using tiles from this level of detail.
        */
        void setMaxLevel( unsigned int value ) { _maxLevel = value; }
        unsigned int getMaxLevel() const { return _maxLevel; }

        /**
        * The bounds to backfill within
        */
        const Bounds& getBounds() const { return _bounds;}
        void setBounds( Bounds& bounds) { _bounds = bounds;}

        /**
         * Processes the given TMS file with the given options
         */
        void process( const std::string& tms, osgDB::Options* options );                        

    private:

        void processKey( const TileKey& key );

        std::string getFilename( const TileKey& key );
        
        osg::Image* readTile( const TileKey& key );

        void writeTile( const TileKey& key, osg::Image* image );
        
        osg::ref_ptr< TMS::TileMap > _tileMap;

        unsigned int _minLevel;
        unsigned int _maxLevel;
        bool _verbose;
        std::string _tmsPath;
        Bounds _bounds;
        osg::ref_ptr< osgDB::Options > _options;
    };

} } // namespace osgEarth::Tools

#endif // OSGEARTHUTIL_TMS_PACKAGER_H
