/*-------------------------------------------------------------------------
 *
 * pg_db_role_setting_d.h
 *    Macro definitions for pg_db_role_setting
 *
 * Portions Copyright (c) 1996-2023, PostgreSQL Global Development Group
 * Portions Copyright (c) 1994, Regents of the University of California
 *
 * NOTES
 *  ******************************
 *  *** DO NOT EDIT THIS FILE! ***
 *  ******************************
 *
 *  It has been GENERATED by src/backend/catalog/genbki.pl
 *
 *-------------------------------------------------------------------------
 */
#ifndef PG_DB_ROLE_SETTING_D_H
#define PG_DB_ROLE_SETTING_D_H

#define DbRoleSettingRelationId 2964
#define PgDbRoleSettingToastTable 2966
#define PgDbRoleSettingToastIndex 2967
#define DbRoleSettingDatidRolidIndexId 2965

#define Anum_pg_db_role_setting_setdatabase 1
#define Anum_pg_db_role_setting_setrole 2
#define Anum_pg_db_role_setting_setconfig 3

#define Natts_pg_db_role_setting 3


#endif							/* PG_DB_ROLE_SETTING_D_H */
