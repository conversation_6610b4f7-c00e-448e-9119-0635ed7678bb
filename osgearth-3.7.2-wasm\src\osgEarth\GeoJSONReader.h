/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/Common>
#include <osgEarth/Feature>
#include <osgEarth/Geometry>
#include <string>
#include <vector>

namespace osgEarth
{
    /**
     * Simple GeoJSON reader that doesn't depend on GDAL.
     * Supports basic GeoJSON reading for Point, LineString, and Polygon types.
     */
    class OSGEARTH_EXPORT GeoJSONReader
    {
    public:
        GeoJSONReader();
        ~GeoJSONReader();

        /**
         * Read features from a GeoJSON file
         */
        bool readFeatures(const std::string& filename, std::vector<osg::ref_ptr<Feature>>& features);

        /**
         * Read features from GeoJSON string content
         */
        bool readFeaturesFromString(const std::string& content, std::vector<osg::ref_ptr<Feature>>& features);

        /**
         * Parse a single GeoJSON feature
         */
        osg::ref_ptr<Feature> parseFeature(const std::string& featureJson);

        /**
         * Parse geometry from GeoJSON
         */
        Geometry* parseGeometry(const std::string& geometryJson);

        /**
         * Get the last error message
         */
        const std::string& getLastError() const { return _lastError; }

    private:
        // JSON parsing helpers
        std::string extractValue(const std::string& json, const std::string& key);
        std::vector<std::string> extractArray(const std::string& json, const std::string& key);
        std::string extractObject(const std::string& json, const std::string& key);
        
        // Geometry parsing
        Point* parsePoint(const std::string& coordinatesJson);
        LineString* parseLineString(const std::string& coordinatesJson);
        Polygon* parsePolygon(const std::string& coordinatesJson);
        MultiGeometry* parseMultiPoint(const std::string& coordinatesJson);
        MultiGeometry* parseMultiLineString(const std::string& coordinatesJson);
        MultiGeometry* parseMultiPolygon(const std::string& coordinatesJson);
        
        // Coordinate parsing
        std::vector<osg::Vec3d> parseCoordinateArray(const std::string& coordsJson);
        std::vector<std::vector<osg::Vec3d>> parseCoordinateArrayArray(const std::string& coordsJson);
        osg::Vec3d parseCoordinate(const std::string& coordJson);
        
        // Attribute parsing
        void parseProperties(const std::string& propertiesJson, AttributeTable& attributes);
        
        // Utility functions
        std::string trim(const std::string& str);
        std::string removeQuotes(const std::string& str);
        bool isNumber(const std::string& str);
        double parseNumber(const std::string& str);
        std::string unescapeString(const std::string& str);
        
        // JSON structure validation
        bool isValidGeoJSON(const std::string& json);
        bool isFeatureCollection(const std::string& json);
        bool isFeature(const std::string& json);
        bool isGeometry(const std::string& json);
        
        // Error handling
        void setError(const std::string& error);
        void clearError();

    private:
        std::string _lastError;
        
        // Parsing state
        size_t _currentPos;
        std::string _currentJson;
        
        // Constants
        static const std::vector<std::string> GEOMETRY_TYPES;
        static const std::vector<std::string> FEATURE_TYPES;
    };

    /**
     * Utility functions for GeoJSON processing
     */
    namespace GeoJSONUtils
    {
        /**
         * Validate GeoJSON string
         */
        OSGEARTH_EXPORT bool validateGeoJSON(const std::string& json, std::string& error);
        
        /**
         * Extract features from FeatureCollection
         */
        OSGEARTH_EXPORT std::vector<std::string> extractFeatures(const std::string& featureCollectionJson);
        
        /**
         * Get geometry type from GeoJSON geometry object
         */
        OSGEARTH_EXPORT std::string getGeometryType(const std::string& geometryJson);
        
        /**
         * Check if string is valid JSON (basic check)
         */
        OSGEARTH_EXPORT bool isValidJSON(const std::string& json);
        
        /**
         * Find matching brace/bracket
         */
        OSGEARTH_EXPORT size_t findMatchingBrace(const std::string& json, size_t startPos, char openChar, char closeChar);
        
        /**
         * Extract JSON object/array at position
         */
        OSGEARTH_EXPORT std::string extractJSONObject(const std::string& json, size_t startPos);
        
        /**
         * Parse JSON array of strings
         */
        OSGEARTH_EXPORT std::vector<std::string> parseStringArray(const std::string& arrayJson);
        
        /**
         * Parse JSON array of numbers
         */
        OSGEARTH_EXPORT std::vector<double> parseNumberArray(const std::string& arrayJson);
        
        /**
         * Escape special characters in JSON string
         */
        OSGEARTH_EXPORT std::string escapeJSONString(const std::string& str);
        
        /**
         * Unescape JSON string
         */
        OSGEARTH_EXPORT std::string unescapeJSONString(const std::string& str);
    }

} // namespace osgEarth
