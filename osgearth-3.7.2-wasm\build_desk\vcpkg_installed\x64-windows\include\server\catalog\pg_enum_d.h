/*-------------------------------------------------------------------------
 *
 * pg_enum_d.h
 *    Macro definitions for pg_enum
 *
 * Portions Copyright (c) 1996-2023, PostgreSQL Global Development Group
 * Portions Copyright (c) 1994, Regents of the University of California
 *
 * NOTES
 *  ******************************
 *  *** DO NOT EDIT THIS FILE! ***
 *  ******************************
 *
 *  It has been GENERATED by src/backend/catalog/genbki.pl
 *
 *-------------------------------------------------------------------------
 */
#ifndef PG_ENUM_D_H
#define PG_ENUM_D_H

#define EnumRelationId 3501
#define EnumOidIndexId 3502
#define EnumTypIdLabelIndexId 3503
#define EnumTypIdSortOrderIndexId 3534

#define Anum_pg_enum_oid 1
#define Anum_pg_enum_enumtypid 2
#define Anum_pg_enum_enumsortorder 3
#define Anum_pg_enum_enumlabel 4

#define Natts_pg_enum 4


#endif							/* PG_ENUM_D_H */
