/* osgEarth
* Copyright 2025 Pelican Mapping
* MIT License
*/

#ifndef OSGEARTH_VIDEO_LAYER_H
#define OSGEARTH_VIDEO_LAYER_H 1

#include <osgEarth/Common>
#include <osgEarth/ImageLayer>
#include <osg/Texture2D>

namespace osgEarth
{
    /**
     * A layer that displays a video texture on the earth.
     */
    class OSGEARTH_EXPORT VideoLayer : public osgEarth::ImageLayer
    {
    public: // serialization
        class OSGEARTH_EXPORT Options : public ImageLayer::Options {
        public:
            META_LayerOptions(osgEarth, Options, ImageLayer::Options);
            OE_OPTION(URI, url);
            virtual Config getConfig() const;
        private:
            void fromConfig( const Config& conf );
        };

    public:
        META_Layer(osgEarth, VideoLayer, Options, ImageLayer, Video);

        //! Location of the source.
        void setURL(const URI& value);
        const URI& getURL() const;

        //! Access the texture 
        osg::Texture2D* getTexture() const { return _texture.get(); }

    public: // ImageLayer:

        virtual void init();

        virtual Status openImplementation();

        virtual TextureWindow createTexture(const TileKey& key, ProgressCallback* progress) const;

    protected:

        osg::ref_ptr< osg::Texture2D > _texture;
    };
} // namespace osgEarth

OSGEARTH_SPECIALIZE_CONFIG(osgEarth::VideoLayer::Options);

#endif // OSGEARTH_VIDEO_LAYER_H
