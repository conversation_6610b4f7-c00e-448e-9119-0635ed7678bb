/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/Common>
#include <osgEarth/VisibleLayer>
#include <osgEarth/FeatureSource>
#include <osgEarth/TiledModelLayer>
#include <osgEarth/LayerReference>
#include <osgEarth/TileMesher>

namespace osgEarth
{
    /**
     * Provides data to the terrain engine to use for customizing the surface mesh.
     * This layer type is used to constrain the terrain surface based on either
     * a collection of features OR tiled model data.
     */
    class OSGEARTH_EXPORT TerrainConstraintLayer : public VisibleLayer
    {
    public: // serialization
        struct OSGEARTH_EXPORT Options : public VisibleLayer::Options
        {
            META_LayerOptions(osgEarth, Options, VisibleLayer::Options);
            OE_OPTION_LAYER(FeatureSource, featureSource);
            OE_OPTION_LAYER(TiledModelLayer, model);
            OE_OPTION_VECTOR(ConfigOptions, filters);
            OE_OPTION(bool, removeInterior, false);
            OE_OPTION(bool, removeExterior, false);
            OE_OPTION(bool, hasElevation, false);
            OE_OPTION(unsigned, minLevel, 8);
            Config getConfig() const override;
            void fromConfig(const Config& conf);
        };

    public:
        META_Layer(osgEarth, TerrainConstraintLayer, Options, VisibleLayer, TerrainConstraint);

        //! Feature data to use for creating terrain constraints
        //! Use this OR setModelLayer, but not both.
        void setFeatureSource(FeatureSource* features);
        FeatureSource* getFeatureSource() const;

        //! Model data to use for creating terrain constraints
        //! Use this OR setFeatureSource(), but not both.
        void setModelLayer(TiledModelLayer* model);
        TiledModelLayer* getModelLayer() const;

        //! Whether the terrain should remove geometry that is interior to polygons
        bool getRemoveInterior() const { return options().removeInterior().get(); }
        void setRemoveInterior(bool value);

        //! Whether the terrain should remove geometry that is interior to polygons
        bool getRemoveExterior() const { return options().removeExterior().get(); }
        void setRemoveExterior(bool value);

        //! Whether the terrain should perserve the Z value in the feature data
        //! as static elevation
        bool getHasElevation() const { return options().hasElevation().get(); }
        void setHasElevation(bool value);

        //! Minimum LOD at which to apply this constraint
        unsigned getMinLevel() const { return options().minLevel().get(); }
        void setMinLevel(unsigned value);

        //! Geospatial extent of this layer's data, if known.
        const GeoExtent& getExtent() const override;

        //! Feature filters to use when querying the feature source, if applicable.
        FeatureFilterChain& getFilters() { return _filterChain; }

        //! Return a constraint object for a given tile key.
        //! @param key The tile key
        //! @param context The filter context, or nullptr if not applicable
        //! @param progress A progress callback to check for user cancellation
        //! @return A constraint object
        MeshConstraint getConstraint(
            const TileKey& key,
            FilterContext* context,
            ProgressCallback* progress) const;

        Callback<void(const TileKey&, MeshConstraint&, FilterContext*, ProgressCallback*)> constraintCallback;

    protected:

        void init() override;

        Status openImplementation() override;

        void addedToMap(const Map*) override;

        void removedFromMap(const Map*) override;

    private:

        void create();

        FeatureFilterChain _filterChain;

        void getFeatureConstraint(
            const TileKey& key,
            FilterContext* context,
            MeshConstraint& constraint,
            ProgressCallback* progress) const;

        void getModelConstraint(
            const TileKey& key,
            MeshConstraint& constraint,
            ProgressCallback* progress) const;
    };

    /**
    * Object you can use to query the map for constraints that
    * intersect a tile key.
    */
    class OSGEARTH_EXPORT TerrainConstraintQuery
    {
    public:
        std::vector<osg::ref_ptr<TerrainConstraintLayer>> layers;
        osg::ref_ptr<Session> session;

        TerrainConstraintQuery(const Map* map = {}) {
            if (map) setup(map);
        }

        //! Sets the query to use all constraint layers in the map
        void setup(const Map* map);

        //! Fetches all constraints intersecting the given tilekey
        bool getConstraints(
            const TileKey& key,
            MeshConstraints& output,
            ProgressCallback* progress) const;
    };

} // namespace osgEarth

OSGEARTH_SPECIALIZE_CONFIG(osgEarth::TerrainConstraintLayer::Options);


